import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  base: './',
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
    minify: 'terser',
    // Increase chunk size warning limit to 1MB
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        // Improved manual chunks for better code splitting
        manualChunks: (id) => {
          // Vendor chunks - separate large libraries
          if (id.includes('node_modules')) {
            // React ecosystem
            if (id.includes('react') || id.includes('react-dom')) {
              return 'react-vendor';
            }
            // Excel/Office libraries
            if (id.includes('xlsx') || id.includes('office')) {
              return 'xlsx-vendor';
            }
            // Other large vendor libraries
            if (id.includes('lodash') || id.includes('moment') || id.includes('date-fns')) {
              return 'utils-vendor';
            }
            // All other vendor libraries
            return 'vendor';
          }

          // Application chunks - split by functionality
          if (id.includes('src/')) {
            // Reports and analytics
            if (id.includes('Report') || id.includes('profitCalculator') || id.includes('AdvancedReports')) {
              return 'reports';
            }
            // Language and translations
            if (id.includes('Language') || id.includes('translations') || id.includes('algerianLocale')) {
              return 'i18n';
            }
            // Thermal printing
            if (id.includes('ThermalPrinter') || id.includes('thermal')) {
              return 'thermal';
            }
            // Data management
            if (id.includes('dataSync') || id.includes('activation')) {
              return 'data';
            }
            // Sound and media
            if (id.includes('Sound') || id.includes('audio') || id.includes('media')) {
              return 'media';
            }
          }
        },
        // Optimize chunk file names
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId ? chunkInfo.facadeModuleId.split('/').pop() : 'chunk';
          return `js/[name]-[hash].js`;
        },
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name.split('.');
          const ext = info[info.length - 1];
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `images/[name]-[hash][extname]`;
          }
          if (/css/i.test(ext)) {
            return `css/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        }
      }
    },
    copyPublicDir: true,
    // Optimize build performance
    target: 'esnext',
    // Enable tree shaking
    treeshake: {
      moduleSideEffects: false
    }
  },
  server: {
    port: 3000,
    host: true
  },
  publicDir: 'public',
  // Optimize dependencies
  optimizeDeps: {
    include: ['react', 'react-dom', 'xlsx'],
    exclude: ['@vite/client', '@vite/env']
  }
})
