import{r as e,j as t,b as n,R as a}from"./react-vendor-03c8a839.js";import{u as s,w as i,r}from"./xlsx-vendor-3e8bf635.js";import{a as o,d as l}from"./data-9da80832.js";import{A as c,g as d,c as m,a as h,b as p,d as u}from"./reports-68e0d1aa.js";import{u as x,L as v,a as g}from"./i18n-7eb290f1.js";import{t as b}from"./thermal-8161055e.js";import"./vendor-38106ca9.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const f=({onActivationSuccess:n})=>{const{t:a,currentLanguage:s,getCurrentLanguageConfig:i}=x(),[r,l]=e.useState(""),[c,d]=e.useState(!1),[m,h]=e.useState(""),[p,u]=e.useState(""),[v,g]=e.useState(!1),[b,f]=e.useState(""),j=i();e.useEffect((()=>{const e=o.generateMachineFingerprint();u(e)}),[]),e.useEffect((()=>{const e=e=>{const t=b+e.key.toLowerCase();f(t),(t.includes("reset")||t.includes("test"))&&(g(!0),f("")),setTimeout((()=>{f("")}),3e3)};return document.addEventListener("keypress",e),()=>document.removeEventListener("keypress",e)}),[b]);const y=async()=>{if(r.trim()){d(!0),h("");try{const e=o.activateProgram(r.trim());if(e.success){if("TRIAL"===e.data.type){e.data.trialDays,new Date(e.data.expiryDate).toLocaleDateString("ar-DZ");h("")}n(e.data)}else{let t=e.error;t.includes("البرنامج مفعل بالفعل على هذا الجهاز")?t=a("programAlreadyActivated"):t.includes("تنسيق كود التفعيل غير صحيح")&&(t=a("invalidActivationCodeFormat")),h(t)}}catch(e){h(a("unexpectedActivationError"))}finally{d(!1)}}else h(a("pleaseEnterActivationCode"))};return t.jsxs("div",{className:"activation-overlay",dir:j.direction,children:[t.jsxs("div",{className:"activation-dialog",children:[t.jsx("div",{className:"activation-header",children:t.jsxs("div",{className:"activation-logo",children:[t.jsx("h1",{children:"🏪 iCalDZ"}),t.jsx("p",{children:a("systemDescription","نظام المحاسبة المتكامل")})]})}),t.jsxs("div",{className:"activation-content",children:[t.jsx("h2",{children:a("activationTitle")}),t.jsx("p",{className:"activation-description",children:a("activationDescription")}),t.jsxs("div",{className:"activation-form",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{htmlFor:"activationCode",children:a("activationCodeLabel")}),t.jsx("input",{type:"text",id:"activationCode",value:r,onChange:e=>{let t=e.target.value.toUpperCase();t=t.replace(/[^A-Z0-9-]/g,""),l(t),h("")},onKeyPress:e=>{"Enter"===e.key&&y()},placeholder:a("activationCodePlaceholder"),className:"activation-input "+(m?"error":""),disabled:c,maxLength:50})]}),m&&t.jsx("div",{className:"activation-error",children:t.jsxs("span",{children:["⚠️ ",m]})}),t.jsx("button",{onClick:y,disabled:c||!r.trim(),className:"activation-button",children:c?t.jsxs(t.Fragment,{children:[t.jsx("span",{className:"loading-spinner",children:"⏳"}),a("activating")]}):t.jsxs(t.Fragment,{children:["🔑 ",a("activateProgram")]})}),v&&t.jsxs("button",{onClick:()=>{window.confirm(a("confirmResetActivation"))&&(o.resetActivation(),h(""),l(""),alert(a("resetActivationSuccess")),window.location.reload())},className:"reset-button",title:a("resetActivationTooltip"),children:["🔄 ",a("resetActivation")]})]}),t.jsxs("div",{className:"machine-info",children:[t.jsx("h3",{children:a("deviceInfo")}),t.jsxs("div",{className:"machine-id",children:[t.jsxs("span",{children:[a("deviceId")," "]}),t.jsx("code",{children:p})]}),t.jsxs("p",{className:"machine-note",children:["💡 ",a("deviceNote")]})]}),t.jsx("div",{className:"activation-footer",children:t.jsxs("div",{className:"contact-info",children:[t.jsx("h4",{children:a("getActivationCode")}),t.jsxs("p",{children:["📞 ",a("phone")," +213 551 93 05 89"]}),t.jsxs("p",{children:["📧 ",a("email")," <EMAIL>"]}),t.jsxs("p",{children:["🌐 ",a("website")," www.icodedz.com"]})]})})]})]}),t.jsx("style",{jsx:!0,children:'\n        .activation-overlay {\n          position: fixed;\n          top: 0;\n          left: 0;\n          right: 0;\n          bottom: 0;\n          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          z-index: 10000;\n          font-family: \'Cairo\', sans-serif;\n        }\n\n        .activation-dialog {\n          background: white;\n          border-radius: 20px;\n          box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n          max-width: 500px;\n          width: 90%;\n          max-height: 90vh;\n          overflow-y: auto;\n          animation: slideIn 0.5s ease-out;\n        }\n\n        @keyframes slideIn {\n          from {\n            opacity: 0;\n            transform: translateY(-50px) scale(0.9);\n          }\n          to {\n            opacity: 1;\n            transform: translateY(0) scale(1);\n          }\n        }\n\n        .activation-header {\n          background: linear-gradient(135deg, #16a085 0%, #2c3e50 100%);\n          color: white;\n          padding: 30px;\n          text-align: center;\n          border-radius: 20px 20px 0 0;\n        }\n\n        .activation-logo h1 {\n          margin: 0;\n          font-size: 2.5rem;\n          font-weight: bold;\n        }\n\n        .activation-logo p {\n          margin: 5px 0 0 0;\n          opacity: 0.9;\n          font-size: 1.1rem;\n        }\n\n        .activation-content {\n          padding: 30px;\n        }\n\n        .activation-content h2 {\n          text-align: center;\n          color: #2c3e50;\n          margin: 0 0 15px 0;\n          font-size: 1.8rem;\n        }\n\n        .activation-description {\n          text-align: center;\n          color: #666;\n          margin-bottom: 30px;\n          line-height: 1.6;\n        }\n\n        /* Language-specific text alignment */\n        [dir="rtl"] .activation-content h2,\n        [dir="rtl"] .activation-description,\n        [dir="rtl"] .contact-info h4,\n        [dir="rtl"] .contact-info p {\n          text-align: right;\n        }\n\n        [dir="ltr"] .activation-content h2,\n        [dir="ltr"] .activation-description,\n        [dir="ltr"] .contact-info h4,\n        [dir="ltr"] .contact-info p {\n          text-align: left;\n        }\n\n        /* Center alignment for titles in all languages */\n        .activation-content h2,\n        .contact-info h4 {\n          text-align: center !important;\n        }\n\n        .form-group {\n          margin-bottom: 20px;\n        }\n\n        .form-group label {\n          display: block;\n          margin-bottom: 8px;\n          font-weight: 600;\n          color: #2c3e50;\n        }\n\n        /* Language-specific label alignment */\n        [dir="rtl"] .form-group label {\n          text-align: right;\n        }\n\n        [dir="ltr"] .form-group label {\n          text-align: left;\n        }\n\n        .activation-input {\n          width: 100%;\n          padding: 15px;\n          border: 2px solid #e0e0e0;\n          border-radius: 10px;\n          font-size: 14px;\n          font-family: \'Courier New\', monospace;\n          text-align: center;\n          transition: all 0.3s ease;\n          box-sizing: border-box;\n        }\n\n        .activation-input:focus {\n          outline: none;\n          border-color: #16a085;\n          box-shadow: 0 0 0 3px rgba(22, 160, 133, 0.1);\n        }\n\n        .activation-input.error {\n          border-color: #e74c3c;\n          box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);\n        }\n\n        .activation-error {\n          background: #ffe6e6;\n          color: #c0392b;\n          padding: 12px;\n          border-radius: 8px;\n          margin: 15px 0;\n          text-align: center;\n          border: 1px solid #f5b7b1;\n        }\n\n        .activation-button {\n          width: 100%;\n          padding: 15px;\n          background: linear-gradient(135deg, #16a085 0%, #2c3e50 100%);\n          color: white;\n          border: none;\n          border-radius: 10px;\n          font-size: 16px;\n          font-weight: 600;\n          cursor: pointer;\n          transition: all 0.3s ease;\n          display: flex;\n          align-items: center;\n          justify-content: center;\n          gap: 10px;\n        }\n\n        .activation-button:hover:not(:disabled) {\n          transform: translateY(-2px);\n          box-shadow: 0 8px 25px rgba(22, 160, 133, 0.3);\n        }\n\n        .activation-button:disabled {\n          opacity: 0.6;\n          cursor: not-allowed;\n          transform: none;\n        }\n\n        .reset-button {\n          width: 100%;\n          padding: 12px;\n          background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);\n          color: white;\n          border: none;\n          border-radius: 8px;\n          font-size: 14px;\n          font-weight: 500;\n          cursor: pointer;\n          transition: all 0.3s ease;\n          margin-top: 10px;\n          opacity: 0.8;\n        }\n\n        .reset-button:hover {\n          opacity: 1;\n          transform: translateY(-1px);\n          box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);\n        }\n\n        .loading-spinner {\n          animation: spin 1s linear infinite;\n        }\n\n        @keyframes spin {\n          from { transform: rotate(0deg); }\n          to { transform: rotate(360deg); }\n        }\n\n        .machine-info {\n          background: #f8f9fa;\n          padding: 20px;\n          border-radius: 10px;\n          margin: 25px 0;\n          border: 1px solid #e9ecef;\n        }\n\n        .machine-info h3 {\n          margin: 0 0 15px 0;\n          color: #2c3e50;\n          font-size: 1.1rem;\n        }\n\n        /* Language-specific machine info alignment */\n        [dir="rtl"] .machine-info h3,\n        [dir="rtl"] .machine-id,\n        [dir="rtl"] .machine-note {\n          text-align: right;\n        }\n\n        [dir="ltr"] .machine-info h3,\n        [dir="ltr"] .machine-id,\n        [dir="ltr"] .machine-note {\n          text-align: left;\n        }\n\n        .machine-id {\n          background: white;\n          padding: 10px;\n          border-radius: 5px;\n          border: 1px solid #ddd;\n          margin-bottom: 10px;\n        }\n\n        .machine-id code {\n          font-family: \'Courier New\', monospace;\n          color: #16a085;\n          font-weight: bold;\n        }\n\n        .machine-note {\n          font-size: 0.9rem;\n          color: #666;\n          margin: 0;\n          font-style: italic;\n        }\n\n        .activation-footer {\n          border-top: 1px solid #e9ecef;\n          padding-top: 20px;\n          margin-top: 20px;\n        }\n\n        .contact-info h4 {\n          margin: 0 0 15px 0;\n          color: #2c3e50;\n          text-align: center;\n        }\n\n        .contact-info p {\n          margin: 8px 0;\n          color: #666;\n          text-align: center;\n          font-size: 0.9rem;\n        }\n      '})]})};const j=new class{constructor(){this.sounds={},this.isEnabled=!0,this.volume=.7,this.isInitialized=!1,this.soundDefinitions={newInvoice:{file:"/sounds/new-invoice.mp3",description:"🛒 فاتورة جديدة",fallback:"beep"},saveInvoice:{file:"/sounds/save-invoice.mp3",description:"💾 حفظ الفاتورة",fallback:"beep"},printInvoice:{file:"/sounds/print.mp3",description:"🖨️ طباعة الفاتورة",fallback:"beep"},closeWindow:{file:"/sounds/close.mp3",description:"❌ إغلاق النافذة",fallback:"click"},success:{file:"/sounds/success.mp3",description:"✅ نجاح العملية",fallback:"beep"},warning:{file:"/sounds/warning.mp3",description:"⚠️ تحذير",fallback:"beep"},error:{file:"/sounds/error.mp3",description:"❌ خطأ",fallback:"beep"},addProduct:{file:"/sounds/add-product.mp3",description:"➕ إضافة منتج",fallback:"click"},deleteProduct:{file:"/sounds/delete.mp3",description:"🗑️ حذف منتج",fallback:"click"},quickSearch:{file:"/sounds/search.mp3",description:"🔍 البحث السريع",fallback:"click"},refresh:{file:"/sounds/refresh.mp3",description:"🔄 تحديث البيانات",fallback:"click"},shortcutActivated:{file:"/sounds/shortcut.mp3",description:"⌨️ اختصار لوحة المفاتيح",fallback:"click"}},this.init()}async init(){try{this.loadPreferences(),await this.preloadSounds(["success","error","newInvoice","saveInvoice"]),this.isInitialized=!0,console.log("🔊 Sound Manager initialized successfully")}catch(e){console.warn("🔇 Sound Manager initialization failed, using fallback mode:",e),this.isInitialized=!0}}async preloadSounds(e){const t=e.map((e=>this.loadSound(e)));await Promise.allSettled(t)}async loadSound(e){if(this.sounds[e])return this.sounds[e];const t=this.soundDefinitions[e];if(!t)return console.warn(`🔇 Sound definition not found: ${e}`),null;try{const n=new Audio(t.file);return n.volume=this.volume,n.preload="auto",await new Promise(((e,t)=>{n.addEventListener("canplaythrough",e,{once:!0}),n.addEventListener("error",t,{once:!0}),n.load()})),this.sounds[e]=n,n}catch(n){return console.warn(`🔇 Failed to load sound ${e}:`,n),null}}async play(e,t={}){if(!this.isEnabled)return;const{volume:n=this.volume,force:a=!1,showNotification:s=!0}=t;try{let t=this.sounds[e];if(t||a||(t=await this.loadSound(e)),t)return t.volume=Math.min(Math.max(n,0),1),t.currentTime=0,await t.play(),s&&this.showSoundNotification(e),!0}catch(i){console.warn(`🔇 Failed to play sound ${e}:`,i)}return this.playFallbackSound(e),!1}playFallbackSound(e){const t=this.soundDefinitions[e];if(t)try{"beep"===t.fallback?this.createBeep(800,150):"click"===t.fallback&&this.createBeep(1200,50)}catch(n){console.warn("🔇 Fallback sound failed:",n)}}createBeep(e=800,t=150){try{const n=new(window.AudioContext||window.webkitAudioContext),a=n.createOscillator(),s=n.createGain();a.connect(s),s.connect(n.destination),a.frequency.value=e,a.type="sine",s.gain.setValueAtTime(0,n.currentTime),s.gain.linearRampToValueAtTime(.3*this.volume,n.currentTime+.01),s.gain.exponentialRampToValueAtTime(.001,n.currentTime+t/1e3),a.start(n.currentTime),a.stop(n.currentTime+t/1e3)}catch(n){console.warn("🔇 Web Audio API beep failed:",n)}}showSoundNotification(e){const t=this.soundDefinitions[e];if(!t)return;const n=document.createElement("div");if(n.className="sound-notification",n.textContent=t.description,n.style.cssText="\n      position: fixed;\n      top: 20px;\n      left: 20px;\n      background: rgba(22, 160, 133, 0.9);\n      color: white;\n      padding: 8px 16px;\n      border-radius: 20px;\n      font-size: 12px;\n      font-weight: 600;\n      z-index: 10000;\n      animation: soundNotificationSlide 2s ease-out forwards;\n      pointer-events: none;\n    ",!document.getElementById("sound-notification-styles")){const e=document.createElement("style");e.id="sound-notification-styles",e.textContent="\n        @keyframes soundNotificationSlide {\n          0% { transform: translateX(-100%); opacity: 0; }\n          20% { transform: translateX(0); opacity: 1; }\n          80% { transform: translateX(0); opacity: 1; }\n          100% { transform: translateX(-100%); opacity: 0; }\n        }\n      ",document.head.appendChild(e)}document.body.appendChild(n),setTimeout((()=>{n.parentNode&&n.parentNode.removeChild(n)}),2e3)}loadPreferences(){try{const e=localStorage.getItem("icaldz-sound-preferences");if(e){const t=JSON.parse(e);this.isEnabled=!1!==t.isEnabled,this.volume=t.volume||.7}}catch(e){console.warn("🔇 Failed to load sound preferences:",e)}}savePreferences(){try{const e={isEnabled:this.isEnabled,volume:this.volume};localStorage.setItem("icaldz-sound-preferences",JSON.stringify(e))}catch(e){console.warn("🔇 Failed to save sound preferences:",e)}}toggle(){return this.isEnabled=!this.isEnabled,this.savePreferences(),this.isEnabled}setVolume(e){this.volume=Math.min(Math.max(e,0),1),Object.values(this.sounds).forEach((e=>{e&&(e.volume=this.volume)})),this.savePreferences()}getStatus(){return{isEnabled:this.isEnabled,volume:this.volume,isInitialized:this.isInitialized,loadedSounds:Object.keys(this.sounds).length}}};function y(){const{t:n,isLanguageSelected:a,setIsLanguageSelected:v,currentLanguage:y,getCurrentLanguageConfig:N}=x();e.useEffect((()=>{window.barcodeShortcutManager||(window.barcodeShortcutManager={isEnabled:!0,isBarcodeActive:!1,checkBarcodeInput:e=>{if(!e||"INPUT"!==e.tagName)return!1;const t=["barcode-input","scanner-input","dashboard-scanner","sales-scanner","edit-scanner","product-barcode"];if(e.className){const n=e.className.toLowerCase();if(t.some((e=>n.includes(e))))return!0}if(e.id){const n=e.id.toLowerCase();if(t.some((e=>n.includes(e))))return!0}if(e.placeholder){const t=e.placeholder.toLowerCase();if(t.includes("barcode")||t.includes("باركود")||t.includes("scanner")||t.includes("مسح"))return!0}return!1},setShortcutsEnabled:e=>{window.KeyboardShortcuts&&window.KeyboardShortcuts.setEnabled(e)}});const e=e=>{window.barcodeShortcutManager&&window.barcodeShortcutManager.checkBarcodeInput(e.target)&&(window.barcodeShortcutManager.isBarcodeActive=!0,window.barcodeShortcutManager.setShortcutsEnabled(!1))},t=e=>{window.barcodeShortcutManager&&setTimeout((()=>{const e=document.activeElement;window.barcodeShortcutManager.checkBarcodeInput(e)||(window.barcodeShortcutManager.isBarcodeActive=!1,window.barcodeShortcutManager.setShortcutsEnabled(!0))}),100)};return document.addEventListener("focusin",e,!0),document.addEventListener("focusout",t,!0),()=>{document.removeEventListener("focusin",e,!0),document.removeEventListener("focusout",t,!0)}}),[]);const $=e=>{if(!e)return"";let t=String(e).replace(/[\r\n\t\f\v]/g,"").replace(/[^\w\d\-_.]/g,"").replace(/\s+/g,"").trim();return t.length>50&&(t=t.substring(0,50)),t},w=e=>{if(!e||"string"!=typeof e)return!1;const t=e.trim();if(t.length<3||t.length>50)return!1;return/^[a-zA-Z0-9\-_\.]{3,50}$/.test(t)},[S,k]=e.useState({lastActivity:Date.now(),totalScans:0,errors:0,isHealthy:!0});e.useEffect((()=>{const e=setInterval((()=>{const e=Date.now();e-S.lastActivity>36e5&&(k({lastActivity:e,totalScans:0,errors:0,isHealthy:!0}),C()),e%6e5<3e5&&C(),S.errors>10&&(k({lastActivity:e,totalScans:0,errors:0,isHealthy:!0}),C(),window.barcodeShortcutManager&&(window.barcodeShortcutManager.isBarcodeActive=!1,window.barcodeShortcutManager.setShortcutsEnabled(!0)))}),3e5);return()=>clearInterval(e)}),[S.lastActivity,S.errors]),e.useEffect((()=>{let e,t;return e=setInterval((()=>{if(Be.length>10&&Ue((e=>e.slice(-5))),Ce.length>100){const e=Ce.slice(-100);Ie(e),localStorage.setItem("icaldz-invoices",JSON.stringify(e))}if(window.gc&&"function"==typeof window.gc)try{window.gc()}catch(e){}"storage"in navigator&&"estimate"in navigator.storage&&navigator.storage.estimate().then((e=>{if((e.usage/1024/1024).toFixed(2),e.usage>52428800){Object.keys(localStorage).forEach((e=>{(e.startsWith("icaldz-temp-")||e.startsWith("icaldz-cache-"))&&localStorage.removeItem(e)}))}})).catch((e=>{}))}),18e5),t=setInterval((()=>{if("memory"in performance){const e=performance.memory;(e.usedJSHeapSize/1024/1024).toFixed(2),(e.totalJSHeapSize/1024/1024).toFixed(2),e.usedJSHeapSize>.8*e.totalJSHeapSize&&(C(),Jt())}document.querySelectorAll("*").length}),3e5),()=>{e&&clearInterval(e),t&&clearInterval(t)}}),[Be.length,Ce.length]);const C=()=>{if(window.dashboardScannerTimeout&&(clearTimeout(window.dashboardScannerTimeout),window.dashboardScannerTimeout=null),window.salesScannerTimeout&&(clearTimeout(window.salesScannerTimeout),window.salesScannerTimeout=null),window.salesScannerValidationTimeout&&(clearTimeout(window.salesScannerValidationTimeout),window.salesScannerValidationTimeout=null),window.editScannerValidationTimeout&&(clearTimeout(window.editScannerValidationTimeout),window.editScannerValidationTimeout=null),window.productScannerTimeout&&(clearTimeout(window.productScannerTimeout),window.productScannerTimeout=null),window.gc&&"function"==typeof window.gc)try{window.gc()}catch(e){}((e=!0,t="scan")=>{k((n=>{const a=e?n.errors:n.errors+1,s=a<10;return!s&&n.isHealthy||s&&n.isHealthy,{lastActivity:Date.now(),totalScans:"cleanup"===t?n.totalScans:n.totalScans+1,errors:a,isHealthy:s,systemStatus:s?"active":"degraded",lastContext:t}}))})(!0,"cleanup")},[I,D]=e.useState(!1),[T,P]=e.useState(!1),[M,E]=e.useState(!0),[A,R]=e.useState(!0),[F,O]=e.useState(!0),[L,z]=e.useState(!0),[B,U]=e.useState(!1);e.useEffect((()=>{(()=>{const e=o.checkActivationStatus();D(e.activated),P(!0),e.activated&&"TRIAL"===e.type&&Zt(`🕐 فترة تجربة: ${e.daysLeft} أيام متبقية`,"info",5e3)})()}),[]),e.useEffect((()=>{I&&(async()=>{try{await j.init();const e=j.getStatus();E(e.isEnabled);const t=localStorage.getItem("printerEnabled");null!==t&&O(JSON.parse(t));const n=localStorage.getItem("shortcutsEnabled");null!==n&&R(JSON.parse(n));const a=localStorage.getItem("notificationsEnabled");null!==a&&z(JSON.parse(a)),setTimeout((()=>{j.play("success",{showNotification:!1})}),1e3)}catch(e){}})()}),[I]),e.useEffect((()=>{const e=()=>{const e=window.pageYOffset||document.documentElement.scrollTop;U(e>300)};return window.addEventListener("scroll",e),e(),()=>window.removeEventListener("scroll",e)}),[]);const q=e=>{v(!0)},V={isLoggedIn:"true"===localStorage.getItem("icaldz-login-status"),page:localStorage.getItem("icaldz-current-page")||"dashboard"},[Z,G]=e.useState(V.isLoggedIn?V.page:"login"),[J,_]=e.useState(V.isLoggedIn),[W,Y]=e.useState(null),[Q,H]=e.useState(!1),[K,X]=e.useState(!1),[ee,te]=e.useState(!1);e.useState(!1),e.useState(null),e.useState(!1),e.useState(null);const[ne,ae]=e.useState(!1),[se,ie]=e.useState(null),[re,oe]=e.useState(!1),[le,ce]=e.useState(null),[de,me]=e.useState(!1),[he,pe]=e.useState(null),[ue,xe]=e.useState(0),[ve,ge]=e.useState("نقداً");e.useState("");const[be,fe]=e.useState(""),[je,ye]=e.useState(""),[Ne,$e]=e.useState(!1),[we,Se]=e.useState({username:"",password:""}),ke=(e,t="dashboard")=>{localStorage.setItem("icaldz-login-status",e.toString()),localStorage.setItem("icaldz-current-page",t)},[Ce,Ie]=e.useState((()=>{const e=localStorage.getItem("icaldz-invoices");return e?JSON.parse(e):[]})),[De,Te]=e.useState(""),Pe=e.useRef(null),[Me,Ee]=e.useState(null),[Ae,Re]=e.useState(null),[Fe,Oe]=e.useState(null),[Le,ze]=e.useState(null),[Be,Ue]=e.useState([]);e.useEffect((()=>{K&&Za.current&&setTimeout((()=>{Za.current.focus()}),100)}),[K]),e.useEffect((()=>{"dashboard"===Z&&Pe.current&&setTimeout((()=>{Pe.current.focus()}),200)}),[Z]);const[qe,Ve]=e.useState(!1),[Ze,Ge]=e.useState(null),[Je,_e]=e.useState(null),[We,Ye]=e.useState({invoiceNumber:"",date:"",supplierId:"",supplierName:"",paymentMethod:"نقداً",items:[],total:0,discount:0,tax:0,finalTotal:0}),[Qe,He]=e.useState([]),[Ke,Xe]=e.useState([]),et=e=>{localStorage.setItem("icaldz-expenses",JSON.stringify(e)),nt(e)},[tt,nt]=e.useState((()=>{const e=localStorage.getItem("icaldz-expenses");return e?JSON.parse(e):[]})),[at,st]=e.useState(!1),[it,rt]=e.useState({id:"",date:(new Date).toISOString().split("T")[0],category:"رواتب",amount:0,description:"",paymentMethod:"نقداً"}),[ot,lt]=e.useState(!1),[ct,dt]=e.useState(!1),[mt,ht]=e.useState(!1),[pt,ut]=e.useState(null),[xt,vt]=e.useState({id:"",name:"",phone:"",address:"",email:""}),[gt,bt]=e.useState(!1),[ft,jt]=e.useState([]),[yt,Nt]=e.useState([]),[$t,wt]=e.useState([]),[St,kt]=e.useState([]),[Ct,It]=e.useState([]),[Dt,Tt]=e.useState([]);e.useState(""),e.useState(""),e.useState(""),e.useState(""),e.useState(""),e.useState(""),e.useState(""),e.useState(""),e.useState(""),e.useState("");const[Pt,Mt]=e.useState({storeName:"iCalDZ Store",storePhone:"+*********** 456",storeAddress:"الجزائر العاصمة، الجزائر",storeLogo:"",taxRate:19,currency:"DZD"}),[Et,At]=e.useState([]),[Rt,Ft]=e.useState(!1),[Ot,Lt]=e.useState({role:"مدير",name:"المدير"}),[zt,Bt]=e.useState({id:"",name:"",username:"",password:"",phone:"",email:"",role:"seller",isActive:!0}),Ut=e=>{const t=(e||0).toLocaleString("en-US",{minimumFractionDigits:2,maximumFractionDigits:2}),n="ar"===y?"د.ج":"DZD";return"ar"===y?`${n} ${t}`:`${t} ${n}`},qt=e=>e&&"GUEST"!==e&&"زبون عابر"!==e&&"Client de passage"!==e&&"Walk-in Customer"!==e?e:n("walkInCustomer","زبون عابر"),Vt=e=>{if(!e||"مورد عام"===e||"Fournisseur général"===e||"General Supplier"===e)return n("generalSupplier","مورد عام");const t={"شركة التوريدات العامة":{fr:"Société de Fournitures Générales",en:"General Supply Company",ar:"شركة التوريدات العامة"},"مؤسسة الإمداد":{fr:"Institution d'Approvisionnement",en:"Supply Institution",ar:"مؤسسة الإمداد"},"شركة السلع الأولية":{fr:"Société de Matières Premières",en:"Raw Materials Company",ar:"شركة السلع الأولية"},"مؤسسة التجهيزات":{fr:"Institution d'Équipements",en:"Equipment Institution",ar:"مؤسسة التجهيزات"},"شركة المعدات الحديثة":{fr:"Société d'Équipements Modernes",en:"Modern Equipment Company",ar:"شركة المعدات الحديثة"},"مورد رقم واحد":{fr:"Fournisseur Numéro Un",en:"Supplier Number One",ar:"مورد رقم واحد"},"مورد رقم اثنين":{fr:"Fournisseur Numéro Deux",en:"Supplier Number Two",ar:"مورد رقم اثنين"},"مورد رقم ثلاثة":{fr:"Fournisseur Numéro Trois",en:"Supplier Number Three",ar:"مورد رقم ثلاثة"},"مورد محلي":{fr:"Fournisseur Local",en:"Local Supplier",ar:"مورد محلي"},"مورد خارجي":{fr:"Fournisseur Externe",en:"External Supplier",ar:"مورد خارجي"}};return t[e]&&t[e][y]||e},Zt=(e,t="success",n=3e3)=>{if(!L)return;const a=Date.now()+Math.random(),s={id:a,message:e,type:t,duration:n};Ue((e=>[...e,s])),setTimeout((()=>{Gt(a)}),n)},Gt=e=>{Ue((t=>t.filter((t=>t.id!==e))))},Jt=()=>{Ue([])},_t=(e,t)=>{((e,t)=>{const n={finalTotal:e,invoiceNumber:t,timestamp:(new Date).toLocaleTimeString(),date:(new Date).toLocaleDateString(),savedAt:(new Date).toISOString()};try{localStorage.setItem("icaldz-last-invoice-lcd",JSON.stringify(n)),ze(n)}catch(a){}})(e,t),Fe&&clearTimeout(Fe),Re({finalTotal:e,invoiceNumber:t,timestamp:(new Date).toLocaleTimeString()});const n=setTimeout((()=>{Re(null),Oe(null)}),6e3);Oe(n)},Wt=(e,t)=>{switch(e){case"sales":ft.length===t.length?jt([]):jt(t.map((e=>e.id)));break;case"purchases":yt.length===t.length?Nt([]):Nt(t.map((e=>e.id)));break;case"customers":$t.length===t.length?wt([]):wt(t.map((e=>e.id)));break;case"products":St.length===t.length?kt([]):kt(t.map((e=>e.id)));break;case"suppliers":Ct.length===t.length?It([]):It(t.map((e=>e.id)));break;case"sellers":Dt.length===t.length?Tt([]):Tt(t.map((e=>e.id)))}},Yt=(e,t)=>{switch(e){case"sales":jt((e=>e.includes(t)?e.filter((e=>e!==t)):[...e,t]));break;case"purchases":Nt((e=>e.includes(t)?e.filter((e=>e!==t)):[...e,t]));break;case"customers":wt((e=>e.includes(t)?e.filter((e=>e!==t)):[...e,t]));break;case"products":kt((e=>e.includes(t)?e.filter((e=>e!==t)):[...e,t]));break;case"suppliers":It((e=>e.includes(t)?e.filter((e=>e!==t)):[...e,t]));break;case"sellers":Tt((e=>e.includes(t)?e.filter((e=>e!==t)):[...e,t]))}},Qt=e=>{if("مدير"===Ot.role||"admin"===Ot.role)switch(e){case"sales":if(0===ft.length)return void Zt(`⚠️ ${n("pleaseSelectItems","يرجى تحديد عناصر للحذف")}`,"warning",3e3);if(confirm(`${n("confirmDeleteSelected","هل أنت متأكد من حذف العناصر المحددة؟")} (${ft.length})\n\n${n("allProductsWillBeRestored","سيتم إرجاع جميع المنتجات إلى المخزون.")}`)){Ce.filter((e=>ft.includes(e.id))).forEach((e=>{e.items.forEach((e=>{const t=Dn.findIndex((t=>t.id===e.productId));if(-1!==t){const n=[...Dn];n[t].stock+=e.quantity,Tn(n),localStorage.setItem("icaldz-products",JSON.stringify(n))}}))}));(e=>{try{localStorage.setItem("icaldz-invoices",JSON.stringify(e)),Ie(e)}catch(t){Zt("❌ خطأ في حفظ الفاتورة","error",3e3)}})(Ce.filter((e=>!ft.includes(e.id)))),jt([]),Zt(`✅ ${n("selectedItemsDeleted","تم حذف العناصر المحددة")} (${ft.length}) ${n("andStockRestored","وإرجاع المنتجات للمخزون")}`,"success",3e3)}break;case"purchases":if(0===yt.length)return void Zt(`⚠️ ${n("pleaseSelectItems","يرجى تحديد عناصر للحذف")}`,"warning",3e3);if(confirm(`${n("confirmDeleteSelected","هل أنت متأكد من حذف العناصر المحددة؟")} (${yt.length})\n\n${n("stockWillBeAdjusted","سيتم تعديل المخزون تلقائياً.")}`)){Ke.filter((e=>yt.includes(e.id))).forEach((e=>{e.items.forEach((e=>{const t=Dn.findIndex((t=>t.id===e.productId));if(-1!==t){const n=[...Dn];n[t].stock-=e.quantity,Tn(n),localStorage.setItem("icaldz-products",JSON.stringify(n))}}))}));const e=Ke.filter((e=>!yt.includes(e.id)));Xe(e),localStorage.setItem("icaldz-purchases",JSON.stringify(e)),Nt([]),Zt(`✅ ${n("selectedItemsDeleted","تم حذف العناصر المحددة")} (${yt.length}) ${n("andStockAdjusted","وتعديل المخزون")}`,"success",3e3)}break;case"customers":if(0===$t.length)return void Zt(`⚠️ ${n("pleaseSelectItems","يرجى تحديد عناصر للحذف")}`,"warning",3e3);if(confirm(`${n("confirmDeleteSelected","هل أنت متأكد من حذف العناصر المحددة؟")} (${$t.length})`)){const e=ba.filter((e=>!$t.includes(e.id)));fa(e),localStorage.setItem("icaldz-customers",JSON.stringify(e)),wt([]),Zt(`✅ ${n("selectedItemsDeleted","تم حذف العناصر المحددة")} (${$t.length})`,"success",3e3)}break;case"products":if(0===St.length)return void Zt(`⚠️ ${n("pleaseSelectItems","يرجى تحديد عناصر للحذف")}`,"warning",3e3);if(confirm(`${n("confirmDeleteSelected","هل أنت متأكد من حذف العناصر المحددة؟")} (${St.length})`)){const e=Dn.filter((e=>!St.includes(e.id)));Tn(e),localStorage.setItem("icaldz-products",JSON.stringify(e)),kt([]),Zt(`✅ ${n("selectedItemsDeleted","تم حذف العناصر المحددة")} (${St.length})`,"success",3e3)}break;case"suppliers":if(0===Ct.length)return void Zt(`⚠️ ${n("pleaseSelectItems","يرجى تحديد عناصر للحذف")}`,"warning",3e3);if(confirm(`${n("confirmDeleteSelected","هل أنت متأكد من حذف العناصر المحددة؟")} (${Ct.length})`)){const e=Qe.filter((e=>!Ct.includes(e.id)));He(e),localStorage.setItem("icaldz-suppliers",JSON.stringify(e)),It([]),Zt(`✅ ${n("selectedItemsDeleted","تم حذف العناصر المحددة")} (${Ct.length})`,"success",3e3)}break;case"sellers":if(0===Dt.length)return void Zt(`⚠️ ${n("pleaseSelectItems","يرجى تحديد عناصر للحذف")}`,"warning",3e3);if(confirm(`${n("confirmDeleteSelected","هل أنت متأكد من حذف العناصر المحددة؟")} (${Dt.length})`)){const e=Et.filter((e=>!Dt.includes(e.id)));At(e),localStorage.setItem("icaldz-sellers",JSON.stringify(e)),Tt([]),Zt(`✅ ${n("selectedItemsDeleted","تم حذف العناصر المحددة")} (${Dt.length})`,"success",3e3)}}else Zt(`❌ ${n("notAllowedManagerOnlyBulkDelete","غير مسموح - المدير فقط يمكنه الحذف المتعدد")}`,"error")},Ht=(e,t="inventory_export")=>{try{const a=s.book_new(),r=[n("productCode","رمز المنتج"),n("productName","اسم المنتج"),n("barcode","الباركود"),n("category","الفئة"),`${n("buyPrice","سعر الشراء")} (${n("currency","دج")})`,`${n("sellPrice","سعر البيع")} (${n("currency","دج")})`,n("availableQuantity","الكمية المتوفرة"),n("minStock","الحد الأدنى"),`${n("totalValue","القيمة الإجمالية")} (${n("currency","دج")})`,n("status","الحالة")],o=e.map((e=>{const t=e.stock<=e.minStock?n("lowStock","منخفض"):0===e.stock?n("outOfStock","نفد"):e.stock>2*e.minStock?n("highStock","مرتفع"):n("normalStock","عادي");return[e.id,e.name,e.barcode||n("notSpecified","غير محدد"),e.category,e.buyPrice,e.sellPrice,e.stock,e.minStock,e.stock*e.buyPrice,t]})),l=e.length,c=e.reduce(((e,t)=>e+t.stock*t.buyPrice),0),d=e.filter((e=>e.stock<=e.minStock)).length,m=e.filter((e=>0===e.stock)).length;o.push([]),o.push(["","","","","","","","","",""]),o.push([n("summary","الملخص"),"","","","","","","","",""]),o.push([n("totalProducts","إجمالي المنتجات"),l,"","","","","","","",""]),o.push([n("totalValue","القيمة الإجمالية"),c,"","","","","","","",""]),o.push([n("lowStockProducts","منتجات منخفضة المخزون"),d,"","","","","","","",""]),o.push([n("outOfStockProducts","منتجات نفدت"),m,"","","","","","","",""]);const h=[r,...o],p=s.aoa_to_sheet(h),u=[{wch:12},{wch:25},{wch:15},{wch:15},{wch:15},{wch:15},{wch:12},{wch:12},{wch:18},{wch:12}];p["!cols"]=u;const x=s.decode_range(p["!ref"]);for(let e=x.s.c;e<=x.e.c;e++){const t=s.encode_cell({r:0,c:e});p[t]&&(p[t].s={font:{bold:!0,color:{rgb:"FFFFFF"}},fill:{fgColor:{rgb:"4472C4"}},alignment:{horizontal:"center",vertical:"center",readingOrder:2},border:{top:{style:"thin",color:{rgb:"000000"}},bottom:{style:"thin",color:{rgb:"000000"}},left:{style:"thin",color:{rgb:"000000"}},right:{style:"thin",color:{rgb:"000000"}}}})}const v=e.length+3;for(let e=v;e<v+5;e++){const t=s.encode_cell({r:e,c:0});p[t]&&(p[t].s={font:{bold:!0},alignment:{horizontal:"right",readingOrder:2}})}p["!dir"]="ar"===y?"rtl":"ltr";const g=n("inventoryReport","تقرير المخزون");s.book_append_sheet(a,p,g);const b=(new Date).toISOString().split("T")[0];return i(a,`${t}_${b}.xlsx`),!0}catch(a){return!1}};e.useEffect((()=>{Kt(),Xt(),tn(),an();const e=(()=>{try{const e=localStorage.getItem("icaldz-last-invoice-lcd");return e?JSON.parse(e):null}catch(e){return null}})();e&&ze(e)}),[]);const Kt=()=>{const e=localStorage.getItem("icaldz-settings");e&&Mt(JSON.parse(e))},Xt=()=>{const e=localStorage.getItem("icaldz-sellers");if(e)At(JSON.parse(e));else{const e=[{id:"S001",name:"مدير النظام",username:"admin",password:"admin",phone:"+213 555 000 000",email:"<EMAIL>",role:"admin",isActive:!0,createdAt:(new Date).toISOString()}];At(e),localStorage.setItem("icaldz-sellers",JSON.stringify(e))}},en=e=>{l.saveData("icaldz-sellers",e),At(e)},tn=()=>{const e=localStorage.getItem("icaldz-suppliers");if(e)He(JSON.parse(e));else{const e=[{id:"SUP001",name:"شركة التوريدات العامة",phone:"+213 555 111 111",email:"<EMAIL>",address:"الجزائر العاصمة"},{id:"SUP002",name:"مؤسسة الإمداد",phone:"+213 555 222 222",email:"<EMAIL>",address:"وهران"},{id:"SUP003",name:"شركة السلع الأولية",phone:"+213 555 333 333",email:"<EMAIL>",address:"قسنطينة"}];He(e),localStorage.setItem("icaldz-suppliers",JSON.stringify(e))}},nn=e=>{l.saveData("icaldz-suppliers",e),He(e)},an=()=>{const e=localStorage.getItem("icaldz-purchases");e&&Xe(JSON.parse(e))},sn=e=>{l.saveData("icaldz-purchases",e),Xe(e)},rn=()=>{lt(!0),vt({id:`SUP${String(Qe.length+1).padStart(3,"0")}`,name:"",phone:"",address:"",email:""})},on=()=>{lt(!1),vt({id:"",name:"",phone:"",address:"",email:""})},ln=e=>{if(window.confirm(n("confirmDeleteSupplier","هل أنت متأكد من حذف هذا المورد؟"))){const t=Qe.filter((t=>t.id!==e));nn(t),Zt(n("supplierDeletedSuccessfully","تم حذف المورد بنجاح"),"success")}},cn=()=>{ut(null),ht(!1)},dn=e=>{Ge(e),Ve(!0)},mn=e=>{try{return b.printInvoice(e,{language:y,showToast:Zt,formatPrice:Ut,directPrint:F,storeSettings:Pt}),void(M&&j.play("printInvoice",{showNotification:!1}))}catch(t){}hn(e)},hn=e=>{const t="ar"===y,a=`\n      <!DOCTYPE html>\n      <html dir="${t?"rtl":"ltr"}" lang="${y}">\n      <head>\n        <meta charset="UTF-8">\n        <style>\n          @page {\n            size: 80mm auto;\n            margin: 0;\n          }\n\n          body {\n            font-family: 'Courier New', monospace;\n            font-size: 14px;\n            font-weight: bold;\n            line-height: 1.4;\n            margin: 0;\n            padding: 3mm;\n            width: 74mm;\n            color: black;\n            background: white;\n            direction: ${t?"rtl":"ltr"};\n            text-align: center;\n          }\n\n          .header {\n            text-align: center;\n            margin-bottom: 4mm;\n            border-bottom: 2px solid black;\n            padding-bottom: 3mm;\n            position: relative;\n          }\n\n          .logo {\n            margin: 0 auto 2mm auto;\n            width: 15mm;\n            height: 15mm;\n            border-radius: 50%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            overflow: hidden;\n            background: ${Pt.storeLogo?"white":"linear-gradient(135deg, #007bff, #0056b3)"};\n            border: 2px solid #333;\n          }\n\n          .logo img {\n            width: 100%;\n            height: 100%;\n            object-fit: cover;\n            border-radius: 50%;\n          }\n\n          .logo-fallback {\n            color: white;\n            font-size: 8px;\n            font-weight: bold;\n          }\n\n          .store-name {\n            font-size: 18px;\n            font-weight: bold;\n            margin-bottom: 2mm;\n            text-transform: uppercase;\n          }\n\n          .store-info {\n            font-size: 12px;\n            font-weight: bold;\n            margin-bottom: 1mm;\n          }\n\n          .invoice-info {\n            margin: 3mm 0;\n            font-size: 12px;\n            text-align: center;\n          }\n\n          .invoice-info div {\n            display: flex;\n            justify-content: space-between;\n            margin-bottom: 2mm;\n            font-weight: bold;\n          }\n\n          .items-header {\n            border-bottom: 2px solid black;\n            padding-bottom: 2mm;\n            margin-bottom: 2mm;\n            font-size: 14px;\n            font-weight: bold;\n            text-align: center;\n            text-transform: uppercase;\n          }\n\n          .item-row {\n            font-size: 12px;\n            margin-bottom: 2mm;\n            padding: 2mm 0;\n            border-bottom: 1px dashed #666;\n          }\n\n          .item-name {\n            font-weight: bold;\n            margin-bottom: 1mm;\n            text-align: center;\n            font-size: 13px;\n          }\n\n          .item-details {\n            display: flex;\n            justify-content: space-between;\n            font-weight: bold;\n          }\n\n          .totals {\n            border-top: 2px solid black;\n            padding-top: 3mm;\n            margin-top: 3mm;\n            font-size: 12px;\n          }\n\n          .total-row {\n            display: flex;\n            justify-content: space-between;\n            margin-bottom: 2mm;\n            font-weight: bold;\n          }\n\n          .final-total {\n            font-weight: bold;\n            font-size: 16px;\n            border: 3px double black;\n            padding: 3mm;\n            margin: 3mm 0;\n            text-align: center;\n            background: #f0f0f0;\n          }\n\n          .footer {\n            text-align: center;\n            margin-top: 4mm;\n            padding-top: 3mm;\n            border-top: 2px solid black;\n            font-size: 10px;\n          }\n\n          .thank-you {\n            font-weight: bold;\n            margin-bottom: 2mm;\n            font-size: 14px;\n          }\n\n          .date-time {\n            font-size: 10px;\n            font-weight: bold;\n            margin-bottom: 3mm;\n          }\n\n          .developer-footer {\n            margin-top: 4mm;\n            padding-top: 2mm;\n            border-top: 1px dashed black;\n            font-size: 10px;\n            font-weight: bold;\n          }\n\n          .developer-name {\n            margin-bottom: 1mm;\n          }\n\n          .developer-phone {\n            font-size: 12px;\n            font-weight: bold;\n          }\n\n          /* Print specific styles */\n          @media print {\n            body {\n              width: 80mm !important;\n              font-size: 14px !important;\n              font-weight: bold !important;\n            }\n\n            .no-print {\n              display: none !important;\n            }\n          }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <div class="logo">\n            ${Pt.storeLogo?`<img src="${Pt.storeLogo}" alt="${Pt.storeName}" />`:'<div class="logo-fallback">iC</div>'}\n          </div>\n          <div class="store-name">${Pt.storeName}</div>\n          <div class="store-info">${Pt.storePhone}</div>\n          <div class="store-info">${Pt.storeAddress}</div>\n        </div>\n\n        <div class="invoice-info">\n          <div>\n            <span>${n("invoiceNumberLabel","فاتورة رقم:")}</span>\n            <span>${e.invoiceNumber}</span>\n          </div>\n          <div>\n            <span>${n("dateLabel","التاريخ:")}</span>\n            <span>${e.date}</span>\n          </div>\n          <div>\n            <span>${n("customerLabel","الزبون:")}</span>\n            <span>${e.customerName||n("walkInCustomer","زبون عابر")}</span>\n          </div>\n          <div>\n            <span>${n("paymentMethodLabel","طريقة الدفع:")}</span>\n            <span>${"نقداً"===e.paymentMethod?n("cash","نقداً"):"دين"===e.paymentMethod?n("credit","دين"):"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?n("cash","نقداً"):"Crédit"===e.paymentMethod||"Credit"===e.paymentMethod?n("credit","دين"):e.paymentMethod||n("cash","نقداً")}</span>\n          </div>\n        </div>\n\n        <div class="items-header">\n          ${n("productsLabel","المنتجات")}\n        </div>\n\n        <div class="items-list">\n          ${e.items.map((e=>`\n            <div class="item-row">\n              <div class="item-name">${e.productName}</div>\n              <div class="item-details">\n                <span>${e.quantity} × ${Ut(e.price)}</span>\n                <span>${Ut(e.total)}</span>\n              </div>\n            </div>\n          `)).join("")}\n        </div>\n\n        <div class="totals">\n          <div class="total-row">\n            <span>${n("subtotalLabel","المجموع الفرعي:")}</span>\n            <span>${Ut(e.total)}</span>\n          </div>\n          ${e.discount>0?`\n            <div class="total-row">\n              <span>${n("discountLabel","الخصم:")}</span>\n              <span>-${Ut(e.discount)}</span>\n            </div>\n          `:""}\n          <div class="total-row">\n            <span>${n("taxLabel","الضريبة")} (${Pt.taxRate}%):</span>\n            <span>${Ut(e.tax)}</span>\n          </div>\n          <div class="final-total">\n            <div>${n("finalTotalLabel","المجموع النهائي:")}</div>\n            <div style="font-size: 20px; margin-top: 2mm;">${Ut(e.finalTotal)}</div>\n          </div>\n        </div>\n\n        <div class="footer">\n          <div class="thank-you">${n("thankYouMessage","شكراً لزيارتكم")}</div>\n          <div class="date-time">${n("printedAtLabel","طُبعت في:")} ${(new Date).toLocaleString("ar"===y?"ar-DZ":"fr"===y?"fr-FR":"en-US")}</div>\n\n          <div class="developer-footer">\n            <div class="developer-name">Developed by iCode DZ</div>\n            <div class="developer-phone">0551930589</div>\n          </div>\n        </div>\n      </body>\n      </html>\n    `,s=window.open("","_blank","width=400,height=700");s.document.write(a),s.document.close(),s.onload=function(){setTimeout((()=>{s.print(),s.close()}),1e3)}},pn=e=>{if(F&&b.isThermalPrinterAvailable())return void mn(e);const t=window.open("","_blank","width=800,height=600");if(!t)return void Zt(`❌ ${n("popupBlocked","تم حظر النافذة المنبثقة - يرجى السماح بالنوافذ المنبثقة")}`,"error",5e3);const a="ar"===y,s=a?"left":"right",i=`\n      <!DOCTYPE html>\n      <html dir="${a?"rtl":"ltr"}" lang="${y}">\n      <head>\n        <meta charset="UTF-8">\n        <meta name="viewport" content="width=device-width, initial-scale=1.0">\n        <title>${n("salesInvoiceTitle","فاتورة مبيعات")} - ${e.invoiceNumber}</title>\n        <style>\n          * {\n            margin: 0;\n            padding: 0;\n            box-sizing: border-box;\n          }\n\n          body {\n            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n            direction: ${a?"rtl":"ltr"};\n            background: white;\n            color: #333;\n            line-height: 1.6;\n            padding: 20px;\n          }\n\n          .invoice-container {\n            max-width: 800px;\n            margin: 0 auto;\n            background: white;\n            border: 2px solid #ddd;\n            border-radius: 10px;\n            overflow: hidden;\n          }\n\n          .invoice-header {\n            background: linear-gradient(135deg, #007bff, #0056b3);\n            color: white;\n            padding: 30px;\n            text-align: center;\n            position: relative;\n          }\n\n          .header-logo {\n            position: absolute;\n            top: 20px;\n            ${s}: 30px;\n            width: 60px;\n            height: 60px;\n            background: ${Pt.storeLogo?"white":"rgba(255, 255, 255, 0.2)"};\n            border-radius: 50%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: white;\n            font-size: 18px;\n            font-weight: bold;\n            border: 2px solid rgba(255, 255, 255, 0.3);\n            overflow: hidden;\n          }\n\n          .header-logo img {\n            width: 100%;\n            height: 100%;\n            object-fit: cover;\n            border-radius: 50%;\n          }\n\n          .header-logo-fallback {\n            color: white;\n            font-size: 18px;\n            font-weight: bold;\n          }\n\n          .invoice-header h1 {\n            font-size: 2.5rem;\n            margin-bottom: 10px;\n            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);\n          }\n\n          .invoice-header p {\n            font-size: 1.1rem;\n            opacity: 0.9;\n          }\n\n          .invoice-info {\n            display: grid;\n            grid-template-columns: 1fr 1fr;\n            gap: 30px;\n            padding: 30px;\n            background: #f8f9fa;\n            border-bottom: 3px solid #007bff;\n          }\n\n          .info-section h3 {\n            color: #007bff;\n            margin-bottom: 15px;\n            font-size: 1.3rem;\n            border-bottom: 2px solid #007bff;\n            padding-bottom: 5px;\n          }\n\n          .info-item {\n            margin-bottom: 8px;\n            font-size: 1.1rem;\n          }\n\n          .info-item strong {\n            color: #2c3e50;\n            display: inline-block;\n            min-width: 120px;\n          }\n\n          .items-table {\n            width: 100%;\n            border-collapse: collapse;\n            margin: 30px 0;\n          }\n\n          .items-table th {\n            background: #007bff;\n            color: white;\n            padding: 15px 10px;\n            text-align: center;\n            font-size: 1.2rem;\n            font-weight: 600;\n          }\n\n          .items-table td {\n            padding: 12px 10px;\n            text-align: center;\n            border-bottom: 1px solid #ddd;\n            font-size: 1.1rem;\n            font-weight: 500;\n          }\n\n          .items-table tbody tr:nth-child(even) {\n            background: #f8f9fa;\n          }\n\n          .items-table tbody tr:hover {\n            background: #e3f2fd;\n          }\n\n          .totals-section {\n            background: #f8f9fa;\n            padding: 30px;\n            border-top: 3px solid #007bff;\n          }\n\n          .totals-grid {\n            display: grid;\n            grid-template-columns: 1fr 300px;\n            gap: 30px;\n            align-items: start;\n          }\n\n          .payment-info h3 {\n            color: #007bff;\n            margin-bottom: 15px;\n            font-size: 1.3rem;\n          }\n\n          .payment-method {\n            display: inline-block;\n            padding: 8px 16px;\n            border-radius: 20px;\n            font-weight: 600;\n            font-size: 1.1rem;\n          }\n\n          .payment-method.cash {\n            background: #d4edda;\n            color: #155724;\n            border: 2px solid #c3e6cb;\n          }\n\n          .payment-method.credit {\n            background: #fff3cd;\n            color: #856404;\n            border: 2px solid #ffeaa7;\n          }\n\n          .totals-table {\n            width: 100%;\n            border-collapse: collapse;\n          }\n\n          .totals-table td {\n            padding: 12px 15px;\n            border-bottom: 1px solid #ddd;\n            font-size: 1.2rem;\n          }\n\n          .totals-table .label {\n            font-weight: 600;\n            color: #2c3e50;\n            text-align: right;\n            background: #f8f9fa;\n          }\n\n          .totals-table .value {\n            font-weight: 700;\n            text-align: left;\n            color: #007bff;\n          }\n\n          .final-total {\n            background: #007bff !important;\n            color: white !important;\n            font-size: 1.4rem !important;\n            font-weight: 800 !important;\n          }\n\n          .invoice-footer {\n            background: #2c3e50;\n            color: white;\n            text-align: center;\n            padding: 20px;\n            font-size: 1.1rem;\n          }\n\n          .thank-you {\n            font-size: 1.3rem;\n            margin-bottom: 10px;\n            font-weight: 600;\n          }\n\n          @media print {\n            body {\n              padding: 0;\n            }\n\n            .invoice-container {\n              border: none;\n              border-radius: 0;\n              box-shadow: none;\n            }\n          }\n        </style>\n      </head>\n      <body>\n        <div class="invoice-container">\n          \x3c!-- Invoice Header --\x3e\n          <div class="invoice-header">\n            <div class="header-logo">\n              ${Pt.storeLogo?`<img src="${Pt.storeLogo}" alt="${Pt.storeName}" />`:'<div class="header-logo-fallback">iC</div>'}\n            </div>\n            <h1>${Pt.storeName}</h1>\n            <p>📞 ${Pt.storePhone} | 📍 ${Pt.storeAddress}</p>\n          </div>\n\n          \x3c!-- Invoice Info --\x3e\n          <div class="invoice-info">\n            <div class="info-section">\n              <h3>📄 ${n("invoiceInfo","معلومات الفاتورة")}</h3>\n              <div class="info-item"><strong>${n("invoiceNumber","رقم الفاتورة")}:</strong> ${e.invoiceNumber}</div>\n              <div class="info-item"><strong>${n("date","التاريخ")}:</strong> ${e.date}</div>\n              <div class="info-item"><strong>${n("creationTime","وقت الإنشاء")}:</strong> ${e.createdAt||(new Date).toLocaleString("ar"===y?"ar-DZ":"fr"===y?"fr-FR":"en-US")}</div>\n            </div>\n\n            <div class="info-section">\n              <h3>👤 ${n("customerInfo","معلومات الزبون")}</h3>\n              <div class="info-item"><strong>${n("customerName","اسم الزبون")}:</strong> ${e.customerName||n("walkInCustomer","زبون عابر")}</div>\n              <div class="info-item"><strong>${n("paymentMethod","طريقة الدفع")}:</strong>\n                <span class="payment-method ${"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"cash":"credit"}">\n                  ${"نقداً"===e.paymentMethod?n("cash","نقداً"):"دين"===e.paymentMethod?n("credit","دين"):"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?n("cash","نقداً"):"Crédit"===e.paymentMethod||"Credit"===e.paymentMethod?n("credit","دين"):e.paymentMethod||n("cash","نقداً")}\n                </span>\n              </div>\n            </div>\n          </div>\n\n          \x3c!-- Items Table --\x3e\n          <table class="items-table">\n            <thead>\n              <tr>\n                <th>${n("product","المنتج")}</th>\n                <th>${n("quantity","الكمية")}</th>\n                <th>${n("price","السعر")}</th>\n                <th>${n("total","المجموع")}</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${e.items.map((e=>`\n                <tr>\n                  <td style="font-weight: 600; color: #2c3e50;">${e.productName}</td>\n                  <td style="font-weight: 600; color: #e74c3c;">${e.quantity}</td>\n                  <td style="font-weight: 600; color: #3498db;">${Ut(e.price)}</td>\n                  <td style="font-weight: 700; color: #27ae60;">${Ut(e.total)}</td>\n                </tr>\n              `)).join("")}\n            </tbody>\n          </table>\n\n          \x3c!-- Totals Section --\x3e\n          <div class="totals-section">\n            <div class="totals-grid">\n              <div class="payment-info">\n                <h3>💳 ${n("paymentInfo","معلومات الدفع")}</h3>\n                <p><strong>${n("paymentMethod","طريقة الدفع")}:</strong>\n                  <span class="payment-method ${"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"cash":"credit"}">\n                    ${"نقداً"===e.paymentMethod?n("cash","نقداً"):"دين"===e.paymentMethod?n("credit","دين"):"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?n("cash","نقداً"):"Crédit"===e.paymentMethod||"Credit"===e.paymentMethod?n("credit","دين"):e.paymentMethod||n("cash","نقداً")}\n                  </span>\n                </p>\n                <p><strong>${n("invoiceStatus","حالة الفاتورة")}:</strong>\n                  <span style="color: ${"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"#28a745":"#ffc107"}; font-weight: 600;">\n                    ${"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?n("paidStatus","مدفوعة"):n("debtStatus","دين")}\n                  </span>\n                </p>\n              </div>\n\n              <table class="totals-table">\n                <tr>\n                  <td class="label">${n("subtotalLabel","المجموع الفرعي:")}</td>\n                  <td class="value">${Ut(e.total)}</td>\n                </tr>\n                <tr>\n                  <td class="label">${n("discountLabel","الخصم:")}</td>\n                  <td class="value">${Ut(e.discount||0)}</td>\n                </tr>\n                <tr>\n                  <td class="label">${n("taxLabel","الضريبة")} (${Pt.taxRate}%):</td>\n                  <td class="value">${Ut(e.tax)}</td>\n                </tr>\n                <tr>\n                  <td class="label final-total">${n("finalTotalLabel","المجموع النهائي:")}</td>\n                  <td class="value final-total">${Ut(e.finalTotal)}</td>\n                </tr>\n              </table>\n            </div>\n          </div>\n\n          \x3c!-- Invoice Footer --\x3e\n          <div class="invoice-footer">\n            <div class="thank-you">${n("thankYouForDealingWithUs","شكراً لتعاملكم معنا")}</div>\n            <div>© ${(new Date).getFullYear()} ${Pt.storeName} - ${n("allRightsReserved","جميع الحقوق محفوظة")}</div>\n          </div>\n        </div>\n      </body>\n      </html>\n    `;t.document.write(i),t.document.close(),t.onload=function(){setTimeout((()=>{t.focus(),t.print(),F&&setTimeout((()=>t.close()),2e3)}),500)},Zt(`🖨️ ${n("printWindowOpened","تم فتح نافذة الطباعة")}`,"success",2e3)},un=()=>{Ve(!1),Ge(null)},xn=()=>{Gn(!1),_n(null),Yn([]),Ja(""),clearTimeout(window.editScannerValidationTimeout),ca(""),ma(1),pa(0)},vn=e=>{Xn(e),ta(e.items.map((e=>({...e,returnQuantity:0,maxReturnQuantity:e.quantity})))),Hn(!0)},gn=()=>{Hn(!1),Xn(null),ta([])},bn=()=>{te(!0),Ye({invoiceNumber:"PUR-"+Date.now(),date:(new Date).toISOString().split("T")[0],supplierId:"",supplierName:"",paymentMethod:"نقداً",items:[],total:0,discount:0,tax:0,finalTotal:0})},fn=()=>{te(!1),_e(null),ca(""),ma(1),pa(0)},jn=()=>{if(!la)return void Zt(`⚠️ ${n("pleaseSelectProduct","يرجى اختيار منتج")}`,"warning",3e3);if(da<=0)return void Zt(`⚠️ ${n("pleaseEnterValidQuantity","يرجى إدخال كمية صحيحة")}`,"warning",3e3);if(ha<=0)return void Zt(`⚠️ ${n("pleaseEnterValidPrice","يرجى إدخال سعر صحيح")}`,"warning",3e3);const e=Dn.find((e=>e.id===la));if(!e)return void Zt(`❌ ${n("productNotFoundInSystem","المنتج غير موجود")}`,"error",3e3);const t=We.items.findIndex((t=>t.productId===e.id));if(-1!==t){const e=[...We.items];e[t].quantity+=da,e[t].total=e[t].quantity*ha;const n=e.reduce(((e,t)=>e+t.total),0),a=n*(Pt.taxRate/100),s=n+a-We.discount;Ye({...We,items:e,total:n,tax:a,finalTotal:s})}else{const t={productId:e.id,productName:e.name,price:ha,quantity:da,total:ha*da},n=[...We.items,t],a=n.reduce(((e,t)=>e+t.total),0),s=a*(Pt.taxRate/100),i=a+s-We.discount;Ye({...We,items:n,total:a,tax:s,finalTotal:i})}ca(""),ma(1),pa(0),Zt(`✅ تم إضافة ${e.name} لفاتورة المشتريات`,"success",2e3)},yn=()=>{if(0===We.items.length)return void Zt(`⚠️ ${n("pleaseAddProductsToInvoice","يرجى إضافة منتجات للفاتورة")}`,"warning",3e3);if(!We.supplierId)return void Zt(`⚠️ ${n("pleaseSelectSupplier","يرجى اختيار مورد")}`,"warning",3e3);const e=We.supplierName||n("generalSupplier","مورد عام");if(Je){const t={...Je,...We,customerName:e,updatedAt:(new Date).toISOString()},n={};Je.items.forEach((e=>{n[e.productId]||(n[e.productId]=0),n[e.productId]-=e.quantity})),We.items.forEach((e=>{n[e.productId]||(n[e.productId]=0),n[e.productId]+=e.quantity}));const a=Dn.map((e=>n[e.id]?{...e,stock:e.stock+n[e.id]}:e)),s=Ke.map((e=>e.id===Je.id?t:e));Tn(a),localStorage.setItem("icaldz-products",JSON.stringify(a)),sn(s),Zt(`✅ تم تحديث فاتورة المشتريات رقم ${We.invoiceNumber} وتحديث المخزون!`,"success",4e3)}else{const t=[{id:"PUR-"+Date.now(),...We,customerName:e,createdAt:(new Date).toISOString(),status:"نقداً"===We.paymentMethod||"Espèces"===We.paymentMethod||"En espèces"===We.paymentMethod||"Cash"===We.paymentMethod?"مدفوعة":"دين"},...Ke];sn(t),We.items.forEach((e=>{const t=Dn.findIndex((t=>t.id===e.productId));if(-1!==t){const n=[...Dn];n[t].stock+=e.quantity,Tn(n),localStorage.setItem("icaldz-products",JSON.stringify(n))}})),Zt(`💾 تم حفظ فاتورة المشتريات رقم ${We.invoiceNumber} للمورد ${e} بنجاح وتم تحديث المخزون!`,"success",4e3)}fn()},Nn=()=>{bt(!1)},$n=()=>{Ft(!1),Bt({id:"",name:"",username:"",password:"",phone:"",email:"",role:"seller",isActive:!0})},wn=e=>{localStorage.setItem("icaldz-categories",JSON.stringify(e)),On(e)},Sn=()=>{zn(!0),Un(""),Vn(null)},kn=()=>{zn(!1),Un(""),Vn(null)},Cn=()=>{if(!Bn.trim())return void Zt(`⚠️ ${n("pleaseEnterCategoryName","يرجى إدخال اسم الفئة")}`,"warning");if(Fn.includes(Bn.trim()))return void Zt(`❌ ${n("categoryAlreadyExists","الفئة موجودة مسبقاً")}`,"error");const e=[...Fn,Bn.trim()];wn(e),Zt(`✅ ${n("categoryAddedSuccessfully","تم إضافة الفئة")} "${Bn.trim()}" ${n("successfully","بنجاح")}`,"success"),Un("")},In=(e,t)=>{if(!t.trim())return void Zt("⚠️ يرجى إدخال اسم الفئة الجديد","warning");if(Fn.includes(t.trim())&&t.trim()!==e)return void Zt("❌ الفئة موجودة مسبقاً","error");const n=Fn.map((n=>n===e?t.trim():n));wn(n);const a=Dn.map((n=>n.category===e?{...n,category:t.trim()}:n));Mn(a),Zt(`✅ تم تحديث الفئة من "${e}" إلى "${t.trim()}" بنجاح`,"success"),Vn(null)};e.useEffect((()=>{Kt(),Xt(),tn(),an(),On((()=>{const e=localStorage.getItem("icaldz-categories");if(e)try{return JSON.parse(e)}catch(n){}const t=["إلكترونيات","ملابس","طعام ومشروبات","أدوات منزلية","كتب وقرطاسية","رياضة وترفيه","صحة وجمال","أطفال ولعب"];return localStorage.setItem("icaldz-categories",JSON.stringify(t)),t})())}),[]),e.useEffect((()=>{const e=setTimeout((()=>{En()}),1e3);return()=>clearTimeout(e)}),[]);const[Dn,Tn]=e.useState((()=>{const e=localStorage.getItem("icaldz-products");if(e)try{return JSON.parse(e).map((e=>({...e,buyPrice:parseFloat(e.buyPrice)||0,sellPrice:parseFloat(e.sellPrice||e.price)||0,price:parseFloat(e.sellPrice||e.price)||0})))}catch(t){}return[{id:"P001",name:"لابتوب HP",buyPrice:75e3,sellPrice:85e3,price:85e3,stock:15,barcode:"*********",category:"إلكترونيات",minStock:5,createdAt:(new Date).toISOString()},{id:"P002",name:"ماوس لاسلكي",buyPrice:2e3,sellPrice:2500,price:2500,stock:50,barcode:"000000002",category:"إلكترونيات",minStock:10,createdAt:(new Date).toISOString()},{id:"P003",name:"كيبورد ميكانيكي",buyPrice:7e3,sellPrice:8500,price:8500,stock:25,barcode:"000000003",category:"إلكترونيات",minStock:5,createdAt:(new Date).toISOString()},{id:"P004",name:"شاشة 24 بوصة",buyPrice:38e3,sellPrice:45e3,price:45e3,stock:10,barcode:"000000004",category:"إلكترونيات",minStock:3,createdAt:(new Date).toISOString()},{id:"P005",name:"سماعات بلوتوث",buyPrice:9500,sellPrice:12e3,price:12e3,stock:30,barcode:"000000005",category:"إلكترونيات",minStock:8,createdAt:(new Date).toISOString()},{id:"P006",name:"كابل USB-C",buyPrice:800,sellPrice:1200,price:1200,stock:100,barcode:"000000006",category:"إلكترونيات",minStock:20,createdAt:(new Date).toISOString()},{id:"P007",name:"شاحن هاتف",buyPrice:1500,sellPrice:2200,price:2200,stock:40,barcode:"000000007",category:"إلكترونيات",minStock:10,createdAt:(new Date).toISOString()}]})),Pn=c({savedInvoices:Ce,products:Dn,expenses:tt,formatPrice:Ut,showToast:Zt}),Mn=e=>{const t=e.map((e=>({...e,buyPrice:e.buyPrice||(e.price?.7*e.price:0),sellPrice:e.sellPrice||e.price||0,price:e.sellPrice||e.price||0,stock:e.stock||0,minStock:e.minStock||5,createdAt:e.createdAt||(new Date).toISOString()})));localStorage.setItem("icaldz-products",JSON.stringify(t)),Tn(t)},En=()=>{let e=!1;const t=Dn.map((t=>{const n={...t};if(!n.buyPrice||isNaN(parseFloat(n.buyPrice))||parseFloat(n.buyPrice)<=0){const t=parseFloat(n.sellPrice||n.price)||0;t>0&&(n.buyPrice=.7*t,e=!0)}if(!n.sellPrice||isNaN(parseFloat(n.sellPrice))||parseFloat(n.sellPrice)<=0){const t=parseFloat(n.price)||0;t>0&&(n.sellPrice=t,e=!0)}return n.price&&parseFloat(n.price)===parseFloat(n.sellPrice)||(n.price=n.sellPrice,e=!0),n.stock&&!isNaN(parseInt(n.stock))||(n.stock=0,e=!0),n.minStock&&!isNaN(parseInt(n.minStock))||(n.minStock=5,e=!0),n.createdAt||(n.createdAt=(new Date).toISOString(),e=!0),n}));return e&&(Mn(t),Zt("✅ تم إصلاح بيانات المنتجات تلقائياً","info",3e3)),t},[An,Rn]=e.useState({id:"",name:"",category:"",barcode:"",buyPrice:0,sellPrice:0,stock:0,minStock:0}),[Fn,On]=e.useState([]),[Ln,zn]=e.useState(!1),[Bn,Un]=e.useState(""),[qn,Vn]=e.useState(null),[Zn,Gn]=e.useState(!1),[Jn,_n]=e.useState(null),[Wn,Yn]=e.useState([]);e.useEffect((()=>{Zn&&Ya.current&&setTimeout((()=>{Ya.current.focus()}),100)}),[Zn]);const[Qn,Hn]=e.useState(!1),[Kn,Xn]=e.useState(null),[ea,ta]=e.useState([]),[na,aa]=e.useState(!1),[sa,ia]=e.useState(""),[ra,oa]=e.useState({invoiceNumber:"INV-"+Date.now(),date:(new Date).toISOString().split("T")[0],customerId:"",customerName:"",paymentMethod:"نقداً",items:[],total:0,discount:0,tax:0,finalTotal:0}),[la,ca]=e.useState(""),[da,ma]=e.useState(1),[ha,pa]=e.useState(0),[ua,xa]=e.useState(""),va=[{id:1,title:n("totalSales","إجمالي المبيعات"),value:Ut(Ce.reduce(((e,t)=>e+t.finalTotal),0)),unit:"",icon:"💰",color:"green"},{id:2,title:n("totalInvoices","عدد الفواتير"),value:Ce.length,unit:n("invoices","فاتورة"),icon:"📄",color:"blue"},{id:3,title:n("totalProducts","إجمالي المنتجات"),value:Dn.length,unit:n("products","منتج"),icon:"📦",color:"orange"},{id:4,title:n("lowStockProducts","منتجات منخفضة المخزون"),value:Dn.filter((e=>e.stock<=e.minStock)).length,unit:n("products","منتج"),icon:"⚠️",color:"red"}],ga=Ce.slice(0,5).map((e=>({id:e.invoiceNumber,date:e.date,supplier:qt(e.customerName),paid:e.finalTotal,amount:e.finalTotal,status:n("paid","مدفوعة")}))),[ba,fa]=e.useState((()=>{const e=localStorage.getItem("icaldz-customers");return e?JSON.parse(e):[{id:"C001",name:"أحمد محمد",email:"<EMAIL>",phone:"0555123456",address:"الجزائر العاصمة",company:"شركة التجارة",balance:15e3,creditLimit:5e4,paymentTerm:30,discountPercentage:5,status:"نشط",createdAt:(new Date).toISOString()},{id:"C002",name:"فاطمة علي",email:"<EMAIL>",phone:"0555654321",address:"وهران",company:"مؤسسة النور",balance:-2500,creditLimit:3e4,paymentTerm:15,discountPercentage:3,status:"نشط",createdAt:(new Date).toISOString()},{id:"C003",name:"محمد حسن",email:"<EMAIL>",phone:"0555789012",address:"قسنطينة",company:"",balance:8750,creditLimit:2e4,paymentTerm:30,discountPercentage:7,status:"غير نشط",createdAt:(new Date).toISOString()},{id:"C004",name:"عائشة سالم",email:"<EMAIL>",phone:"0555345678",address:"عنابة",company:"متجر الأمل",balance:0,creditLimit:25e3,paymentTerm:30,discountPercentage:2,status:"نشط",createdAt:(new Date).toISOString()}]})),ja=e=>{localStorage.setItem("icaldz-customers",JSON.stringify(e)),fa(e)},[ya,Na]=e.useState({id:"",name:"",email:"",phone:"",address:"",company:"",balance:0,creditLimit:0,paymentTerm:30,discountPercentage:0,status:"نشط"}),[$a,wa]=e.useState(!1),[Sa,ka]=e.useState(null),[Ca,Ia]=e.useState(""),Da=ba.filter((e=>e.name.toLowerCase().includes(Ca.toLowerCase()))),[Ta,Pa]=e.useState(""),Ma=Ce.filter((e=>e.customerName?.toLowerCase().includes(Ta.toLowerCase())||e.invoiceNumber?.toLowerCase().includes(Ta.toLowerCase()))),[Ea,Aa]=e.useState(""),Ra=Ke.filter((e=>e.customerName?.toLowerCase().includes(Ea.toLowerCase())||e.invoiceNumber?.toLowerCase().includes(Ea.toLowerCase())||e.supplierName?.toLowerCase().includes(Ea.toLowerCase()))),Fa=e=>{pe(e),xe(e.balance),ge("نقداً"),me(!0)},Oa=()=>{me(!1),pe(null),xe(0),ge("نقداً")},La=(e,t,n)=>{const a=ba.map((a=>a.id===e?{...a,balance:"add"===n?a.balance+t:a.balance-t}:a));ja(a)},za=()=>{wa(!1),ka(null)},[Ba,Ua]=e.useState(""),[qa,Va]=e.useState(null),Za=e.useRef(null),[Ga,Ja]=e.useState(""),[_a,Wa]=e.useState(null),Ya=e.useRef(null);e.useState(null),e.useState(null);const Qa=()=>{oa({invoiceNumber:"INV-"+Date.now(),date:(new Date).toISOString().split("T")[0],customerId:"GUEST",customerName:n("walkInCustomer","زبون عابر"),paymentMethod:n("cash","نقداً"),items:[],total:0,discount:0,tax:0,finalTotal:0}),Ua(""),clearTimeout(window.salesScannerValidationTimeout),ca(""),ma(1),pa(0),xa(""),Ja(""),clearTimeout(window.editScannerValidationTimeout),Te(""),X(!0),j.play("newInvoice",{showNotification:!1})},Ha=()=>{ra.items.length>0?Zt(`⚠️ ${n("invoiceHasItems","الفاتورة تحتوي على منتجات - استخدم زر الحفظ أو احذف المنتجات")}`,"warning",4e3):(X(!1),ca(""),ma(1),pa(0),xa(""),Ua(""),clearTimeout(window.salesScannerValidationTimeout),KeyboardShortcuts.setActiveWindow(Z),j.play("closeWindow",{showNotification:!1}))},Ka=()=>{X(!1),ca(""),ma(1),pa(0),xa(""),Ua(""),clearTimeout(window.salesScannerValidationTimeout),KeyboardShortcuts.setActiveWindow(Z),j.play("closeWindow",{showNotification:!1})},Xa=()=>{X(!1),ca(""),ma(1),pa(0),xa(""),Te(""),Ua(""),clearTimeout(window.salesScannerValidationTimeout),Ja(""),clearTimeout(window.editScannerValidationTimeout),Va(null),Wa(null),oa({invoiceNumber:"INV-"+Date.now(),date:(new Date).toISOString().split("T")[0],customerId:"GUEST",customerName:n("walkInCustomer","زبون عابر"),paymentMethod:n("cash","نقداً"),items:[],total:0,discount:0,tax:0,finalTotal:0}),KeyboardShortcuts.setActiveWindow(Z),j.play("closeWindow",{showNotification:!1}),setTimeout((()=>{"dashboard"===Z&&Pe.current&&Pe.current.focus()}),300)},es=()=>{if(!la)return void Zt(`⚠️ ${n("pleaseSelectProduct","يرجى اختيار منتج")}`,"warning",3e3);if(da<=0)return void Zt(`⚠️ ${n("pleaseEnterValidQuantity","يرجى إدخال كمية صحيحة")}`,"warning",3e3);const e=Dn.find((e=>e.id===la));if(!e)return void Zt(`❌ ${n("productNotFoundInSystem","المنتج غير موجود")}`,"error",3e3);if(da>e.stock)return void Zt(`❌ ${n("quantityExceedsStock","الكمية المطلوبة أكبر من المتوفر")}: (${da}) > (${e.stock})`,"error",3e3);const t={productId:e.id,productName:e.name,price:ha||e.sellPrice||e.price||0,quantity:da,total:(ha||e.sellPrice||e.price||0)*da},a=[...ra.items,t],s=a.reduce(((e,t)=>e+t.total),0),i=s*(Pt.taxRate/100),r=s+i-ra.discount;oa({...ra,items:a,total:s,tax:i,finalTotal:r}),ca(""),ma(1),pa(0),j.play("addProduct",{showNotification:!1}),Zt(`✅ ${n("productAddedToInvoice","تم إضافة")} ${e.name} ${n("toInvoice","للفاتورة")}`,"success",2e3)},ts=Dn.filter((e=>{if(!ua)return!0;const t=ua.toLowerCase();return e.name.toLowerCase().includes(t)||e.id.toLowerCase().includes(t)||e.barcode&&e.barcode.toLowerCase().includes(t)})),ns=e=>{const t=Dn.map((t=>{const n=e.find((e=>e.productId===t.id));return n?{...t,stock:t.stock-n.quantity}:t}));Mn(t)},as=()=>{if(0===ra.items.length)return void Zt(`⚠️ ${n("pleaseAddProductsToInvoice","يرجى إضافة منتجات للفاتورة")}`,"warning",3e3);const e="دين"===ra.paymentMethod||"Crédit"===ra.paymentMethod||"Credit"===ra.paymentMethod,t="GUEST"===ra.customerId||!ra.customerId||"زبون عابر"===ra.customerName||"Client de passage"===ra.customerName||"Walk-in Customer"===ra.customerName;if(e&&t)return void Zt(`❌ ${n("cannotSaveCreditForWalkInCustomer","لا يمكن حفظ فاتورة دين لزبون عابر")}. ${n("pleaseSelectRegisteredCustomerForCredit","يرجى اختيار زبون مسجل للدفع بالدين")}.`,"error",4e3);if(e&&ra.customerId&&"GUEST"!==ra.customerId){const e=ba.find((e=>e.id===ra.customerId));if(e&&e.creditLimit>0){const t=e.balance+ra.finalTotal;if(t>e.creditLimit){const a=t-e.creditLimit;return void Zt(`❌ ${n("creditLimitExceeded","تم تجاوز حد الائتمان")}! ${n("customerName","العميل")}: ${e.name}\n${n("currentBalance","الرصيد الحالي")}: ${Ut(e.balance)}\n${n("invoiceAmount","مبلغ الفاتورة")}: ${Ut(ra.finalTotal)}\n${n("newBalance","الرصيد الجديد")}: ${Ut(t)}\n${n("creditLimit","حد الائتمان")}: ${Ut(e.creditLimit)}\n${n("exceededAmount","المبلغ المتجاوز")}: ${Ut(a)}`,"error",8e3)}}}for(const l of ra.items){const e=Dn.find((e=>e.id===l.productId));if(!e)return void Zt(`❌ ${n("productNotFoundInSystem","المنتج غير موجود")}: ${l.productName}`,"error",3e3);if(l.quantity>e.stock)return void Zt(`❌ ${n("quantityExceedsStock","الكمية المطلوبة أكبر من المتوفر")}: ${e.name}`,"error",3e3)}const a=ra.customerId||"GUEST",s=ra.customerName||n("walkInCustomer","زبون عابر"),i={...ra,customerId:a,customerName:s,id:ra.invoiceNumber,createdAt:(new Date).toLocaleString("ar-DZ")},r=[i,...Ce];localStorage.setItem("icaldz-invoices",JSON.stringify(r)),Ie(r),ns(ra.items),"دين"===ra.paymentMethod&&ra.customerId&&"GUEST"!==ra.customerId&&La(ra.customerId,ra.finalTotal,"add");const o=s===n("walkInCustomer","زبون عابر")?n("forWalkInCustomer","لزبون عابر"):`${n("forCustomer","للزبون")} ${s}`;j.play("saveInvoice",{showNotification:!1}),Zt(`💾 F2: ${n("salesInvoiceSavedSuccessfully","تم حفظ فاتورة المبيعات رقم")} ${ra.invoiceNumber} ${o} ${n("successfullyAndStockUpdated","بنجاح!")}`,"success",3e3),_t(ra.finalTotal,ra.invoiceNumber),Xa(),F&&setTimeout((()=>{mn(i),j.play("printInvoice",{showNotification:!1}),Zt(`🖨️ F2: ${n("invoicePrintedAutomatically","تم طباعة الفاتورة تلقائياً")}`,"info",2e3)}),500)},ss=()=>{if(0===ra.items.length)return void Zt(`⚠️ ${n("pleaseAddProductsToInvoice","يرجى إضافة منتجات للفاتورة")}`,"warning",3e3);const e="دين"===ra.paymentMethod||"Crédit"===ra.paymentMethod||"Credit"===ra.paymentMethod,t="GUEST"===ra.customerId||!ra.customerId||"زبون عابر"===ra.customerName||"Client de passage"===ra.customerName||"Walk-in Customer"===ra.customerName;if(e&&t)return void Zt(`❌ ${n("cannotSaveCreditForWalkInCustomer","لا يمكن حفظ فاتورة دين لزبون عابر")}`,"error",4e3);if(e&&ra.customerId&&"GUEST"!==ra.customerId){const e=ba.find((e=>e.id===ra.customerId));if(e&&e.creditLimit>0){const t=e.balance+ra.finalTotal;if(t>e.creditLimit){const a=t-e.creditLimit;return void Zt(`❌ ${n("creditLimitExceeded","تم تجاوز حد الائتمان")}! ${n("customerName","العميل")}: ${e.name}\n${n("currentBalance","الرصيد الحالي")}: ${Ut(e.balance)}\n${n("invoiceAmount","مبلغ الفاتورة")}: ${Ut(ra.finalTotal)}\n${n("newBalance","الرصيد الجديد")}: ${Ut(t)}\n${n("creditLimit","حد الائتمان")}: ${Ut(e.creditLimit)}\n${n("exceededAmount","المبلغ المتجاوز")}: ${Ut(a)}`,"error",8e3)}}}const a="GUEST"===ra.customerId?n("walkInCustomer","زبون عابر"):ra.customerName,s=[{id:ra.invoiceNumber,invoiceNumber:ra.invoiceNumber,date:ra.date,customerId:ra.customerId,customerName:a,paymentMethod:ra.paymentMethod,items:ra.items,total:ra.total,discount:ra.discount,tax:ra.tax,finalTotal:ra.finalTotal,createdAt:(new Date).toISOString()},...Ce];localStorage.setItem("icaldz-invoices",JSON.stringify(s)),Ie(s),ns(ra.items),"دين"!==ra.paymentMethod&&"Crédit"!==ra.paymentMethod&&"Credit"!==ra.paymentMethod||!ra.customerId||"GUEST"===ra.customerId||La(ra.customerId,ra.finalTotal,"add");const i=a===n("walkInCustomer","زبون عابر")?n("forWalkInCustomer","لزبون عابر"):`${n("forCustomer","للزبون")} ${a}`;j.play("saveInvoice",{showNotification:!1}),Zt(`💾 ${n("salesInvoiceSavedSuccessfully","تم حفظ فاتورة المبيعات رقم")} ${ra.invoiceNumber} ${i} ${n("successfullyAndStockUpdated","بنجاح وتم تحديث المخزون!")}`,"success",4e3),_t(ra.finalTotal,ra.invoiceNumber),Xa()},is=()=>{if(0===ra.items.length)return void Zt(`⚠️ ${n("pleaseAddProductsToInvoice","يرجى إضافة منتجات للفاتورة")}`,"warning",3e3);const e="دين"===ra.paymentMethod||"Crédit"===ra.paymentMethod||"Credit"===ra.paymentMethod,t="GUEST"===ra.customerId||!ra.customerId||"زبون عابر"===ra.customerName||"Client de passage"===ra.customerName||"Walk-in Customer"===ra.customerName;if(e&&t)return void Zt(`❌ ${n("cannotSaveCreditForWalkInCustomer","لا يمكن حفظ فاتورة دين لزبون عابر")}`,"error",4e3);if(e&&ra.customerId&&"GUEST"!==ra.customerId){const e=ba.find((e=>e.id===ra.customerId));if(e&&e.creditLimit>0){const t=e.balance+ra.finalTotal;if(t>e.creditLimit){const a=t-e.creditLimit;return void Zt(`❌ ${n("creditLimitExceeded","تم تجاوز حد الائتمان")}! ${n("customerName","العميل")}: ${e.name}\n${n("currentBalance","الرصيد الحالي")}: ${Ut(e.balance)}\n${n("invoiceAmount","مبلغ الفاتورة")}: ${Ut(ra.finalTotal)}\n${n("newBalance","الرصيد الجديد")}: ${Ut(t)}\n${n("creditLimit","حد الائتمان")}: ${Ut(e.creditLimit)}\n${n("exceededAmount","المبلغ المتجاوز")}: ${Ut(a)}`,"error",8e3)}}}const a="GUEST"===ra.customerId?n("walkInCustomer","زبون عابر"):ra.customerName,s={id:ra.invoiceNumber,invoiceNumber:ra.invoiceNumber,date:ra.date,customerId:ra.customerId,customerName:a,paymentMethod:ra.paymentMethod,items:ra.items,total:ra.total,discount:ra.discount,tax:ra.tax,finalTotal:ra.finalTotal,createdAt:(new Date).toISOString()},i=[s,...Ce];localStorage.setItem("icaldz-invoices",JSON.stringify(i)),Ie(i),ns(ra.items),"دين"!==ra.paymentMethod&&"Crédit"!==ra.paymentMethod&&"Credit"!==ra.paymentMethod||!ra.customerId||"GUEST"===ra.customerId||La(ra.customerId,ra.finalTotal,"add");const r=a===n("walkInCustomer","زبون عابر")?n("forWalkInCustomer","لزبون عابر"):`${n("forCustomer","للزبون")} ${a}`;j.play("saveInvoice",{showNotification:!1}),Zt(`💾 ${n("salesInvoiceSavedSuccessfully","تم حفظ فاتورة المبيعات رقم")} ${ra.invoiceNumber} ${r} ${n("successfullyAndStockUpdated","بنجاح وتم تحديث المخزون!")}`,"success",4e3),_t(ra.finalTotal,ra.invoiceNumber),setTimeout((()=>{mn(s),j.play("printInvoice",{showNotification:!1}),Zt(`🖨️ ${n("invoiceSentToThermalPrinter","تم إرسال الفاتورة للطباعة الحرارية")}`,"info",3e3)}),500),Xa()},rs=e=>{const t=Dn.find((t=>t.barcode===e&&""!==e.trim()));if(!t)return Zt(`❌ ${n("productNotFound","لم يتم العثور على المنتج بالباركود")}: ${e}`,"error",2e3),Ua(""),void Va(null);const a=ra.items.findIndex((e=>e.productId===t.id));let s;if(a>=0)s=[...ra.items],s[a]={...s[a],quantity:s[a].quantity+1,total:(s[a].quantity+1)*s[a].price};else{const e={productId:t.id,productName:t.name,name:t.name,barcode:t.barcode,price:t.sellPrice||t.salePrice||t.price,quantity:1,total:t.sellPrice||t.salePrice||t.price};s=[...ra.items,e]}const i=s.reduce(((e,t)=>e+t.total),0),r=i*(Pt.taxRate/100),o=i+r-ra.discount;oa({...ra,items:s,total:i,tax:r,finalTotal:o}),Ua(""),clearTimeout(window.salesScannerValidationTimeout),j.play("addProduct")},os=e=>{if(!Zn||K)return;const t=e.target.value,a=$(t);if(Ja(a),a.length>=3){Dn.find((e=>e.barcode===a&&""!==a.trim()))&&cs(a)}else a.length>0&&a.length<3&&(clearTimeout(window.editScannerValidationTimeout),window.editScannerValidationTimeout=setTimeout((()=>{Ga===a&&a.length>0&&a.length<3&&Zt(`❌ ${n("barcodeMinLength","الباركود قصير جداً - يجب أن يكون 3 أحرف على الأقل")}`,"warning",2e3)}),1e3))},ls=e=>{if(Zn&&!K&&"Enter"===e.key&&"keydown"===e.type){e.preventDefault(),e.stopPropagation();const t=e.target.value.trim();t.length>=3?(Ja(""),clearTimeout(window.editScannerValidationTimeout)):t.length>0&&Zt(`❌ ${n("barcodeMinLength","الباركود قصير جداً - يجب أن يكون 3 أحرف على الأقل")}`,"warning",2e3)}},cs=e=>{if(!Jn)return void Zt(`❌ ${n("noInvoiceBeingEdited","لا توجد فاتورة قيد التعديل")}`,"error",2e3);const t=Dn.find((t=>t.barcode===e&&""!==e.trim()));if(!t)return Zt(`❌ ${n("productNotFound","لم يتم العثور على المنتج بالباركود")}: ${e}`,"error",2e3),Ja(""),void Wa(null);if(t.stock<=0)return Zt(`❌ ${n("productOutOfStock","المنتج غير متوفر في المخزون")}: ${t.name}`,"error",3e3),Ja(""),void Wa(null);const a=Jn.items.findIndex((e=>e.productId===t.id));let s;if(a>=0){const e=Jn.items[a],i=e.quantity+1;if(i>t.stock)return Zt(`❌ ${n("quantityExceedsStock","الكمية الإجمالية أكبر من المتوفر")} (${t.stock})`,"error",3e3),void Ja("");s=[...Jn.items],s[a]={...e,quantity:i,total:i*e.price},Zt(`✅ ${n("quantityUpdated","تم تحديث الكمية")}: ${t.name} (${i})`,"success",2e3)}else{const e={id:Date.now(),productId:t.id,productName:t.name,name:t.name,price:t.sellPrice||t.price||0,quantity:1,total:t.sellPrice||t.price||0};s=[...Jn.items,e],Zt(`✅ ${n("productAdded","تم إضافة المنتج")}: ${t.name}`,"success",2e3)}const i=s.reduce(((e,t)=>e+t.total),0),r=i*(Pt.taxRate/100),o=i+r-Jn.discount;_n({...Jn,items:s,total:i,tax:r,finalTotal:o}),Ja(""),clearTimeout(window.editScannerValidationTimeout),j.play("addProduct")},ds=()=>{const e=Dn.map((e=>e.barcode)).filter((e=>e&&/^0{6}\d{3}$/.test(e))).map((e=>parseInt(e,10))).filter((e=>!isNaN(e))).sort(((e,t)=>t-e)),t=e.length>0?e[0]+1:6;return String(t).padStart(9,"0")},ms=()=>{aa(!0),Rn({id:"P"+String(Date.now()).slice(-3),name:"",category:"",barcode:"",buyPrice:0,sellPrice:0,stock:0,minStock:0})},hs=()=>{aa(!1),Rn({id:"",name:"",category:"",barcode:"",buyPrice:0,sellPrice:0,stock:0,minStock:0})},ps=()=>{if(!An.name||!An.category)return void Zt(`⚠️ ${n("fillRequiredFields","يرجى ملء جميع الحقول المطلوبة")}`,"warning",3e3);const e=An.name.trim();if(Dn.find((t=>t.name.toLowerCase().trim()===e.toLowerCase()&&t.id!==An.id)))return void Zt(`❌ ${n("productNameAlreadyExists","اسم المنتج موجود بالفعل")}: "${e}" - ${n("pleaseChooseDifferentName","يرجى اختيار اسم مختلف")}`,"error",4e3);let t=An.barcode;if(t&&""!==t.trim()||(t=ds(),Zt(`🔢 ${n("autoBarcodeGenerated","تم توليد باركود تلقائي")}: ${t}`,"info",2e3)),t&&""!==t.trim()){const e=Dn.find((e=>e.barcode===t&&e.id!==An.id));if(e)return void Zt(`❌ ${n("barcodeAlreadyExists","الباركود موجود بالفعل")}: "${t}" - ${n("usedByProduct","مستخدم بواسطة")}: "${e.name}"`,"error",4e3)}const a=Dn.find((e=>e.barcode===t));if(a){const e={...An,id:a.id,barcode:t,price:An.sellPrice,createdAt:a.createdAt,updatedAt:(new Date).toISOString()},s=Dn.map((t=>t.id===a.id?e:t));Mn(s),Zt(`✅ ${n("productUpdatedSuccessfully","تم تحديث المنتج")} ${An.name} ${n("successfully","بنجاح")}`,"success",3e3)}else{const e={...An,barcode:t,price:An.sellPrice,createdAt:(new Date).toISOString()},a=[...Dn,e];Mn(a),An.barcode&&""!==An.barcode.trim()?Zt(`✅ ${n("productAddedWithScannedBarcode","تم إضافة المنتج")} ${An.name} ${n("withScannedBarcode","بالباركود المسحوب:")} ${t}`,"success",3e3):Zt(`✅ ${n("productAddedWithAutoBarcode","تم إضافة المنتج")} ${An.name} ${n("withAutoBarcode","بباركود تلقائي:")} ${t}`,"success",3e3)}hs()},us=(e,t)=>{if("مدير"!==Ot.role&&"admin"!==Ot.role)return void Zt(`❌ ${n("notAllowedManagerOnly","غير مسموح - المدير فقط يمكنه تعديل المخزون")}`,"error");const a=Dn.map((n=>n.id===e?{...n,stock:t}:n));Mn(a),Zt(`✅ ${n("stockQuantityUpdated","تم تحديث الكمية بنجاح")}`,"success",2e3)},xs=e=>{Rn({...e}),aa(!0)},vs=Dn.filter((e=>{const t=e.name.toLowerCase().includes(sa.toLowerCase())||e.id.toLowerCase().includes(sa.toLowerCase())||e.barcode.includes(sa),n=""===be||e.category===be;let a=!0;if(""!==je){a=(e.stock<=e.minStock?"منخفض":0===e.stock?"نفد":e.stock>2*e.minStock?"مرتفع":"عادي")===je}return t&&n&&a})),gs=e=>{e.preventDefault();const t=localStorage.getItem("icaldz-sellers");let a=[];t?a=JSON.parse(t):(a=[{id:"S001",name:"مدير النظام",username:"admin",password:"admin",phone:"+213 555 000 000",email:"<EMAIL>",role:"admin",isActive:!0,createdAt:(new Date).toISOString()}],localStorage.setItem("icaldz-sellers",JSON.stringify(a)),At(a));const s=a.find((e=>e.username===we.username&&e.password===we.password&&e.isActive));if(s)_(!0),G("dashboard"),Lt(s),ke(!0,"dashboard"),Zt(`🎉 ${n("welcomeUser","مرحباً بك")} ${s.name}!`,"success",3e3);else{a.find((e=>e.username===we.username&&e.password===we.password&&!e.isActive))?Zt(`❌ ${n("accountInactiveContactManager","حسابك غير نشط، يرجى التواصل مع المدير")}`,"error",4e3):Zt(`❌ ${n("invalidUsernameOrPassword","اسم المستخدم أو كلمة المرور غير صحيحة")}`,"error",3e3)}},bs=e=>{G(e),J&&ke(!0,e)},fs=()=>{Y(null),H(!1)};return e.useEffect((()=>{const e=e=>{if(A){if((e.key.startsWith("F")||"Escape"===e.key)&&(e.preventDefault(),e.stopPropagation()),"F1"===e.key)return Qa(),void Zt(`🛒 F1: ${n("f1NewSalesInvoiceOpened","تم فتح فاتورة مبيعات جديدة")}`,"success",2e3);if("F6"!==e.key)if("F7"!==e.key)if("F2"!==e.key)if("F3"!==e.key)if("Enter"!==e.key)"Escape"!==e.key||(K?(Ka(),Zt("❌ تم إغلاق نافذة الفاتورة","info",2e3)):$a?(za(),Zt("❌ تم إغلاق نافذة تعديل الزبون","info",2e3)):na?(hs(),Zt("❌ تم إغلاق نافذة المنتج","info",2e3)):Q?(fs(),Zt("❌ تم إغلاق نافذة الزبون","info",2e3)):ee&&(fn(),Zt("❌ تم إغلاق نافذة المشتريات","info",2e3)));else{if("dashboard"===Z&&document.activeElement&&document.activeElement.classList.contains("barcode-input"))return;if(document.activeElement&&(document.activeElement.classList.contains("barcode-input")||document.activeElement.classList.contains("barcode-input-field")))return;K?e.shiftKey?(is(),Zt(`🖨️ ${n("shiftEnterSaveAndPrint","Shift+Enter: تم حفظ الفاتورة وإرسالها للطباعة الحرارية")}`,"success",3e3)):(ss(),Zt(`💾 ${n("enterSaveOnly","Enter: تم حفظ الفاتورة")}`,"success",2e3)):na?(ps(),Zt("✅ Enter: تم حفظ المنتج","success",2e3)):ee&&(yn(),Zt("💾 Enter: تم حفظ فاتورة المشتريات","success",2e3))}else K?(es(),Zt(`➕ F3: ${n("f3ProductAdded","تم إضافة المنتج")}`,"success",2e3)):ee?(jn(),Zt(`➕ F3: ${n("f3ProductAddedToPurchase","تم إضافة المنتج للمشتريات")}`,"success",2e3)):Zt(`⚠️ F3 ${n("f3OnlyAvailableInInvoice","متاح فقط في نافذة الفاتورة")}`,"warning",2e3);else K||na||ee||(Qa(),Zt(`🛒 F2: ${n("f2NewInvoiceOpened","تم فتح فاتورة جديدة")}`,"success",2e3));else"purchases"===Z?(bn(),Zt(`🛒 F7: ${n("f7NewPurchaseInvoiceOpened","تم فتح فاتورة مشتريات جديدة")}`,"success",2e3)):(G("purchases"),setTimeout((()=>{bn(),Zt(`🛒 F7: ${n("f7NavigatedToPurchases","تم الانتقال لإدارة المشتريات وفتح فاتورة مشتريات جديدة")}`,"success",2e3)}),100));else"inventory"===Z?(ms(),Zt(`📦 F6: ${n("f6NewProductOpened","تم فتح نافذة إضافة منتج جديد")}`,"success",2e3)):(G("inventory"),setTimeout((()=>{ms(),Zt(`📦 F6: ${n("f6NavigatedToInventory","تم الانتقال لإدارة المخزون وفتح نافذة إضافة منتج جديد")}`,"success",2e3)}),100))}};return document.addEventListener("keydown",e,!0),()=>{document.removeEventListener("keydown",e,!0)}}),[A,K,na,Q,$a,ee,Z,la,ra,as,ss,is,es,jn,ps,yn,Qa,ms,bn,Ha,hs,fs,za,fn,Zt]),a?J?t.jsxs("div",{className:"accounting-system",children:[t.jsx("div",{className:"top-header",children:t.jsxs("div",{className:"header-content",children:[t.jsxs("div",{className:"store-logo-section",children:[t.jsx("img",{src:"./assets/logo2png.png",alt:Pt.storeName,className:"header-logo",onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="block"}}),t.jsx("div",{className:"header-logo-fallback",style:{display:"none"},children:"🏛️"})]}),t.jsxs("div",{className:"store-info-section",children:[t.jsx("h1",{className:"store-name",children:Pt.storeName}),t.jsx("p",{className:"store-number",children:Pt.storePhone})]})]})}),t.jsxs("div",{className:"main-content-wrapper",children:[t.jsxs("div",{className:`sidebar page-${Z}`,children:[t.jsxs("div",{className:"sidebar-header",children:[t.jsxs("div",{className:"system-logo",children:[t.jsx("img",{src:"./assets/logo2png.png",alt:"نظام المحاسبي",className:"logo-image",onError:e=>{e.target.style.display="none",e.target.nextSibling.style.display="block"}}),t.jsx("div",{className:"logo-fallback",style:{display:"none"},children:"🏛️"})]}),t.jsxs("div",{className:"system-title",children:[t.jsx("h3",{children:n("systemName","نظام المحاسبي")}),t.jsxs("span",{children:[n("version","الإصدار")," 1.0"]})]})]}),t.jsxs("nav",{className:"sidebar-nav",children:[t.jsxs("button",{className:"nav-item "+("dashboard"===Z?"active":""),onClick:()=>bs("dashboard"),children:[t.jsx("span",{className:"nav-icon",children:"🏠"}),n("dashboard","لوحة التحكم")]}),t.jsxs("button",{className:"nav-item "+("sales"===Z?"active":""),onClick:()=>bs("sales"),children:[t.jsx("span",{className:"nav-icon",children:"🛒"}),n("sales","المبيعات")]}),t.jsxs("button",{className:"nav-item "+("purchases"===Z?"active":""),onClick:()=>bs("purchases"),children:[t.jsx("span",{className:"nav-icon",children:"📦"}),n("purchases","المشتريات")]}),t.jsxs("button",{className:"nav-item "+("customers"===Z?"active":""),onClick:()=>bs("customers"),children:[t.jsx("span",{className:"nav-icon",children:"👥"}),n("customers","العملاء")]}),t.jsxs("button",{className:"nav-item "+("inventory"===Z?"active":""),onClick:()=>bs("inventory"),children:[t.jsx("span",{className:"nav-icon",children:"📊"}),n("products","المخزون")]}),t.jsxs("button",{className:"nav-item "+("reports"===Z?"active":""),onClick:()=>bs("reports"),children:[t.jsx("span",{className:"nav-icon",children:"📈"}),n("reports","التقارير")]}),t.jsxs("button",{className:"nav-item "+("settings"===Z?"active":""),onClick:()=>bs("settings"),children:[t.jsx("span",{className:"nav-icon",children:"⚙️"}),n("settings","الإعدادات")]})]}),t.jsxs("div",{className:"sidebar-footer",children:[t.jsxs("div",{className:"user-info "+("ar"!==y?"user-info-left":""),children:[t.jsx("div",{className:"user-avatar",children:"👤"}),t.jsxs("div",{className:"user-details",children:[t.jsx("span",{children:"المدير"===Ot.name?n("manager","المدير"):Ot.name||n("systemUser","مستخدم النظام")}),t.jsx("small",{children:"admin"===Ot.role||"مدير"===Ot.role?n("systemManager","مدير النظام"):n("seller","بائع")})]})]}),t.jsxs("div",{className:"system-controls",children:[t.jsx("button",{className:"control-btn "+(M?"active":"inactive"),onClick:()=>{const e=j.toggle();E(e),Zt(e?`🔊 ${n("soundEnabled","تم تفعيل الأصوات")}`:`🔇 ${n("soundDisabled","تم إيقاف الأصوات")}`,"info",2e3)},title:M?n("disableSounds","إيقاف الأصوات"):n("enableSounds","تفعيل الأصوات"),children:M?"🔊":"🔇"}),t.jsx("button",{className:"control-btn "+(A?"active":"inactive"),onClick:()=>{const e=!A;R(e),localStorage.setItem("shortcutsEnabled",JSON.stringify(e)),Zt(e?`⌨️ ${n("keyboardShortcutsEnabled","تم تفعيل اختصارات لوحة المفاتيح")}`:`🚫 ${n("keyboardShortcutsDisabled","تم إيقاف اختصارات لوحة المفاتيح")}`,"info",3e3)},title:A?n("disableKeyboardShortcuts","إيقاف اختصارات لوحة المفاتيح"):n("enableKeyboardShortcuts","تفعيل اختصارات لوحة المفاتيح"),children:A?"⌨️":"🚫"}),t.jsx("button",{className:"control-btn "+(F?"active":"inactive"),onClick:()=>{const e=!F;O(e),localStorage.setItem("printerEnabled",JSON.stringify(e)),Zt(e?`🖨️ ${n("printerEnabled","تم تفعيل الطابعة")}`:`🚫 ${n("printerDisabled","تم إيقاف الطابعة")}`,"info",2e3)},title:F?n("disablePrinter","إيقاف الطابعة"):n("enablePrinter","تفعيل الطابعة"),children:F?"🖨️":"🚫"}),t.jsx("button",{className:"control-btn "+(L?"active":"inactive"),onClick:()=>{const e=!L;z(e),localStorage.setItem("notificationsEnabled",JSON.stringify(e)),Zt(e?`🔔 ${n("notificationsEnabled","تم تفعيل الإشعارات")}`:`🔕 ${n("notificationsDisabled","تم إيقاف الإشعارات")}`,"info",2e3)},title:L?n("disableNotifications","إيقاف الإشعارات"):n("enableNotifications","تفعيل الإشعارات"),children:L?"🔔":"🔕"})]}),t.jsxs("button",{className:"logout-btn",onClick:()=>{_(!1),G("login"),Se({username:"",password:""}),ke(!1,"login")},children:["🚪 ",n("logout","خروج")]})]})]}),t.jsxs("main",{className:"main-content",children:["dashboard"===Z&&t.jsxs("div",{className:"dashboard",children:[t.jsx("div",{className:"page-header "+("ar"!==y?"page-header-ltr-split":""),children:t.jsx("div",{className:"page-title-section",children:t.jsxs("h1",{children:["🏠 ",n("dashboard","لوحة التحكم")]})})}),t.jsx("div",{className:"dashboard-scanner-lcd-unified",children:t.jsxs("div",{className:"unified-frame",children:[t.jsxs("div",{className:"scanner-section",children:[t.jsxs("h3",{children:["📷 ",n("scanBarcode","مسح الباركود")," - ",t.jsx("span",{className:"scanner-status-active",children:n("active","نشط")})]}),t.jsxs("div",{className:"barcode-input-container",children:[t.jsx("span",{className:"barcode-icon",children:"📷"}),t.jsx("input",{type:"text",placeholder:n("scanBarcodeToAddProduct","امسح الباركود - اضغط Enter لفتح الفاتورة"),value:De,onChange:e=>{if(K||Zn)return;const t=e.target.value,n=$(t);if(Te(n),n.length>=3&&(clearTimeout(window.dashboardScannerTimeout),window.dashboardScannerTimeout=setTimeout((()=>{const e=Dn.find((e=>e.barcode===n&&""!==n.trim()));e&&Ee({productName:e.name,price:e.sellPrice,barcode:e.barcode,showPrice:!0})}),100)),n.length>0&&Ae&&(Re(null),Fe&&(clearTimeout(Fe),Oe(null))),n.length>=3){Dn.find((e=>e.barcode===n&&""!==n.trim()))||Dn.map((e=>e.barcode)).filter((e=>e))}},onKeyDown:e=>{if(!K&&!Zn&&"Enter"===e.key&&"keydown"===e.type){e.preventDefault(),e.stopPropagation();const t=e.target.value.trim();if(t.length>=3){const e=Dn.find((e=>e.barcode===t&&""!==t.trim()));if(e){Te("");const t={invoiceNumber:"INV-"+Date.now(),date:(new Date).toISOString().split("T")[0],customerId:"GUEST",customerName:n("walkInCustomer","زبون عابر"),paymentMethod:n("cash","نقداً"),items:[],total:0,discount:0,tax:0,finalTotal:0};Ua(""),Ja(""),oa(t),X(!0),setTimeout((()=>{const n={productId:e.id,productName:e.name,name:e.name,quantity:1,price:e.sellPrice||e.price,total:e.sellPrice||e.price},a=n.total,s=a*(Pt.taxRate/100),i=a+s;oa({...t,items:[n],total:a,tax:s,finalTotal:i})}),300),Zt(`🛒 ${n("openingSalesInvoice","فتح فاتورة المبيعات")} - ${e.name}`,"success",2e3)}else Zt(`❌ ${n("productNotFound","لم يتم العثور على المنتج بالباركود")}: ${t}`,"error",3e3)}else Zt(`❌ ${n("barcodeMinLength","الباركود قصير جداً - يجب أن يكون 3 أحرف على الأقل")}`,"warning",2e3)}},onFocus:()=>{window.barcodeShortcutManager&&(window.barcodeShortcutManager.isBarcodeActive=!0,window.barcodeShortcutManager.setShortcutsEnabled(!1))},onBlur:()=>{setTimeout((()=>{window.barcodeShortcutManager&&!window.barcodeShortcutManager.checkBarcodeInput(document.activeElement)&&(window.barcodeShortcutManager.isBarcodeActive=!1,window.barcodeShortcutManager.setShortcutsEnabled(!0))}),100)},className:"barcode-input",ref:Pe,autoFocus:!0})]}),t.jsxs("div",{className:"barcode-actions",children:[t.jsxs("button",{type:"button",className:"btn btn-success btn-sm",onClick:e=>{e.preventDefault(),e.stopPropagation();const t=De.trim();if(t&&w(t)){const e=Dn.find((e=>e.barcode===t&&""!==t.trim()));if(e){Te("");const t={invoiceNumber:"INV-"+Date.now(),date:(new Date).toISOString().split("T")[0],customerId:"GUEST",customerName:n("walkInCustomer","زبون عابر"),paymentMethod:"نقداً",items:[],total:0,discount:0,tax:0,finalTotal:0};Ua(""),oa(t),X(!0),setTimeout((()=>{const n={productId:e.id,productName:e.name,quantity:1,price:e.sellPrice||e.price,total:e.sellPrice||e.price},a=n.total,s=a*(Pt.taxRate/100),i=a+s;oa({...t,items:[n],total:a,tax:s,finalTotal:i})}),300),Zt(`🛒 ${n("openingSalesInvoice","فتح فاتورة المبيعات")} - ${e.name}`,"success",2e3)}else Zt(`❌ ${n("productNotFound","لم يتم العثور على المنتج بالباركود")}: ${t}`,"error",2e3)}else Zt(`⚠️ ${n("pleaseEnterBarcode","يرجى إدخال الباركود أولاً")}`,"warning",2e3)},title:n("openInvoiceWithProduct","فتح الفاتورة مع المنتج"),children:["🛒 ",n("openInvoice","فتح الفاتورة")]}),t.jsxs("button",{type:"button",className:"btn btn-warning btn-sm",onClick:()=>{Te("")},title:n("clearBarcode","مسح الباركود"),children:["🗑️ ",n("clear","مسح")]}),t.jsx("button",{type:"button",className:"btn btn-info btn-sm",onClick:()=>{["*********","*********","ABC123","test123","12","a"].forEach((e=>{w(e),$(e)})),Zt("🔍 Debug info logged to console - Press F12 to view","info",3e3)},title:"Debug barcode scanning",children:"🔍 Debug"}),t.jsxs("button",{type:"button",className:"btn btn-danger btn-sm",onClick:()=>{C(),Te(""),Ua(""),Ja(""),Ee(null),Va(null),Wa(null),k({lastActivity:Date.now(),totalScans:0,errors:0,isHealthy:!0,systemStatus:"active",lastContext:"manual_reset"}),window.barcodeShortcutManager&&(window.barcodeShortcutManager.isBarcodeActive=!1,window.barcodeShortcutManager.setShortcutsEnabled(!0)),setTimeout((()=>{"dashboard"===Z&&Pe.current&&Pe.current.focus()}),500),Zt("🔧 نظام الباركود تم إعادة تعيينه بنجاح","success",3e3)},title:n("resetScannerSystem","إعادة تعيين نظام الباركود"),children:["🔧 ",n("reset","إعادة تعيين")]})]}),t.jsxs("small",{className:"barcode-help",style:{color:"#7fb3d3",marginTop:"10px",display:"block",textAlign:"center",fontSize:"12px",fontStyle:"italic"},children:["💡 ",n("dashboardBarcodeHelp","امسح الباركود لعرض المعلومات، اضغط Enter أو زر فتح الفاتورة لإضافة المنتج للفاتورة")]})]}),t.jsx("div",{className:"lcd-section",children:t.jsxs("div",{className:"lcd-screen",children:[t.jsxs("div",{className:"lcd-header",children:[t.jsxs("span",{className:"lcd-title",children:["💰 ",n("totalFinal","المجموع النهائي")]}),t.jsx("span",{className:"lcd-status",children:Ae||Le?"●":"○"})]}),t.jsx("div",{className:"lcd-content",children:Ae?t.jsxs("div",{className:"total-final-display",children:[t.jsxs("div",{className:"total-final-label",children:[n("finalTotal","المجموع النهائي"),":"]}),t.jsx("div",{className:"total-final-amount",children:Ut(Ae.finalTotal)}),t.jsx("div",{className:"total-final-currency",children:"DZD"}),t.jsxs("div",{className:"invoice-info",children:[t.jsx("div",{className:"invoice-number",children:Ae.invoiceNumber}),t.jsx("div",{className:"timestamp",children:Ae.timestamp})]})]}):Le?t.jsxs("div",{className:"total-final-display",children:[t.jsxs("div",{className:"total-final-label",children:[n("lastInvoice","آخر فاتورة"),":"]}),t.jsx("div",{className:"total-final-amount",children:Ut(Le.finalTotal)}),t.jsx("div",{className:"total-final-currency",children:"DZD"}),t.jsxs("div",{className:"invoice-info",children:[t.jsx("div",{className:"invoice-number",children:Le.invoiceNumber}),t.jsxs("div",{className:"timestamp",children:[Le.date," - ",Le.timestamp]})]})]}):t.jsxs("div",{className:"display-placeholder",children:[t.jsx("div",{className:"placeholder-text",children:n("scanBarcodeToOpenInvoice","امسح الباركود واضغط Enter لفتح الفاتورة")}),t.jsxs("div",{className:"placeholder-price-big",children:["--- ",Pt.currency]}),t.jsxs("div",{className:"placeholder-scanner-hint",children:["📷 ",n("scannerActive","الماسح نشط")]})]})})]})})]})}),t.jsxs("div",{className:"dashboard-action-buttons",children:[t.jsxs("button",{className:"action-btn action-btn-sales",onClick:Qa,title:"F1",children:[t.jsx("div",{className:"btn-shortcut",children:"F1"}),t.jsx("div",{className:"btn-icon",children:"🛒"}),t.jsx("div",{className:"btn-text",children:n("newSalesInvoice","فاتورة مبيعات جديدة")})]}),t.jsxs("button",{className:"action-btn action-btn-product",onClick:ms,title:"F6",children:[t.jsx("div",{className:"btn-shortcut",children:"F6"}),t.jsx("div",{className:"btn-icon",children:"📦"}),t.jsx("div",{className:"btn-text",children:n("addNewProduct","إضافة منتج جديد")})]}),t.jsxs("button",{className:"action-btn action-btn-purchase",onClick:()=>{G("purchases"),setTimeout((()=>{bn()}),100)},title:"F7",children:[t.jsx("div",{className:"btn-shortcut",children:"F7"}),t.jsx("div",{className:"btn-icon",children:"📦"}),t.jsx("div",{className:"btn-text",children:n("newPurchaseInvoice","فاتورة مشتريات جديدة")})]})]}),t.jsx("div",{className:"stats-grid",children:va.map((e=>t.jsxs("div",{className:`stat-card ${e.color}`,children:[t.jsx("div",{className:"stat-icon",children:e.icon}),t.jsxs("div",{className:"stat-content",children:[t.jsx("h3",{children:e.title}),t.jsxs("div",{className:"stat-value",children:[e.value," ",t.jsx("span",{className:"stat-unit",children:e.unit})]})]})]},e.id)))}),t.jsxs("div",{className:"dashboard-section",children:[t.jsx("h2",{children:n("recentInvoicesAndOperations","آخر الفواتير والعمليات")}),t.jsx("div",{className:"table-container",children:t.jsxs("table",{className:"data-table",children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:n("invoiceNumber","رقم الفاتورة")}),t.jsx("th",{children:n("date","التاريخ")}),t.jsx("th",{children:n("supplier","المورد")}),t.jsx("th",{children:n("amountPaid","المبلغ المدفوع")}),t.jsx("th",{children:n("totalAmount","المبلغ الإجمالي")}),t.jsx("th",{children:n("status","الحالة")})]})}),t.jsx("tbody",{children:ga.map((e=>t.jsxs("tr",{children:[t.jsx("td",{children:e.id}),t.jsx("td",{children:e.date}),t.jsx("td",{children:qt(e.supplier)}),t.jsx("td",{children:Ut(e.paid)}),t.jsx("td",{children:Ut(e.amount)}),t.jsx("td",{children:t.jsx("span",{className:"status "+("مدفوعة"===e.status?"paid":"partial"),children:"مدفوعة"===e.status?n("paid","مدفوعة"):e.status})})]},e.id)))})]})}),t.jsxs("div",{className:"actions-header",children:[t.jsx("h3",{children:n("quickOperations","العمليات السريعة")}),t.jsx("p",{children:n("chooseOperation","اختر العملية التي تريد تنفيذها")})]}),t.jsxs("div",{className:"table-actions",children:[t.jsxs("button",{className:"btn btn-success btn-large quick-operation-btn",onClick:Qa,title:n("newSalesInvoice","إنشاء فاتورة بيع جديدة"),children:[t.jsx("span",{className:"btn-icon",children:"🛒"}),t.jsx("span",{className:"btn-text",children:n("newSalesInvoice","فاتورة مبيعات جديدة")})]}),t.jsxs("button",{className:"btn btn-primary quick-operation-btn",onClick:()=>{G("purchases"),setTimeout((()=>{bn()}),100),Zt(`📄 ${n("f7NewPurchaseInvoiceOpened","تم فتح فاتورة مشتريات جديدة")}`,"success",2e3)},title:n("newPurchaseInvoice","إنشاء فاتورة مشتريات جديدة"),children:[t.jsx("span",{className:"btn-icon",children:"📄"}),t.jsx("span",{className:"btn-text",children:n("newPurchaseInvoice","فاتورة مشتريات جديدة")})]}),t.jsxs("button",{className:"btn btn-secondary quick-operation-btn",onClick:()=>{G("purchases"),Zt(`📊 ${n("purchaseReportClicked","تم الانتقال لصفحة المشتريات")}`,"success",2e3)},title:n("purchaseReport","عرض تقرير المشتريات"),children:[t.jsx("span",{className:"btn-icon",children:"📊"}),t.jsx("span",{className:"btn-text",children:n("purchaseReport","تقرير المشتريات")})]}),t.jsxs("button",{className:"btn btn-info quick-operation-btn",onClick:()=>{"مدير"===Ot.role||"admin"===Ot.role?(G("reports"),Zt(`📈 ${n("purchaseStatisticsClicked","تم الانتقال لصفحة التقارير والإحصائيات")}`,"success",2e3)):Zt(`🔒 ${n("reportsManagerOnly","التقارير والإحصائيات متاحة للمدير فقط")}`,"warning",3e3)},title:n("purchaseStatistics","عرض إحصائيات المشتريات"),children:[t.jsx("span",{className:"btn-icon",children:"📈"}),t.jsx("span",{className:"btn-text",children:n("purchaseStatistics","إحصائيات المشتريات")})]})]})]})]}),"customers"===Z&&t.jsxs("div",{className:"customers-page",children:[t.jsxs("div",{className:"page-header "+("ar"!==y?"page-header-ltr-split":""),children:[t.jsx("div",{className:"page-title-section",children:t.jsxs("h1",{children:["👥 ",n("customersManagement","إدارة الزبائن")]})}),t.jsx("div",{className:"page-description-section",children:t.jsxs("button",{className:"btn btn-primary",onClick:()=>H(!0),children:["+ ",n("addNewCustomer","إضافة زبون جديد")]})})]}),t.jsxs("div",{className:"stats-grid",children:[t.jsxs("div",{className:"stat-card",children:[t.jsx("div",{className:"stat-icon",children:t.jsx("i",{className:"fas fa-users"})}),t.jsxs("div",{className:"stat-info",children:[t.jsx("h3",{children:n("totalCustomers","إجمالي الزبائن")}),t.jsx("p",{children:ba.length})]})]}),t.jsxs("div",{className:"stat-card",children:[t.jsx("div",{className:"stat-icon",children:t.jsx("i",{className:"fas fa-money-bill-wave"})}),t.jsxs("div",{className:"stat-info",children:[t.jsx("h3",{children:n("totalDues","إجمالي المستحقات")}),t.jsxs("p",{children:[ba.reduce(((e,t)=>e+Math.max(0,t.balance)),0).toLocaleString()," ",Pt.currency]})]})]}),t.jsxs("div",{className:"stat-card",children:[t.jsx("div",{className:"stat-icon",children:t.jsx("i",{className:"fas fa-exclamation-triangle"})}),t.jsxs("div",{className:"stat-info",children:[t.jsx("h3",{children:n("debtorCustomers","زبائن مدينين")}),t.jsx("p",{children:ba.filter((e=>e.balance>0)).length})]})]})]}),ba.length>0&&t.jsxs("div",{className:"bulk-actions "+($t.length>0?"has-selection":""),children:[t.jsxs("div",{className:"select-all-container",children:[t.jsx("input",{type:"checkbox",className:"select-all-checkbox",checked:ba.length>0&&$t.length===ba.length,onChange:()=>Wt("customers",ba),id:"select-all-customers"}),t.jsx("label",{htmlFor:"select-all-customers",className:"select-all-label",children:n("selectAll","تحديد الكل")}),$t.length>0&&t.jsxs("span",{className:"selected-count",children:["(",$t.length," ",n("selected","محدد"),")"]})]}),$t.length>0&&("مدير"===Ot.role||"admin"===Ot.role)&&t.jsxs("button",{className:"bulk-delete-btn",onClick:()=>Qt("customers"),title:n("deleteSelected","حذف المحدد"),children:["🗑️ ",n("deleteSelected","حذف المحدد")," (",$t.length,")"]})]}),t.jsx("div",{className:"inventory-controls",children:t.jsxs("div",{className:"search-section",children:[t.jsx("input",{type:"text",className:"search-input",placeholder:`🔍 ${n("searchCustomers","البحث في الزبائن (الاسم، الهاتف، البريد الإلكتروني)...")}`,value:Ca,onChange:e=>Ia(e.target.value)}),Ca&&t.jsx("button",{className:"btn btn-secondary btn-xs",onClick:()=>Ia(""),title:n("clearFilters","مسح الفلاتر"),children:"🔄"})]})}),t.jsx("div",{className:"table-container",children:t.jsxs("table",{className:"data-table "+("ar"!==y?"table-ltr":""),children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{className:"checkbox-column",children:t.jsx("input",{type:"checkbox",checked:ba.length>0&&$t.length===ba.length,onChange:()=>Wt("customers",ba),title:n("selectAll","تحديد الكل")})}),t.jsx("th",{children:n("customerNumber","رقم الزبون")}),t.jsx("th",{children:n("customerName","اسم الزبون")}),t.jsx("th",{children:n("phone","الهاتف")}),t.jsx("th",{children:n("email","البريد الإلكتروني")}),t.jsx("th",{children:n("company","الشركة")}),t.jsx("th",{children:n("balance","الرصيد")}),t.jsx("th",{children:n("status","الحالة")}),t.jsx("th",{children:n("profitMarginDiscount","خصم (%)")}),t.jsx("th",{children:n("actions","الإجراءات")})]})}),t.jsx("tbody",{children:0===Da.length?t.jsx("tr",{children:t.jsx("td",{colSpan:"10",style:{textAlign:"center",padding:"20px"},children:Ca.trim()?`${n("noCustomersMatchingFilter","لا توجد زبائن مطابقين للفلتر")}: "${Ca}"`:n("noCustomersAdded","لا توجد زبائن مضافين")})}):Da.map((e=>t.jsxs("tr",{className:$t.includes(e.id)?"selected":"",children:[t.jsx("td",{className:"checkbox-column",children:t.jsx("input",{type:"checkbox",className:"row-checkbox",checked:$t.includes(e.id),onChange:()=>Yt("customers",e.id)})}),t.jsx("td",{children:e.id}),t.jsx("td",{children:e.name}),t.jsx("td",{children:e.phone}),t.jsx("td",{children:e.email}),t.jsx("td",{children:e.company||"-"}),t.jsxs("td",{className:e.balance<=0?"text-success":"text-danger",children:[e.balance.toLocaleString()," ",Pt.currency]}),t.jsx("td",{children:t.jsx("span",{className:"status-badge "+("نشط"===e.status?"active":"inactive"),children:"نشط"===e.status?n("active","نشط"):n("inactive","غير نشط")})}),t.jsx("td",{children:t.jsxs("span",{className:"discount-badge",children:[e.discountPercentage||0,"%"]})}),t.jsx("td",{children:t.jsxs("div",{className:"action-buttons-group",children:[t.jsx("button",{className:"btn btn-info btn-xs",onClick:()=>(e=>{const t=Ce.filter((t=>{const n=t.customerName===e.name,a=t.customerId===e.id,s=t.customer===e.name,i=t.customerName&&t.customerName.toLowerCase().trim()===e.name.toLowerCase().trim();return n||a||s||i})),n=JSON.parse(localStorage.getItem("icaldz-payments")||"[]").filter((t=>{const n=t.customerId===e.id,a=t.customerName===e.name,s=t.customerName&&t.customerName.toLowerCase().trim()===e.name.toLowerCase().trim();return n||a||s})),a=[...t.map((e=>({...e,type:"invoice",operationType:"sale"}))),...n.map((e=>({...e,type:"payment",operationType:"payment",invoiceNumber:e.id,finalTotal:-e.amount,paymentMethod:e.paymentMethod})))].sort(((e,t)=>new Date(t.date)-new Date(e.date))),s=a.length,i=t.reduce(((e,t)=>e+t.finalTotal),0),r=n.reduce(((e,t)=>e+t.amount),0),o=t.filter((e=>"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod)),l=t.filter((e=>"دين"===e.paymentMethod||"Dette"===e.paymentMethod||"Credit"===e.paymentMethod)),c=o.reduce(((e,t)=>e+t.finalTotal),0),d=l.reduce(((e,t)=>e+t.finalTotal),0);ie({customer:e,invoices:t,payments:n,allOperations:a,totalOperations:s,totalAmount:i,totalPayments:r,cashOperations:o,creditOperations:l,totalCash:c,totalCredit:d}),ae(!0)})(e),title:n("viewCustomerOperations","عرض عمليات الزبون"),children:"👁️"}),e.balance>0&&t.jsx("button",{className:"btn btn-success btn-xs",onClick:()=>Fa(e),title:n("payCustomerDebt","تسديد فواتير الزبون"),children:"💰"}),t.jsx("button",{className:"btn btn-primary btn-xs",onClick:()=>(e=>{ka({...e}),wa(!0)})(e),title:n("editCustomer","تعديل الزبون"),children:"✏️"}),("مدير"===Ot.role||"admin"===Ot.role)&&t.jsx("button",{className:"btn btn-danger btn-xs",onClick:()=>(e=>{const t=ba.find((t=>t.id===e));ce(t),oe(!0)})(e.id),title:n("deleteCustomer","حذف الزبون"),children:"🗑️"})]})})]},e.id)))})]})})]}),"purchases"===Z&&t.jsxs("div",{className:"purchases-page",children:[t.jsxs("div",{className:"page-header "+("ar"!==y?"page-header-ltr-split":""),children:[t.jsx("div",{className:"page-title-section",children:t.jsxs("h1",{children:["📦 ",n("purchaseManagement","إدارة المشتريات")]})}),t.jsx("div",{className:"page-description-section",children:t.jsxs("button",{className:"btn btn-primary",onClick:bn,children:["📦 ",n("newPurchaseInvoice","فاتورة مشتريات جديدة")]})})]}),t.jsxs("div",{className:"purchase-summary",children:[t.jsxs("div",{className:"summary-card orange",children:[t.jsx("h3",{children:n("totalPurchases","إجمالي المشتريات")}),t.jsx("div",{className:"summary-value",children:Ut(Ke.reduce(((e,t)=>e+t.finalTotal),0))})]}),t.jsxs("div",{className:"summary-card blue",children:[t.jsx("h3",{children:n("purchaseInvoiceCount","عدد فواتير المشتريات")}),t.jsx("div",{className:"summary-value",children:Ke.length})]}),t.jsxs("div",{className:"summary-card green",children:[t.jsx("h3",{children:n("averagePurchaseInvoice","متوسط فاتورة المشتريات")}),t.jsx("div",{className:"summary-value",children:Ke.length>0?Ut(Ke.reduce(((e,t)=>e+t.finalTotal),0)/Ke.length):Ut(0)})]}),t.jsxs("div",{className:"summary-card purple",children:[t.jsx("h3",{children:n("creditPurchaseInvoices","فواتير دين")}),t.jsx("div",{className:"summary-value",children:Ke.filter((e=>"دين"===e.paymentMethod||"Dette"===e.paymentMethod||"Credit"===e.paymentMethod)).length})]})]}),Ra.length>0&&t.jsxs("div",{className:"bulk-actions "+(yt.length>0?"has-selection":""),children:[t.jsxs("div",{className:"select-all-container",children:[t.jsx("input",{type:"checkbox",className:"select-all-checkbox",checked:Ra.length>0&&yt.length===Ra.length,onChange:()=>Wt("purchases",Ra),id:"select-all-purchases"}),t.jsx("label",{htmlFor:"select-all-purchases",className:"select-all-label",children:n("selectAll","تحديد الكل")}),yt.length>0&&t.jsxs("span",{className:"selected-count",children:["(",yt.length," ",n("selected","محدد"),")"]})]}),yt.length>0&&("مدير"===Ot.role||"admin"===Ot.role)&&t.jsxs("button",{className:"bulk-delete-btn",onClick:()=>Qt("purchases"),title:n("deleteSelected","حذف المحدد"),children:["🗑️ ",n("deleteSelected","حذف المحدد")," (",yt.length,")"]})]}),t.jsx("div",{className:"inventory-controls",children:t.jsxs("div",{className:"search-section",children:[t.jsx("input",{type:"text",className:"search-input",placeholder:`🔍 ${n("searchPurchaseInvoices","البحث في فواتير المشتريات (رقم الفاتورة، اسم المورد)...")}`,value:Ea,onChange:e=>Aa(e.target.value)}),Ea&&t.jsx("button",{className:"btn btn-secondary btn-xs",onClick:()=>Aa(""),title:n("clearFilters","مسح الفلاتر"),children:"🔄"})]})}),t.jsx("div",{className:"table-container",children:t.jsxs("table",{className:"data-table",children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{className:"checkbox-column",children:t.jsx("input",{type:"checkbox",checked:Ra.length>0&&yt.length===Ra.length,onChange:()=>Wt("purchases",Ra),title:n("selectAll","تحديد الكل")})}),t.jsx("th",{children:n("invoiceNumber","رقم الفاتورة")}),t.jsx("th",{children:n("date","التاريخ")}),t.jsx("th",{children:n("supplier","المورد")}),t.jsx("th",{children:n("paymentMethod","طريقة الدفع")}),t.jsx("th",{children:n("amount","المبلغ")}),t.jsx("th",{children:n("status","الحالة")}),t.jsx("th",{children:n("actions","الإجراءات")})]})}),t.jsx("tbody",{children:0===Ra.length?t.jsx("tr",{children:t.jsx("td",{colSpan:"8",style:{textAlign:"center",padding:"20px"},children:Ea.trim()?`${n("noPurchaseInvoicesMatchingFilter","لا توجد فواتير مشتريات مطابقة للفلتر")}: "${Ea}"`:n("noPurchaseInvoicesSaved","لا توجد فواتير مشتريات محفوظة")})}):Ra.map((e=>t.jsxs("tr",{className:yt.includes(e.id)?"selected":"",children:[t.jsx("td",{className:"checkbox-column",children:t.jsx("input",{type:"checkbox",className:"row-checkbox",checked:yt.includes(e.id),onChange:()=>Yt("purchases",e.id)})}),t.jsx("td",{children:e.invoiceNumber}),t.jsx("td",{children:e.date}),t.jsx("td",{children:Vt(e.customerName)}),t.jsx("td",{children:t.jsx("span",{className:"payment-method "+("نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"cash":"credit"),children:"نقداً"===e.paymentMethod?n("cash","نقداً"):"دين"===e.paymentMethod?n("credit","دين"):e.paymentMethod||n("cash","نقداً")})}),t.jsx("td",{children:Ut(e.finalTotal)}),t.jsx("td",{children:t.jsx("span",{className:"status "+("نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"paid":"pending"),children:"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?n("paid","مدفوعة"):n("debt","دين")})}),t.jsx("td",{children:t.jsxs("div",{className:"action-buttons-group",children:[t.jsx("button",{className:"btn btn-info btn-xs",onClick:()=>dn(e),title:n("viewInvoiceDetails","عرض تفاصيل الفاتورة"),children:"👁️"}),t.jsx("button",{className:"btn btn-secondary btn-xs",onClick:()=>pn(e),title:n("print","طباعة"),children:"🖨️"}),("مدير"===Ot.role||"admin"===Ot.role)&&t.jsxs(t.Fragment,{children:[t.jsx("button",{className:"btn btn-warning btn-xs",onClick:()=>(e=>{"مدير"===Ot.role||"admin"===Ot.role?(Ye({...e,invoiceNumber:e.invoiceNumber,date:e.date,supplierId:e.supplierId||"",supplierName:e.customerName||e.supplierName||"",paymentMethod:e.paymentMethod||"نقداً",items:[...e.items],total:e.total,discount:e.discount||0,tax:e.tax,finalTotal:e.finalTotal}),_e(e),te(!0),Zt(`✏️ ${n("purchaseInvoiceOpenedForEdit","تم فتح فاتورة المشتريات للتعديل")}`,"info")):Zt(`❌ ${n("notAllowedManagerOnlyPurchase","غير مسموح - المدير فقط يمكنه تعديل فواتير المشتريات")}`,"error")})(e),title:n("editPurchaseInvoice","تعديل فاتورة المشتريات (المدير فقط)"),children:"✏️"}),t.jsx("button",{className:"btn btn-danger btn-xs",onClick:()=>(e=>{if("مدير"===Ot.role||"admin"===Ot.role){if(window.confirm(`${n("confirmDeletePurchaseInvoice","هل أنت متأكد من حذف فاتورة المشتريات")} ${e.invoiceNumber}؟`)){const t=JSON.parse(localStorage.getItem("icaldz-purchases")||"[]").filter((t=>t.id!==e.id));localStorage.setItem("icaldz-purchases",JSON.stringify(t)),e.items.forEach((e=>{const t=Dn.findIndex((t=>t.id===e.productId));if(-1!==t){const n=[...Dn];n[t].stock-=e.quantity,Tn(n),localStorage.setItem("icaldz-products",JSON.stringify(n))}})),Zt(`🗑️ ${n("purchaseInvoiceDeletedAndStockAdjusted","تم حذف فاتورة المشتريات وإعادة تعديل المخزون")}`,"success")}}else Zt(`❌ ${n("notAllowedManagerOnlyDeletePurchase","غير مسموح - المدير فقط يمكنه حذف فواتير المشتريات")}`,"error")})(e),title:n("deletePurchaseInvoice","حذف فاتورة المشتريات (المدير فقط)"),children:"🗑️"})]})]})})]},e.id)))})]})}),t.jsxs("div",{className:"suppliers-section",children:[t.jsxs("div",{className:"section-header "+("ar"!==y?"section-header-ltr":""),children:[t.jsxs("h2",{children:["🏭 ",n("suppliersManagement","إدارة الموردين")]}),t.jsxs("button",{className:"btn btn-success",onClick:rn,children:["➕ ",n("addNewSupplierButton","إضافة مورد جديد")]})]}),t.jsx("div",{className:"suppliers-table-container",children:t.jsxs("table",{className:"data-table "+("ar"!==y?"table-ltr":""),children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:n("supplierID","رقم المورد")}),t.jsx("th",{children:n("supplierNameHeader","اسم المورد")}),t.jsx("th",{children:n("supplierPhoneHeader","رقم الهاتف")}),t.jsx("th",{children:n("supplierEmailHeader","البريد الإلكتروني")}),t.jsx("th",{children:n("supplierAddressHeader","العنوان")}),t.jsx("th",{children:n("actions","الإجراءات")})]})}),t.jsx("tbody",{children:0===Qe.length?t.jsx("tr",{children:t.jsx("td",{colSpan:"6",style:{textAlign:"center",padding:"20px"},children:n("noSuppliersAdded","لا توجد موردين مضافين")})}):Qe.map((e=>t.jsxs("tr",{children:[t.jsx("td",{children:e.id}),t.jsx("td",{children:e.name}),t.jsx("td",{children:e.phone}),t.jsx("td",{children:e.email}),t.jsx("td",{children:e.address}),t.jsx("td",{children:t.jsxs("div",{className:"action-buttons-group",children:[t.jsx("button",{className:"btn btn-primary btn-xs",onClick:()=>(e=>{ut({...e}),ht(!0)})(e),title:n("editSupplier","تعديل المورد"),children:"✏️"}),t.jsx("button",{className:"btn btn-danger btn-xs",onClick:()=>ln(e.id),title:n("deleteSupplier","حذف المورد"),children:"🗑️"})]})})]},e.id)))})]})})]})]}),"sales"===Z&&t.jsxs("div",{className:"sales-page",children:[t.jsxs("div",{className:"page-header "+("ar"!==y?"page-header-ltr-split":""),children:[t.jsx("div",{className:"page-title-section",children:t.jsxs("h1",{children:["🛒 ",n("salesManagement","إدارة المبيعات")]})}),t.jsx("div",{className:"page-description-section",children:t.jsx("div",{className:"header-actions",children:t.jsxs("button",{className:"btn btn-info btn-compact",onClick:()=>{const e="ar"===y,t="ar"===y?"ar":"fr"===y?"fr":"en",a=n("salesReport","تقرير المبيعات"),s=n("reportDate","تاريخ التقرير"),i=n("generationTime","وقت الإنشاء"),r=n("salesSummary","ملخص المبيعات"),o=n("totalSales","إجمالي المبيعات"),l=n("invoiceCount","عدد الفواتير"),c=n("averageInvoice","متوسط الفاتورة"),d=n("cashSales","المبيعات النقدية"),m=n("salesInvoiceDetails","تفاصيل فواتير المبيعات"),h=n("invoiceNumber","رقم الفاتورة"),p=n("date","التاريخ"),u=n("customer","الزبون"),x=n("paymentMethod","طريقة الدفع"),v=n("subtotal","المبلغ الفرعي"),g=n("tax","الضريبة"),b=n("discount","الخصم"),f=n("finalAmount","المبلغ النهائي"),j=n("status","الحالة"),N=n("noSalesInvoicesToDisplay","لا توجد فواتير مبيعات لعرضها"),$=n("walkInCustomer","زبون عابر"),w=n("cash","نقداً"),S=n("credit","دين"),k=n("paid","مدفوعة"),C=n("additionalStatistics","إحصائيات إضافية"),I=n("totalTaxes","إجمالي الضرائب"),D=n("totalDiscounts","إجمالي الخصومات"),T=n("creditInvoices","فواتير الدين"),P=n("creditValue","قيمة الديون"),M=n("accountingSystemIntegratedBusinessManagement","نظام المحاسبي - إدارة الأعمال المتكاملة"),E=n("reportGeneratedAutomatically","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ"),A=n("allRightsReserved","جميع الحقوق محفوظة"),R=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${a} - ${Pt.storeName}</title>\n        <style>\n          @page {\n            size: A4;\n            margin: 20mm;\n          }\n\n          body {\n            font-family: 'Arial', sans-serif;\n            direction: ${e?"rtl":"ltr"};\n            margin: 0;\n            padding: 0;\n            color: #333;\n            line-height: 1.6;\n          }\n\n          .header {\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n            margin-bottom: 30px;\n            padding-bottom: 20px;\n            border-bottom: 3px solid #007bff;\n          }\n\n          .logo-section {\n            display: flex;\n            align-items: center;\n            gap: 15px;\n          }\n\n          .logo {\n            width: 80px;\n            height: 80px;\n            background: linear-gradient(135deg, #007bff, #0056b3);\n            border-radius: 50%;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: white;\n            font-size: 24px;\n            font-weight: bold;\n            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);\n          }\n\n          .company-info h1 {\n            margin: 0;\n            color: #007bff;\n            font-size: 28px;\n            font-weight: bold;\n          }\n\n          .company-info p {\n            margin: 5px 0;\n            color: #666;\n            font-size: 14px;\n          }\n\n          .report-info {\n            text-align: ${e?"left":"right"};\n            direction: ${e?"ltr":"rtl"};\n          }\n\n          .report-title {\n            font-size: 24px;\n            font-weight: bold;\n            color: #007bff;\n            margin: 0;\n          }\n\n          .report-date {\n            color: #666;\n            font-size: 14px;\n            margin: 5px 0;\n          }\n\n          .summary-section {\n            margin: 30px 0;\n            background: #f8f9fa;\n            padding: 20px;\n            border-radius: 10px;\n            border-${e?"left":"right"}: 5px solid #007bff;\n          }\n\n          .store-logo {\n            width: 80px;\n            height: 80px;\n            border-radius: 50%;\n            object-fit: cover;\n            border: 3px solid #007bff;\n          }\n\n          .summary-title {\n            font-size: 20px;\n            font-weight: bold;\n            color: #007bff;\n            margin-bottom: 15px;\n          }\n\n          .summary-grid {\n            display: grid;\n            grid-template-columns: repeat(4, 1fr);\n            gap: 20px;\n          }\n\n          .summary-card {\n            background: white;\n            padding: 15px;\n            border-radius: 8px;\n            text-align: center;\n            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n          }\n\n          .summary-card h3 {\n            margin: 0 0 10px 0;\n            font-size: 14px;\n            color: #666;\n          }\n\n          .summary-card .value {\n            font-size: 24px;\n            font-weight: bold;\n            color: #007bff;\n          }\n\n          .table-section {\n            margin: 30px 0;\n          }\n\n          .section-title {\n            font-size: 18px;\n            font-weight: bold;\n            color: #333;\n            margin-bottom: 15px;\n            padding-bottom: 10px;\n            border-bottom: 2px solid #e9ecef;\n          }\n\n          .data-table {\n            width: 100%;\n            border-collapse: collapse;\n            margin-bottom: 20px;\n            background: white;\n            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n            border-radius: 8px;\n            overflow: hidden;\n          }\n\n          .data-table th {\n            background: linear-gradient(135deg, #007bff, #0056b3);\n            color: white;\n            padding: 12px 8px;\n            text-align: center;\n            font-weight: bold;\n            font-size: 14px;\n          }\n\n          .data-table td {\n            padding: 10px 8px;\n            text-align: center;\n            border-bottom: 1px solid #e9ecef;\n            font-size: 13px;\n          }\n\n          .data-table tr:nth-child(even) {\n            background-color: #f8f9fa;\n          }\n\n          .data-table tr:hover {\n            background-color: #e3f2fd;\n          }\n\n          .amount {\n            font-weight: bold;\n            color: #28a745;\n          }\n\n          .status-paid {\n            background: #28a745;\n            color: white;\n            padding: 4px 8px;\n            border-radius: 4px;\n            font-size: 11px;\n          }\n\n          .status-credit {\n            background: #ffc107;\n            color: #333;\n            padding: 4px 8px;\n            border-radius: 4px;\n            font-size: 11px;\n          }\n\n          .footer {\n            margin-top: 40px;\n            padding-top: 20px;\n            border-top: 2px solid #e9ecef;\n            text-align: center;\n            color: #666;\n            font-size: 12px;\n          }\n\n          .footer-logo {\n            margin-bottom: 10px;\n          }\n\n          @media print {\n            body {\n              -webkit-print-color-adjust: exact;\n              print-color-adjust: exact;\n            }\n\n            .no-print {\n              display: none !important;\n            }\n          }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <div class="logo-section">\n            ${Pt.logo?`\n              <img src="${Pt.logo}" alt="Store Logo" class="store-logo">\n            `:'\n              <div class="logo">\n                iC\n              </div>\n            '}\n            <div class="company-info">\n              <h1>${Pt.storeName}</h1>\n              <p>📞 ${Pt.storePhone}</p>\n              <p>📍 ${Pt.storeAddress}</p>\n            </div>\n          </div>\n          <div class="report-info">\n            <h2 class="report-title">${a}</h2>\n            <p class="report-date">${s}: ${(new Date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n            <p class="report-date">${i}: ${(new Date).toLocaleTimeString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n          </div>\n        </div>\n\n        <div class="summary-section">\n          <h2 class="summary-title">📊 ${r}</h2>\n          <div class="summary-grid">\n            <div class="summary-card">\n              <h3>${o}</h3>\n              <div class="value">${Ut(Ce.reduce(((e,t)=>e+t.finalTotal),0))}</div>\n            </div>\n            <div class="summary-card">\n              <h3>${l}</h3>\n              <div class="value">${Ce.length}</div>\n            </div>\n            <div class="summary-card">\n              <h3>${c}</h3>\n              <div class="value">${Ce.length>0?Ut(Ce.reduce(((e,t)=>e+t.finalTotal),0)/Ce.length):"0.00 دج"}</div>\n            </div>\n            <div class="summary-card">\n              <h3>${d}</h3>\n              <div class="value">${Ut(Ce.filter((e=>"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0))}</div>\n            </div>\n          </div>\n        </div>\n\n        <div class="table-section">\n          <h2 class="section-title">📋 ${m}</h2>\n          <table class="data-table">\n            <thead>\n              <tr>\n                <th>${h}</th>\n                <th>${p}</th>\n                <th>${u}</th>\n                <th>${x}</th>\n                <th>${v}</th>\n                <th>${g}</th>\n                <th>${b}</th>\n                <th>${f}</th>\n                <th>${j}</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${0===Ce.length?`\n                <tr>\n                  <td colspan="9" style="text-align: center; padding: 30px; color: #666;">\n                    ${N}\n                  </td>\n                </tr>\n              `:Ce.map((e=>`\n                <tr>\n                  <td style="font-weight: bold;">${e.invoiceNumber}</td>\n                  <td>${new Date(e.date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</td>\n                  <td>${e.customerName||$}</td>\n                  <td>${"نقداً"===e.paymentMethod?w:"دين"===e.paymentMethod?S:"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?w:"Crédit"===e.paymentMethod||"Credit"===e.paymentMethod?S:e.paymentMethod||w}</td>\n                  <td class="amount">${Ut(e.total)}</td>\n                  <td class="amount">${Ut(e.tax)}</td>\n                  <td class="amount">${Ut(e.discount)}</td>\n                  <td class="amount" style="font-size: 14px; font-weight: bold;">${Ut(e.finalTotal)}</td>\n                  <td>\n                    <span class="${"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"status-paid":"status-credit"}">\n                      ${"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?k:S}\n                    </span>\n                  </td>\n                </tr>\n              `)).join("")}\n            </tbody>\n          </table>\n        </div>\n\n        ${Ce.length>0?`\n          <div class="table-section">\n            <h2 class="section-title">📈 ${C}</h2>\n            <div class="summary-grid">\n              <div class="summary-card">\n                <h3>${I}</h3>\n                <div class="value">${Ut(Ce.reduce(((e,t)=>e+t.tax),0))}</div>\n              </div>\n              <div class="summary-card">\n                <h3>${D}</h3>\n                <div class="value">${Ut(Ce.reduce(((e,t)=>e+t.discount),0))}</div>\n              </div>\n              <div class="summary-card">\n                <h3>${T}</h3>\n                <div class="value">${Ce.filter((e=>"دين"===e.paymentMethod||"Crédit"===e.paymentMethod||"Credit"===e.paymentMethod)).length}</div>\n              </div>\n              <div class="summary-card">\n                <h3>${P}</h3>\n                <div class="value">${Ut(Ce.filter((e=>"دين"===e.paymentMethod||"Crédit"===e.paymentMethod||"Credit"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0))}</div>\n              </div>\n            </div>\n          </div>\n        `:""}\n\n        <div class="footer">\n          <div class="footer-logo">\n            <strong>${M}</strong>\n          </div>\n          <p>${E}</p>\n          <p>© 2025 iDesign DZ +213 551 93 05 89 - ${A}</p>\n        </div>\n      </body>\n      </html>\n    `,F=window.open("","_blank","width=1200,height=800");F.document.write(R),F.document.close(),F.onload=function(){setTimeout((()=>{F.print()}),1e3)},Zt(`📊 ${n("salesReportOpened","تم فتح تقرير المبيعات الاحترافي للطباعة")}`,"success",3e3)},children:["📊 ",n("report","تقرير")]})})})]}),t.jsxs("div",{className:"sales-summary",children:[t.jsxs("div",{className:"summary-card green",children:[t.jsx("h3",{children:n("totalSales","إجمالي المبيعات")}),t.jsx("div",{className:"summary-value",children:Ut(Ce.reduce(((e,t)=>e+t.finalTotal),0))})]}),t.jsxs("div",{className:"summary-card blue",children:[t.jsx("h3",{children:n("invoiceCount","عدد الفواتير")}),t.jsx("div",{className:"summary-value",children:Ce.length})]}),t.jsxs("div",{className:"summary-card orange",children:[t.jsx("h3",{children:n("averageInvoice","متوسط الفاتورة")}),t.jsx("div",{className:"summary-value",children:Ce.length>0?Ut(Ce.reduce(((e,t)=>e+t.finalTotal),0)/Ce.length):Ut(0)})]}),t.jsxs("div",{className:"summary-card purple",children:[t.jsx("h3",{children:n("creditInvoices","فواتير دين")}),t.jsx("div",{className:"summary-value",children:Ce.filter((e=>"دين"===e.paymentMethod||"Dette"===e.paymentMethod||"Credit"===e.paymentMethod)).length})]})]}),Ma.length>0&&t.jsxs("div",{className:"bulk-actions "+(ft.length>0?"has-selection":""),children:[t.jsxs("div",{className:"select-all-container",children:[t.jsx("input",{type:"checkbox",className:"select-all-checkbox",checked:Ma.length>0&&ft.length===Ma.length,onChange:()=>Wt("sales",Ma),id:"select-all-sales"}),t.jsx("label",{htmlFor:"select-all-sales",className:"select-all-label",children:n("selectAll","تحديد الكل")}),ft.length>0&&t.jsxs("span",{className:"selected-count",children:["(",ft.length," ",n("selected","محدد"),")"]})]}),ft.length>0&&("مدير"===Ot.role||"admin"===Ot.role)&&t.jsxs("button",{className:"bulk-delete-btn",onClick:()=>Qt("sales"),title:n("deleteSelected","حذف المحدد"),children:["🗑️ ",n("deleteSelected","حذف المحدد")," (",ft.length,")"]})]}),t.jsx("div",{className:"inventory-controls",children:t.jsxs("div",{className:"search-section",children:[t.jsx("input",{type:"text",className:"search-input",placeholder:`🔍 ${n("searchSalesInvoices","البحث في فواتير المبيعات (رقم الفاتورة، اسم الزبون)...")}`,value:Ta,onChange:e=>Pa(e.target.value)}),Ta&&t.jsx("button",{className:"btn btn-secondary btn-xs",onClick:()=>Pa(""),title:n("clearFilters","مسح الفلاتر"),children:"🔄"})]})}),t.jsx("div",{className:"table-container",children:t.jsxs("table",{className:"data-table "+("ar"!==y?"table-ltr":""),children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{className:"checkbox-column",children:t.jsx("input",{type:"checkbox",checked:Ma.length>0&&ft.length===Ma.length,onChange:()=>Wt("sales",Ma),title:n("selectAll","تحديد الكل")})}),t.jsx("th",{children:n("invoiceNumber","رقم الفاتورة")}),t.jsx("th",{children:n("date","التاريخ")}),t.jsx("th",{children:n("customer","الزبون")}),t.jsx("th",{children:n("paymentMethod","طريقة الدفع")}),t.jsx("th",{children:n("amount","المبلغ")}),t.jsx("th",{children:n("status","الحالة")}),t.jsx("th",{children:n("actions","الإجراءات")})]})}),t.jsx("tbody",{children:0===Ma.length?t.jsx("tr",{children:t.jsx("td",{colSpan:"8",style:{textAlign:"center",padding:"20px"},children:Ta.trim()?`${n("noInvoicesMatchingFilter","لا توجد فواتير مطابقة للفلتر")}: "${Ta}"`:n("noInvoicesSaved","لا توجد فواتير محفوظة")})}):Ma.map((e=>t.jsxs("tr",{className:ft.includes(e.id)?"selected":"",children:[t.jsx("td",{className:"checkbox-column",children:t.jsx("input",{type:"checkbox",className:"row-checkbox",checked:ft.includes(e.id),onChange:()=>Yt("sales",e.id)})}),t.jsx("td",{children:e.invoiceNumber}),t.jsx("td",{children:e.date}),t.jsx("td",{children:qt(e.customerName)}),t.jsx("td",{children:t.jsx("span",{className:"payment-method "+("نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"cash":"credit"),children:"نقداً"===e.paymentMethod?n("cash","نقداً"):"دين"===e.paymentMethod?n("credit","دين"):e.paymentMethod||n("cash","نقداً")})}),t.jsx("td",{children:Ut(e.finalTotal)}),t.jsx("td",{children:t.jsx("span",{className:"status "+("نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"paid":"pending"),children:"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?n("paid","مدفوعة"):n("debt","دين")})}),t.jsx("td",{children:t.jsxs("div",{className:"action-buttons-group",children:[t.jsx("button",{className:"btn btn-info btn-xs",onClick:()=>dn(e),title:n("viewInvoiceDetails","عرض تفاصيل الفاتورة"),children:"👁️"}),t.jsx("button",{className:"btn btn-secondary btn-xs",onClick:()=>pn(e),title:n("normalPrint","طباعة عادية"),children:"🖨️"}),t.jsx("button",{className:"btn btn-success btn-xs",onClick:()=>mn(e),title:n("thermalPrint","طباعة حرارية"),children:"🧾"}),("مدير"===Ot.role||"admin"===Ot.role)&&t.jsxs(t.Fragment,{children:[t.jsx("button",{className:"btn btn-warning btn-xs",onClick:()=>(e=>{"مدير"===Ot.role||"admin"===Ot.role?(Ja(""),_n({...e}),Yn([...e.items]),Gn(!0)):Zt(`❌ ${n("notAllowedManagerOnlyEditInvoices","غير مسموح - المدير فقط يمكنه تعديل الفواتير")}`,"error")})(e),title:n("editInvoice","تعديل الفاتورة (المدير فقط)"),children:"✏️"}),t.jsx("button",{className:"btn btn-primary btn-xs",onClick:()=>vn(e),title:n("returnProducts","إرجاع منتجات"),children:"↩️"}),t.jsx("button",{className:"btn btn-danger btn-xs",onClick:()=>(e=>{if(window.confirm(`${n("confirmDeleteInvoice","هل أنت متأكد من حذف الفاتورة رقم")} ${e.invoiceNumber}؟\n\n${n("allProductsWillBeRestored","سيتم إرجاع جميع المنتجات إلى المخزون.")}`)){const t=Dn.map((t=>{const n=e.items.find((e=>e.productId===t.id||String(e.productId)===String(t.id)));return n?{...t,stock:t.stock+n.quantity}:t})),a=Ce.filter((t=>t.id!==e.id));Mn(t),Ie(a),localStorage.setItem("icaldz-invoices",JSON.stringify(a)),Zt(`🗑️ ${n("invoiceDeletedAndStockRestored","تم حذف الفاتورة")} ${e.invoiceNumber} ${n("andStockRestored","وإرجاع المنتجات للمخزون")}`,"success",3e3)}})(e),title:n("deleteInvoice","حذف الفاتورة (المدير فقط)"),children:"🗑️"})]}),("seller"===Ot.role||"بائع"===Ot.role)&&t.jsx("button",{className:"btn btn-primary btn-xs",onClick:()=>vn(e),title:n("returnProducts","إرجاع منتجات"),children:"↩️"})]})})]},e.id)))})]})})]}),"inventory"===Z&&t.jsxs("div",{className:"inventory-page",children:[t.jsxs("div",{className:"page-header "+("ar"!==y?"page-header-ltr-split":""),children:[t.jsx("div",{className:"page-title-section",children:t.jsxs("h1",{children:["📊 ",n("inventoryManagement","إدارة المخزون")]})}),t.jsx("div",{className:"page-description-section",children:t.jsxs("div",{className:"header-actions",children:[t.jsxs("button",{className:"btn btn-success btn-compact",onClick:ms,children:["➕ ",n("newProduct","منتج جديد")]}),t.jsxs("div",{className:"dropdown-container",children:[t.jsxs("button",{className:"btn btn-primary btn-compact dropdown-toggle",onClick:()=>$e(!Ne),children:["💾 ",n("dataManagement","البيانات")]}),Ne&&t.jsxs("div",{className:"dropdown-menu",children:[t.jsxs("button",{className:"dropdown-item",onClick:()=>{if(Ht(Dn,"ar"===y?"تقرير_المخزون":"fr"===y?"Rapport_Inventaire":"Inventory_Report")){Zt("ar"===y?"📊 تم تصدير المخزون بتنسيق Excel بنجاح":"fr"===y?"📊 Inventaire exporté en Excel avec succès":"📊 Inventory exported to Excel successfully","success",3e3)}else{Zt("ar"===y?"❌ حدث خطأ أثناء تصدير الملف":"fr"===y?"❌ Erreur lors de l'export du fichier":"❌ Error occurred while exporting file","error",3e3)}$e(!1)},children:["📊 ",n("exportExcel","تصدير Excel")]}),t.jsxs("button",{className:"dropdown-item",onClick:()=>{const e={products:Dn,timestamp:(new Date).toISOString(),version:"1.0",totalProducts:Dn.length,totalValue:Dn.reduce(((e,t)=>e+t.stock*t.buyPrice),0)},t=new Blob([JSON.stringify(e,null,2)],{type:"application/json"}),n=document.createElement("a");n.href=URL.createObjectURL(t),n.download=`Inventory_Backup_${(new Date).toISOString().split("T")[0]}.json`,n.click(),Zt("💾 تم إنشاء نسخة احتياطية من البيانات","success",3e3),$e(!1)},children:["💾 ",n("jsonBackup","نسخة احتياطية JSON")]}),t.jsxs("button",{className:"dropdown-item",onClick:()=>{document.getElementById("import-file").click(),$e(!1)},children:["📥 ",n("importData","استيراد بيانات")]}),t.jsxs("button",{className:"dropdown-item danger",onClick:()=>{window.confirm(n("confirmDeleteAllData","هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!"))&&(localStorage.removeItem("icaldz-products"),Tn([]),Zt(`🗑️ ${n("allInventoryDataDeleted","تم حذف جميع بيانات المخزون")}`,"warning",3e3),$e(!1))},children:["🗑️ ",n("deleteAllData","حذف جميع البيانات")]})]})]}),t.jsxs("button",{className:"btn btn-info btn-compact",onClick:()=>{window.print(),Zt(`🖨️ ${n("reportSentToPrinter","تم إرسال التقرير للطباعة")}`,"success",2e3)},children:["🖨️ ",n("print","طباعة")]})]})})]}),t.jsx("input",{type:"file",id:"import-file",accept:".json,.xls,.xlsx,.csv",style:{display:"none"},onChange:async e=>{const t=e.target.files[0];if(t)try{if(t.name.endsWith(".json")){const e=new FileReader;e.onload=e=>{try{const t=e.target.result,n=JSON.parse(t);n.products&&Array.isArray(n.products)?(Mn(n.products),Zt(`✅ تم استيراد ${n.products.length} منتج بنجاح من JSON`,"success",3e3)):Zt("❌ تنسيق ملف JSON غير صحيح","error",3e3)}catch(t){Zt("❌ خطأ في قراءة ملف JSON","error",3e3)}},e.readAsText(t)}else if(t.name.endsWith(".xlsx")||t.name.endsWith(".xls"))try{const e=await(e=>new Promise(((t,n)=>{const a=new FileReader;a.onload=e=>{try{const n=new Uint8Array(e.target.result),a=r(n,{type:"array"}),i=a.SheetNames[0],o=a.Sheets[i],l=s.sheet_to_json(o,{header:1}).slice(1).filter((e=>e.length>0&&e[0]&&e[1])).map(((e,t)=>"string"==typeof e[0]&&(e[0].includes("الملخص")||e[0].includes("إجمالي")||e[0].includes("Résumé")||e[0].includes("Total")||e[0].includes("Summary")||e[0].includes("Produits"))?null:{id:e[0]||`IMP${String(t+1).padStart(3,"0")}`,name:e[1]||"منتج مستورد",barcode:"غير محدد"===e[2]?"":e[2]||"",category:e[3]||"عام",buyPrice:parseFloat(e[4])||0,sellPrice:parseFloat(e[5])||0,stock:parseInt(e[6])||0,minStock:parseInt(e[7])||5,price:parseFloat(e[5])||0,createdAt:(new Date).toISOString()})).filter((e=>null!==e));t(l)}catch(a){n(a)}},a.onerror=()=>n(new Error("فشل في قراءة الملف")),a.readAsArrayBuffer(e)})))(t);if(e.length>0){if(window.confirm(`تم العثور على ${e.length} منتج في الملف.\n\nاختر "موافق" لاستبدال جميع المنتجات الحالية\nأو "إلغاء" لإضافة المنتجات الجديدة فقط`))Mn(e),Zt(`✅ تم استبدال المخزون بـ ${e.length} منتج من Excel`,"success",3e3);else{const t=Dn.map((e=>e.id)),n=e.filter((e=>!t.includes(e.id))),a=[...Dn,...n];Mn(a),Zt(`✅ تم إضافة ${n.length} منتج جديد من Excel`,"success",3e3)}}else Zt("⚠️ لم يتم العثور على منتجات صالحة في الملف","warning",3e3)}catch(n){Zt("❌ خطأ في قراءة ملف Excel: "+n.message,"error",3e3)}else Zt("⚠️ يدعم النظام ملفات JSON و Excel فقط (.json, .xlsx, .xls)","warning",3e3)}catch(n){Zt("❌ خطأ في معالجة الملف","error",3e3)}e.target.value=""}}),t.jsx("div",{className:"inventory-controls",children:t.jsxs("div",{className:"search-section",children:[t.jsx("input",{type:"text",placeholder:n("searchProducts","البحث في المنتجات (الاسم، الرمز، الباركود)..."),className:"search-input",value:sa,onChange:e=>ia(e.target.value)}),t.jsxs("div",{className:"category-filter-group",children:[t.jsxs("select",{className:"filter-select",value:be,onChange:e=>fe(e.target.value),children:[t.jsx("option",{value:"",children:n("allCategories","جميع الفئات")}),Fn.map((e=>t.jsx("option",{value:e,children:e},e)))]}),t.jsx("button",{className:"btn btn-secondary btn-xs",onClick:Sn,title:n("manageCategories","إدارة الفئات"),children:"⚙️"})]}),t.jsxs("select",{className:"filter-select",value:je,onChange:e=>ye(e.target.value),children:[t.jsx("option",{value:"",children:n("allStatuses","جميع الحالات")}),t.jsx("option",{value:"عادي",children:n("normal","عادي")}),t.jsx("option",{value:"مرتفع",children:n("high","مرتفع")}),t.jsx("option",{value:"منخفض",children:n("low","منخفض")}),t.jsx("option",{value:"نفد",children:n("outOfStock","نفد")})]}),(sa||be||je)&&t.jsx("button",{className:"btn btn-secondary btn-xs",onClick:()=>{ia(""),fe(""),ye("")},title:n("clearFilters","مسح الفلاتر"),children:"🔄"})]})}),vs.length>0&&t.jsxs("div",{className:"bulk-actions "+(St.length>0?"has-selection":""),children:[t.jsxs("div",{className:"select-all-container",children:[t.jsx("input",{type:"checkbox",className:"select-all-checkbox",checked:vs.length>0&&St.length===vs.length,onChange:()=>Wt("products",vs),id:"select-all-products"}),t.jsx("label",{htmlFor:"select-all-products",className:"select-all-label",children:n("selectAll","تحديد الكل")}),St.length>0&&t.jsxs("span",{className:"selected-count",children:["(",St.length," ",n("selected","محدد"),")"]})]}),St.length>0&&("مدير"===Ot.role||"admin"===Ot.role)&&t.jsxs("button",{className:"bulk-delete-btn",onClick:()=>Qt("products"),title:n("deleteSelected","حذف المحدد"),children:["🗑️ ",n("deleteSelected","حذف المحدد")," (",St.length,")"]})]}),t.jsx("div",{className:"inventory-table-container",children:t.jsxs("table",{className:"inventory-table",children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{className:"checkbox-column",children:t.jsx("input",{type:"checkbox",checked:vs.length>0&&St.length===vs.length,onChange:()=>Wt("products",vs),title:n("selectAll","تحديد الكل")})}),t.jsx("th",{children:n("productCode","رمز المنتج")}),t.jsx("th",{children:n("productName","اسم المنتج")}),t.jsx("th",{children:n("barcode","الباركود")}),t.jsx("th",{children:n("category","الفئة")}),t.jsx("th",{children:n("buyPrice","سعر الشراء")}),t.jsx("th",{children:n("sellPrice","سعر البيع")}),t.jsx("th",{children:n("availableQuantity","الكمية المتوفرة")}),t.jsx("th",{children:n("minStock","الحد الأدنى")}),t.jsx("th",{children:n("totalValue","القيمة الإجمالية")}),t.jsx("th",{children:n("status","الحالة")}),t.jsx("th",{children:n("actions","الإجراءات")})]})}),t.jsx("tbody",{children:0===vs.length?t.jsx("tr",{children:t.jsx("td",{colSpan:"12",style:{textAlign:"center",padding:"40px",color:"#666"},children:sa||be||je?`🔍 ${n("noProductsFound","لا توجد منتجات تطابق معايير البحث")}`:`📦 ${n("noProductsInInventory","لا توجد منتجات في المخزون")}`})}):vs.map((e=>t.jsxs("tr",{className:`${e.stock<=e.minStock?"low-stock":""} ${St.includes(e.id)?"selected":""} clickable-row`,onClick:t=>{"checkbox"===t.target.type||t.target.closest(".action-buttons-group")||t.target.closest(".stock-input")||t.target.closest(".stock-display-vendor")||xs(e)},title:n("clickToEdit","انقر للتعديل"),children:[t.jsx("td",{className:"checkbox-column",onClick:e=>e.stopPropagation(),children:t.jsx("input",{type:"checkbox",className:"row-checkbox",checked:St.includes(e.id),onChange:()=>Yt("products",e.id)})}),t.jsx("td",{children:e.id}),t.jsx("td",{children:e.name}),t.jsx("td",{children:t.jsx("span",{className:"barcode-display",children:e.barcode||n("notSpecified","غير محدد")})}),t.jsx("td",{children:e.category}),t.jsx("td",{children:Ut(e.buyPrice)}),t.jsx("td",{children:Ut(e.sellPrice)}),t.jsx("td",{onClick:e=>e.stopPropagation(),children:"مدير"===Ot.role||"admin"===Ot.role?t.jsx("input",{type:"number",value:e.stock,onChange:t=>us(e.id,parseInt(t.target.value)||0),className:"stock-input",min:"0"}):t.jsxs("div",{className:"stock-display-vendor",children:[t.jsx("span",{className:"stock-value",children:e.stock}),t.jsx("button",{className:"btn btn-xs btn-warning",onClick:()=>(e=>{if("مدير"===Ot.role||"admin"===Ot.role){const t=prompt(`تعديل كمية ${e.name}\nالكمية الحالية: ${e.stock}`,e.stock);return void(null===t||isNaN(t)||us(e.id,parseInt(t)))}const t=prompt(`طلب تعديل كمية ${e.name}\nالكمية الحالية: ${e.stock}\nأدخل الكمية المطلوبة:`,e.stock);if(null!==t&&!isNaN(t)){const n={id:"REQ-"+Date.now(),productId:e.id,productName:e.name,currentStock:e.stock,requestedStock:parseInt(t),requestedBy:Ot.name,requestDate:(new Date).toISOString(),status:"pending",reason:prompt("سبب التعديل (اختياري):")||"تعديل كمية"},a=JSON.parse(localStorage.getItem("icaldz-stock-requests")||"[]");a.push(n),localStorage.setItem("icaldz-stock-requests",JSON.stringify(a)),Zt("📝 تم إرسال طلب تعديل الكمية للمدير","info",3e3)}})(e),title:n("requestStockUpdate","طلب تعديل الكمية (يحتاج موافقة المدير)"),children:"📝"})]})}),t.jsx("td",{children:e.minStock}),t.jsx("td",{children:Ut(e.stock*e.buyPrice)}),t.jsx("td",{children:t.jsx("span",{className:"stock-status "+(e.stock<=e.minStock?"low":e.stock>2*e.minStock?"high":"normal"),children:e.stock<=e.minStock?n("low","منخفض"):e.stock>2*e.minStock?n("high","مرتفع"):n("normal","عادي")})}),t.jsx("td",{onClick:e=>e.stopPropagation(),children:t.jsxs("div",{className:"action-buttons-group",style:{justifyContent:"flex-start",gap:"5px"},children:[t.jsx("button",{className:"btn btn-warning btn-xs",onClick:()=>xs(e),title:n("editProduct","تعديل المنتج"),children:"✏️"}),t.jsx("button",{className:"btn btn-secondary btn-xs",onClick:()=>generateProductBarcode(e),title:n("generateBarcode","إنشاء باركود"),children:"📊"}),("مدير"===Ot.role||"admin"===Ot.role)&&t.jsx("button",{className:"btn btn-danger btn-xs",onClick:()=>(e=>{const t=Dn.find((t=>t.id===e));if(window.confirm(`${n("confirmDeleteProduct","هل أنت متأكد من حذف المنتج")} "${t?.name}"؟`)){const a=Dn.filter((t=>t.id!==e));Mn(a),j.play("deleteProduct",{showNotification:!1}),Zt(`🗑️ ${n("productDeletedSuccessfully","تم حذف المنتج")} "${t?.name}" ${n("successfully","بنجاح")}`,"success",2e3)}})(e.id),title:n("deleteProduct","حذف (المدير فقط)"),children:"🗑️"})]})})]},e.id)))})]})}),t.jsxs("div",{className:"inventory-summary",children:[t.jsxs("div",{className:"summary-card blue",children:[t.jsx("h3",{children:sa||be||je?n("displayedProducts","المنتجات المعروضة"):n("totalProducts","إجمالي المنتجات")}),t.jsx("div",{className:"summary-value",children:vs.length}),(sa||be||je)&&t.jsxs("small",{style:{color:"#666",fontSize:"12px"},children:[n("outOfTotal","من أصل")," ",Dn.length," ",n("product","منتج")]})]}),t.jsxs("div",{className:"summary-card green",children:[t.jsx("h3",{children:n("totalValue","القيمة الإجمالية")}),t.jsx("div",{className:"summary-value",children:Ut(vs.reduce(((e,t)=>e+t.stock*t.buyPrice),0))})]}),t.jsxs("div",{className:"summary-card orange",children:[t.jsx("h3",{children:n("lowStockProducts","منتجات منخفضة المخزون")}),t.jsx("div",{className:"summary-value",children:vs.filter((e=>e.stock<=e.minStock)).length})]}),t.jsxs("div",{className:"summary-card red",children:[t.jsx("h3",{children:n("outOfStockProducts","منتجات نفدت")}),t.jsx("div",{className:"summary-value",children:vs.filter((e=>0===e.stock)).length})]})]})]}),"reports"===Z&&("مدير"===Ot.role||"admin"===Ot.role)&&t.jsxs("div",{className:"reports-page",children:[t.jsxs("div",{className:"page-header "+("ar"!==y?"page-header-ltr-split":""),children:[t.jsx("div",{className:"page-title-section",children:t.jsxs("h1",{children:["📊 ",n("reportsAndStatistics","التقارير والإحصائيات")]})}),t.jsx("div",{className:"page-description-section",children:t.jsx("p",{children:n("comprehensiveReports","تقارير شاملة لجميع العمليات المالية والتجارية - المدير فقط")})})]}),t.jsxs("div",{className:"reports-categories",children:[t.jsxs("div",{className:"report-category",children:[t.jsxs("div",{className:"category-header",children:[t.jsxs("h2",{children:["💰 ",n("financialReports","التقارير المالية")]}),t.jsx("p",{children:n("financialReportsDesc","تقارير المبيعات والمشتريات والأرباح")})]}),t.jsxs("div",{className:"reports-grid",children:[t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e="ar"===y,t="ar"===y?"ar":"fr"===y?"fr":"en",a=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("salesReport","تقرير المبيعات")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${e?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n          .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n          .stat-card h3 { margin: 0 0 10px 0; color: #34495e; }\n          .stat-card .value { font-size: 24px; font-weight: bold; color: #27ae60; }\n          .table-container { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          table { width: 100%; border-collapse: collapse; }\n          th { background: #3498db; color: white; padding: 15px; text-align: center; font-weight: bold; }\n          td { padding: 12px; text-align: center; border-bottom: 1px solid #ecf0f1; }\n          tr:nth-child(even) { background: #f8f9fa; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .header, .stat-card, .table-container { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>📈 ${n("salesReport","تقرير المبيعات التفصيلي")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("salesManagement","إدارة المبيعات")}</p>\n        </div>\n\n        <div class="stats-grid">\n          <div class="stat-card">\n            <h3>${n("totalSales","إجمالي المبيعات")}</h3>\n            <div class="value">${Ut(Ce.reduce(((e,t)=>e+t.finalTotal),0))}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("invoiceCount","عدد الفواتير")}</h3>\n            <div class="value">${Ce.length}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("averageInvoice","متوسط قيمة الفاتورة")}</h3>\n            <div class="value">${Ut(Ce.length>0?Ce.reduce(((e,t)=>e+t.finalTotal),0)/Ce.length:0)}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("cashSales","المبيعات النقدية")}</h3>\n            <div class="value">${Ut(Ce.filter((e=>"نقداً"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0))}</div>\n          </div>\n        </div>\n\n        <div class="table-container">\n          <table>\n            <thead>\n              <tr>\n                <th>${n("invoiceNumber","رقم الفاتورة")}</th>\n                <th>${n("date","التاريخ")}</th>\n                <th>${n("customerName","العميل")}</th>\n                <th>${n("paymentMethod","طريقة الدفع")}</th>\n                <th>${n("subtotal","المبلغ الإجمالي")}</th>\n                <th>${n("tax","الضريبة")}</th>\n                <th>${n("finalAmount","المبلغ النهائي")}</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${Ce.map((e=>`\n                <tr>\n                  <td>${e.invoiceNumber}</td>\n                  <td>${e.date}</td>\n                  <td>${e.customerName||n("walkInCustomer","زبون عابر")}</td>\n                  <td>${"نقداً"===e.paymentMethod?n("cash","نقداً"):"دين"===e.paymentMethod?n("credit","دين"):e.paymentMethod||n("cash","نقداً")}</td>\n                  <td>${Ut(e.total)}</td>\n                  <td>${Ut(e.tax)}</td>\n                  <td>${Ut(e.finalTotal)}</td>\n                </tr>\n              `)).join("")}\n            </tbody>\n          </table>\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,s=window.open("","_blank","width=1200,height=800");s.document.write(a),s.document.close(),s.onload=()=>setTimeout((()=>s.print()),1e3),Zt(n("salesReportOpened","📈 تم فتح تقرير المبيعات للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"📈"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("salesReport","تقرير المبيعات")}),t.jsx("p",{children:n("salesReportDesc","إجمالي المبيعات والفواتير")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:Ut(Ce.reduce(((e,t)=>e+t.finalTotal),0))}),t.jsxs("small",{children:[Ce.length," ",n("invoice","فاتورة")]})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e="ar"===y,t="ar"===y?"ar":"fr"===y?"fr":"en",a=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("purchaseReport","تقرير المشتريات")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${e?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n          .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n          .stat-card h3 { margin: 0 0 10px 0; color: #34495e; }\n          .stat-card .value { font-size: 24px; font-weight: bold; color: #e74c3c; }\n          .table-container { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          table { width: 100%; border-collapse: collapse; }\n          th { background: #e74c3c; color: white; padding: 15px; text-align: center; font-weight: bold; }\n          td { padding: 12px; text-align: center; border-bottom: 1px solid #ecf0f1; }\n          tr:nth-child(even) { background: #f8f9fa; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .header, .stat-card, .table-container { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>📉 ${n("purchaseReport","تقرير المشتريات التفصيلي")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("purchaseManagement","إدارة المشتريات")}</p>\n        </div>\n\n        <div class="stats-grid">\n          <div class="stat-card">\n            <h3>${n("totalPurchases","إجمالي المشتريات")}</h3>\n            <div class="value">${Ut(Ke.reduce(((e,t)=>e+t.finalTotal),0))}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("invoiceCount","عدد فواتير الشراء")}</h3>\n            <div class="value">${Ke.length}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("averageInvoice","متوسط قيمة الفاتورة")}</h3>\n            <div class="value">${Ut(Ke.length>0?Ke.reduce(((e,t)=>e+t.finalTotal),0)/Ke.length:0)}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("cashPurchases","المشتريات النقدية")}</h3>\n            <div class="value">${Ut(Ke.filter((e=>"نقداً"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0))}</div>\n          </div>\n        </div>\n\n        <div class="table-container">\n          <table>\n            <thead>\n              <tr>\n                <th>${n("invoiceNumber","رقم الفاتورة")}</th>\n                <th>${n("date","التاريخ")}</th>\n                <th>${n("supplier","المورد")}</th>\n                <th>${n("paymentMethod","طريقة الدفع")}</th>\n                <th>${n("subtotal","المبلغ الإجمالي")}</th>\n                <th>${n("tax","الضريبة")}</th>\n                <th>${n("finalAmount","المبلغ النهائي")}</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${Ke.map((e=>`\n                <tr>\n                  <td>${e.invoiceNumber}</td>\n                  <td>${e.date}</td>\n                  <td>${e.supplierName||n("unknownSupplier","مورد غير محدد")}</td>\n                  <td>${"نقداً"===e.paymentMethod?n("cash","نقداً"):"دين"===e.paymentMethod?n("credit","دين"):e.paymentMethod||n("cash","نقداً")}</td>\n                  <td>${Ut(e.total)}</td>\n                  <td>${Ut(e.tax)}</td>\n                  <td>${Ut(e.finalTotal)}</td>\n                </tr>\n              `)).join("")}\n            </tbody>\n          </table>\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,s=window.open("","_blank","width=1200,height=800");s.document.write(a),s.document.close(),s.onload=()=>setTimeout((()=>s.print()),1e3),Zt(n("purchaseReportOpened","📉 تم فتح تقرير المشتريات للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"📉"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("purchaseReport","تقرير المشتريات")}),t.jsx("p",{children:n("purchaseReportDesc","إجمالي المشتريات والموردين")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:Ut(Ke.reduce(((e,t)=>e+t.finalTotal),0))}),t.jsxs("small",{children:[Ke.length," ",n("invoice","فاتورة")]})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e="ar"===y,t="ar"===y?"ar":"fr"===y?"fr":"en",a=Ce.reduce(((e,t)=>e+(parseFloat(t.finalTotal)||0)),0),s=m(Ce,Dn).totalCost,i=tt.reduce(((e,t)=>e+(parseFloat(t.amount)||0)),0),r=a-s-i,o=a>0?(r/a*100).toFixed(2):0,l=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("profitReport","تقرير الأرباح")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${e?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .profit-summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }\n          .profit-card { background: white; padding: 25px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n          .profit-card.positive { border-left: 5px solid #27ae60; }\n          .profit-card.negative { border-left: 5px solid #e74c3c; }\n          .profit-card h3 { margin: 0 0 15px 0; color: #34495e; }\n          .profit-card .value { font-size: 28px; font-weight: bold; margin-bottom: 10px; }\n          .profit-card.positive .value { color: #27ae60; }\n          .profit-card.negative .value { color: #e74c3c; }\n          .profit-card .description { color: #7f8c8d; font-size: 14px; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .header, .profit-card { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>💎 ${n("profitLossReport","تقرير الأرباح والخسائر")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("financialAnalysis","التحليل المالي")}</p>\n        </div>\n\n        <div class="profit-summary">\n          <div class="profit-card positive">\n            <h3>${n("totalSales","إجمالي المبيعات")}</h3>\n            <div class="value">${Ut(a)}</div>\n            <div class="description">${n("totalRevenueFromSales","إجمالي الإيرادات من المبيعات")}</div>\n          </div>\n\n          <div class="profit-card negative">\n            <h3>${n("costOfGoodsSold","تكلفة البضاعة المباعة")}</h3>\n            <div class="value">${Ut(s)}</div>\n            <div class="description">${n("costOfProductsSold","تكلفة المنتجات المباعة")}</div>\n          </div>\n\n          <div class="profit-card negative">\n            <h3>${n("operatingExpenses","المصاريف التشغيلية")}</h3>\n            <div class="value">${Ut(i)}</div>\n            <div class="description">${n("salariesRentOtherExpenses","الرواتب، الإيجار، والمصاريف الأخرى")}</div>\n          </div>\n\n          <div class="profit-card ${r>=0?"positive":"negative"}">\n            <h3>${n("netProfitLoss","صافي الربح/الخسارة")}</h3>\n            <div class="value">${Ut(r)}</div>\n            <div class="description">${r>=0?n("netProfit","ربح"):n("netLoss","خسارة")} ${n("net","صافية")}</div>\n          </div>\n\n          <div class="profit-card">\n            <h3>${n("profitMargin","هامش الربح")}</h3>\n            <div class="value" style="color: #3498db;">${o}%</div>\n            <div class="description">${n("profitPercentageFromSales","نسبة الربح من المبيعات")}</div>\n          </div>\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,c=window.open("","_blank","width=1200,height=800");c.document.write(l),c.document.close(),c.onload=()=>setTimeout((()=>c.print()),1e3),Zt(n("profitReportOpened","💎 تم فتح تقرير الأرباح للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"💎"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("profitReport","تقرير الأرباح")}),t.jsx("p",{children:n("profitReportDesc","تحليل الأرباح والخسائر")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:Ut(Ce.reduce(((e,t)=>e+t.finalTotal),0)-Ke.reduce(((e,t)=>e+t.finalTotal),0))}),t.jsx("small",{children:n("netProfit","صافي الربح")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e=Ce.filter((e=>"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0),t=Ce.filter((e=>"دين"===e.paymentMethod||"Crédit"===e.paymentMethod||"Credit"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0),a=Ke.filter((e=>"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0),s=Ke.filter((e=>"دين"===e.paymentMethod||"Crédit"===e.paymentMethod||"Credit"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0),i=e-a,r="ar"===y,o="ar"===y?"ar":"fr"===y?"fr":"en",l=`\n      <!DOCTYPE html>\n      <html dir="${r?"rtl":"ltr"}" lang="${o}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("cashFlowReport","تقرير التدفق النقدي")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${r?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .cash-flow-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n          .cash-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n          .cash-card.inflow { border-left: 5px solid #27ae60; }\n          .cash-card.outflow { border-left: 5px solid #e74c3c; }\n          .cash-card.net { border-left: 5px solid #3498db; }\n          .cash-card h3 { margin: 0 0 10px 0; color: #34495e; }\n          .cash-card .value { font-size: 24px; font-weight: bold; margin-bottom: 5px; }\n          .cash-card.inflow .value { color: #27ae60; }\n          .cash-card.outflow .value { color: #e74c3c; }\n          .cash-card.net .value { color: #3498db; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .header, .cash-card { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>💸 ${n("cashFlowReport","تقرير التدفق النقدي")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===o?"ar-DZ":"fr"===o?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("cashManagement","إدارة النقدية")}</p>\n        </div>\n\n        <div class="cash-flow-grid">\n          <div class="cash-card inflow">\n            <h3>${n("cashSales","المبيعات النقدية")}</h3>\n            <div class="value">${Ut(e)}</div>\n            <small>${n("cashInflow","نقد داخل")}</small>\n          </div>\n\n          <div class="cash-card outflow">\n            <h3>${n("cashPurchases","المشتريات النقدية")}</h3>\n            <div class="value">${Ut(a)}</div>\n            <small>${n("cashOutflow","نقد خارج")}</small>\n          </div>\n\n          <div class="cash-card net">\n            <h3>${n("netCashFlow","صافي التدفق النقدي")}</h3>\n            <div class="value">${Ut(i)}</div>\n            <small>${i>=0?n("cashSurplus","فائض نقدي"):n("cashDeficit","عجز نقدي")}</small>\n          </div>\n\n          <div class="cash-card">\n            <h3>${n("creditSales","المبيعات الآجلة")}</h3>\n            <div class="value" style="color: #f39c12;">${Ut(t)}</div>\n            <small>${n("receivables","ذمم مدينة")}</small>\n          </div>\n\n          <div class="cash-card">\n            <h3>${n("creditPurchases","المشتريات الآجلة")}</h3>\n            <div class="value" style="color: #9b59b6;">${Ut(s)}</div>\n            <small>${n("payables","ذمم دائنة")}</small>\n          </div>\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,c=window.open("","_blank","width=1200,height=800");c.document.write(l),c.document.close(),c.onload=()=>setTimeout((()=>c.print()),1e3),Zt(n("cashFlowReportOpened","💸 تم فتح تقرير التدفق النقدي للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"💸"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("cashFlowReport","تقرير التدفق النقدي")}),t.jsx("p",{children:n("cashFlowReportDesc","حركة النقد الداخل والخارج")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:Ut(Ce.filter((e=>"نقداً"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0))}),t.jsx("small",{children:n("inCash","نقداً")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]})]})]}),t.jsxs("div",{className:"report-category",children:[t.jsxs("div",{className:"category-header",children:[t.jsxs("h2",{children:["📦 ",n("inventoryReports","تقارير المخزون")]}),t.jsx("p",{children:n("inventoryReportsDesc","حالة المخزون والمنتجات")})]}),t.jsxs("div",{className:"reports-grid",children:[t.jsxs("div",{className:"report-card",onClick:()=>{Ht(Dn,"ar"===y?"تقرير_المخزون_العام":"fr"===y?"Rapport_Inventaire_General":"General_Inventory_Report")?Zt("ar"===y?"📊 تم تصدير تقرير المخزون العام بتنسيق Excel":"fr"===y?"📊 Rapport d'inventaire général exporté en Excel avec succès":"📊 General inventory report exported to Excel successfully","success",3e3):Zt("ar"===y?"❌ حدث خطأ أثناء تصدير التقرير":"fr"===y?"❌ Erreur lors de l'export du rapport":"❌ Error occurred while exporting report","error",3e3)},children:[t.jsx("div",{className:"report-icon",children:"📊"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("generalInventoryReport","تقرير المخزون العام")}),t.jsx("p",{children:n("generalInventoryReportDesc","جميع المنتجات وحالة المخزون")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:Dn.length}),t.jsx("small",{children:n("product","منتج")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e=Dn.filter((e=>e.stock<=e.minStock));Ht(e,"ar"===y?"تقرير_المخزون_المنخفض":"fr"===y?"Rapport_Stock_Faible":"Low_Stock_Report")?Zt("ar"===y?"⚠️ تم تصدير تقرير المخزون المنخفض بتنسيق Excel":"fr"===y?"⚠️ Rapport de stock faible exporté en Excel avec succès":"⚠️ Low stock report exported to Excel successfully","success",3e3):Zt("ar"===y?"❌ حدث خطأ أثناء تصدير التقرير":"fr"===y?"❌ Erreur lors de l'export du rapport":"❌ Error occurred while exporting report","error",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"⚠️"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("lowStockReport","تقرير المخزون المنخفض")}),t.jsx("p",{children:n("lowStockReportDesc","المنتجات التي تحتاج إعادة تموين")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:Dn.filter((e=>e.stock<=e.minStock)).length}),t.jsx("small",{children:n("product","منتج")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e="ar"===y,t="ar"===y?"ar":"fr"===y?"fr":"en",a=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("inventoryValueReport","تقرير قيمة المخزون")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${e?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .value-summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n          .value-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n          .value-card h3 { margin: 0 0 10px 0; color: #34495e; }\n          .value-card .value { font-size: 24px; font-weight: bold; color: #27ae60; }\n          .table-container { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          table { width: 100%; border-collapse: collapse; }\n          th { background: #27ae60; color: white; padding: 15px; text-align: center; font-weight: bold; }\n          td { padding: 12px; text-align: center; border-bottom: 1px solid #ecf0f1; }\n          tr:nth-child(even) { background: #f8f9fa; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .header, .value-card, .table-container { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>💰 ${n("inventoryValueReport","تقرير قيمة المخزون")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("inventoryValuation","تقييم المخزون")}</p>\n        </div>\n\n        <div class="value-summary">\n          <div class="value-card">\n            <h3>${n("totalInventoryValue","إجمالي قيمة المخزون")}</h3>\n            <div class="value">${Ut(Dn.reduce(((e,t)=>e+t.stock*t.buyPrice),0))}</div>\n          </div>\n          <div class="value-card">\n            <h3>${n("productCount","عدد المنتجات")}</h3>\n            <div class="value">${Dn.length}</div>\n          </div>\n          <div class="value-card">\n            <h3>${n("totalQuantities","إجمالي الكميات")}</h3>\n            <div class="value">${Dn.reduce(((e,t)=>e+t.stock),0)}</div>\n          </div>\n          <div class="value-card">\n            <h3>${n("averageProductValue","متوسط قيمة المنتج")}</h3>\n            <div class="value">${Ut(Dn.length>0?Dn.reduce(((e,t)=>e+t.stock*t.buyPrice),0)/Dn.length:0)}</div>\n          </div>\n        </div>\n\n        <div class="table-container">\n          <table>\n            <thead>\n              <tr>\n                <th>${n("productName","اسم المنتج")}</th>\n                <th>${n("category","الفئة")}</th>\n                <th>${n("quantity","الكمية")}</th>\n                <th>${n("buyPrice","سعر الشراء")}</th>\n                <th>${n("totalValue","القيمة الإجمالية")}</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${Dn.map((e=>`\n                <tr>\n                  <td>${e.name}</td>\n                  <td>${e.category}</td>\n                  <td>${e.stock}</td>\n                  <td>${Ut(e.buyPrice)}</td>\n                  <td>${Ut(e.stock*e.buyPrice)}</td>\n                </tr>\n              `)).join("")}\n            </tbody>\n          </table>\n        </div>\n\n        <div class="footer">\n          <p>تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ</p>\n          <p>© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة</p>\n        </div>\n      </body>\n      </html>\n    `,s=window.open("","_blank","width=1200,height=800");s.document.write(a),s.document.close(),s.onload=()=>setTimeout((()=>s.print()),1e3),Zt(n("inventoryValueReportOpened","💰 تم فتح تقرير قيمة المخزون للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"💰"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("inventoryValueReport","تقرير قيمة المخزون")}),t.jsx("p",{children:n("inventoryValueReportDesc","القيمة الإجمالية للمخزون")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:Ut(Dn.reduce(((e,t)=>e+t.stock*t.buyPrice),0))}),t.jsx("small",{children:n("totalValue","قيمة إجمالية")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e="ar"===y,t="ar"===y?"ar":"fr"===y?"fr":"en",a=[...new Set(Dn.map((e=>e.category)))].map((e=>{const t=Dn.filter((t=>t.category===e));return{category:e,count:t.length,totalStock:t.reduce(((e,t)=>e+t.stock),0),totalValue:t.reduce(((e,t)=>e+t.stock*t.buyPrice),0)}})),s=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("categoriesReport","تقرير الفئات")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${e?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .table-container { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          table { width: 100%; border-collapse: collapse; }\n          th { background: #9b59b6; color: white; padding: 15px; text-align: center; font-weight: bold; }\n          td { padding: 12px; text-align: center; border-bottom: 1px solid #ecf0f1; }\n          tr:nth-child(even) { background: #f8f9fa; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .header, .table-container { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>🏷️ ${n("categoriesReport","تقرير الفئات")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("categoryAnalysis","تحليل الفئات")}</p>\n        </div>\n\n        <div class="table-container">\n          <table>\n            <thead>\n              <tr>\n                <th>${n("category","الفئة")}</th>\n                <th>${n("productCount","عدد المنتجات")}</th>\n                <th>${n("totalQuantities","إجمالي الكميات")}</th>\n                <th>${n("totalValue","القيمة الإجمالية")}</th>\n                <th>${n("inventoryPercentage","النسبة من المخزون")}</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${a.map((e=>`\n                <tr>\n                  <td>${e.category}</td>\n                  <td>${e.count}</td>\n                  <td>${e.totalStock}</td>\n                  <td>${Ut(e.totalValue)}</td>\n                  <td>${(e.totalValue/Dn.reduce(((e,t)=>e+t.stock*t.buyPrice),0)*100).toFixed(1)}%</td>\n                </tr>\n              `)).join("")}\n            </tbody>\n          </table>\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,i=window.open("","_blank","width=1200,height=800");i.document.write(s),i.document.close(),i.onload=()=>setTimeout((()=>i.print()),1e3),Zt(n("categoriesReportOpened","🏷️ تم فتح تقرير الفئات للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"🏷️"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("categoriesReport","تقرير الفئات")}),t.jsx("p",{children:n("categoriesReportDesc","تحليل المنتجات حسب الفئات")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:[...new Set(Dn.map((e=>e.category)))].length}),t.jsx("small",{children:n("category","فئة")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]})]})]}),t.jsxs("div",{className:"report-category",children:[t.jsxs("div",{className:"category-header",children:[t.jsxs("h2",{children:["👥 ",n("customerReports","تقارير العملاء")]}),t.jsx("p",{children:n("customerReportsDesc","تحليل العملاء والمبيعات")})]}),t.jsxs("div",{className:"reports-grid",children:[t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e="ar"===y,t="ar"===y?"ar":"fr"===y?"fr":"en",a=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("customersReport","تقرير العملاء")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${e?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n          .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n          .stat-card h3 { margin: 0 0 10px 0; color: #34495e; }\n          .stat-card .value { font-size: 24px; font-weight: bold; color: #8e44ad; }\n          .table-container { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          table { width: 100%; border-collapse: collapse; }\n          th { background: #8e44ad; color: white; padding: 15px; text-align: center; font-weight: bold; }\n          td { padding: 12px; text-align: center; border-bottom: 1px solid #ecf0f1; }\n          tr:nth-child(even) { background: #f8f9fa; }\n          .status-active { color: #27ae60; font-weight: bold; }\n          .status-inactive { color: #e74c3c; font-weight: bold; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .header, .stat-card, .table-container { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>👥 ${n("customersReport","تقرير العملاء التفصيلي")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("customerManagement","إدارة العملاء")}</p>\n        </div>\n\n        <div class="stats-grid">\n          <div class="stat-card">\n            <h3>${n("totalCustomers","إجمالي العملاء")}</h3>\n            <div class="value">${ba.length}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("activeCustomers","العملاء النشطون")}</h3>\n            <div class="value">${ba.filter((e=>"نشط"===e.status)).length}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("newCustomersThisMonth","العملاء الجدد هذا الشهر")}</h3>\n            <div class="value">${ba.filter((e=>{const t=new Date(e.createdAt),n=new Date;return t.getMonth()===n.getMonth()&&t.getFullYear()===n.getFullYear()})).length}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("averagePurchasesPerCustomer","متوسط المشتريات للعميل")}</h3>\n            <div class="value">${ba.length>0?Math.round(Ce.length/ba.length):0}</div>\n          </div>\n        </div>\n\n        <div class="table-container">\n          <table>\n            <thead>\n              <tr>\n                <th>${n("customerNumber","رقم العميل")}</th>\n                <th>${n("customerName","اسم العميل")}</th>\n                <th>${n("phoneNumber","رقم الهاتف")}</th>\n                <th>${n("address","العنوان")}</th>\n                <th>${n("registrationDate","تاريخ التسجيل")}</th>\n                <th>${n("purchaseCount","عدد المشتريات")}</th>\n                <th>${n("totalPurchases","إجمالي المشتريات")}</th>\n                <th>${n("status","الحالة")}</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${ba.map((e=>{const a=Ce.filter((t=>t.customerName===e.name)),s=a.reduce(((e,t)=>e+t.finalTotal),0);return`\n                  <tr>\n                    <td>${e.id}</td>\n                    <td>${e.name}</td>\n                    <td>${e.phone||n("notSpecified","غير محدد")}</td>\n                    <td>${e.address||n("notSpecified","غير محدد")}</td>\n                    <td>${new Date(e.createdAt).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</td>\n                    <td>${a.length}</td>\n                    <td>${Ut(s)}</td>\n                    <td class="${"نشط"===e.status?"status-active":"status-inactive"}">${"نشط"===e.status?n("active","نشط"):n("inactive","غير نشط")}</td>\n                  </tr>\n                `})).join("")}\n            </tbody>\n          </table>\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,s=window.open("","_blank","width=1200,height=800");s.document.write(a),s.document.close(),s.onload=()=>setTimeout((()=>s.print()),1e3),Zt(n("customersReportOpened","👤 تم فتح تقرير العملاء للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"👤"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("customersReport","تقرير العملاء")}),t.jsx("p",{children:n("customersReportDesc","قائمة العملاء وبياناتهم")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:ba.length}),t.jsx("small",{children:n("customer","عميل")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e="ar"===y,t="ar"===y?"ar":"fr"===y?"fr":"en",a=Ce.filter((e=>"دين"===e.paymentMethod)),s={};a.forEach((e=>{s[e.customerName]||(s[e.customerName]={customerName:e.customerName,totalDebt:0,invoiceCount:0,invoices:[]}),s[e.customerName].totalDebt+=e.finalTotal,s[e.customerName].invoiceCount+=1,s[e.customerName].invoices.push(e)}));const i=Object.values(s),r=a.reduce(((e,t)=>e+t.finalTotal),0),o=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("debtorsReport","تقرير المدينين")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${e?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .alert-box { background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 10px; padding: 20px; margin-bottom: 30px; text-align: center; }\n          .alert-box h2 { color: #856404; margin: 0 0 10px 0; }\n          .alert-box .total-debt { font-size: 32px; font-weight: bold; color: #d63031; }\n          .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n          .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n          .stat-card h3 { margin: 0 0 10px 0; color: #34495e; }\n          .stat-card .value { font-size: 24px; font-weight: bold; color: #e74c3c; }\n          .table-container { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          table { width: 100%; border-collapse: collapse; }\n          th { background: #e74c3c; color: white; padding: 15px; text-align: center; font-weight: bold; }\n          td { padding: 12px; text-align: center; border-bottom: 1px solid #ecf0f1; }\n          tr:nth-child(even) { background: #f8f9fa; }\n          .high-debt { background: #ffebee !important; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .header, .stat-card, .table-container { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>💳 ${n("debtorsAndReceivables","تقرير المدينين والمستحقات")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("debtManagement","إدارة الديون")}</p>\n        </div>\n\n        <div class="alert-box">\n          <h2>⚠️ ${n("totalOutstandingAmount","إجمالي المبالغ المستحقة")}</h2>\n          <div class="total-debt">${Ut(r)}</div>\n          <p>${n("followUpRequired","يجب متابعة تحصيل هذه المبالغ")}</p>\n        </div>\n\n        <div class="stats-grid">\n          <div class="stat-card">\n            <h3>${n("debtorCustomers","عدد العملاء المدينين")}</h3>\n            <div class="value">${i.length}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("outstandingInvoices","عدد الفواتير المستحقة")}</h3>\n            <div class="value">${a.length}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("averageDebtPerCustomer","متوسط الدين للعميل")}</h3>\n            <div class="value">${Ut(i.length>0?r/i.length:0)}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("highestOutstandingAmount","أكبر مبلغ مستحق")}</h3>\n            <div class="value">${Ut(i.length>0?Math.max(...i.map((e=>e.totalDebt))):0)}</div>\n          </div>\n        </div>\n\n        <div class="table-container">\n          <table>\n            <thead>\n              <tr>\n                <th>${n("customerName","اسم العميل")}</th>\n                <th>${n("invoiceCount","عدد الفواتير")}</th>\n                <th>${n("totalOutstanding","إجمالي المبلغ المستحق")}</th>\n                <th>${n("oldestInvoice","أقدم فاتورة")}</th>\n                <th>${n("newestInvoice","أحدث فاتورة")}</th>\n                <th>${n("priorityLevel","مستوى الأولوية")}</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${i.sort(((e,t)=>t.totalDebt-e.totalDebt)).map((e=>{const a=e.invoices.sort(((e,t)=>new Date(e.date)-new Date(t.date)))[0],s=e.invoices.sort(((e,t)=>new Date(t.date)-new Date(e.date)))[0],i=e.totalDebt>5e4?n("high","عالية"):e.totalDebt>2e4?n("medium","متوسطة"):n("low","منخفضة");return`\n                  <tr class="${e.totalDebt>5e4?"high-debt":""}">\n                    <td>${e.customerName}</td>\n                    <td>${e.invoiceCount}</td>\n                    <td>${Ut(e.totalDebt)}</td>\n                    <td>${new Date(a.date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</td>\n                    <td>${new Date(s.date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</td>\n                    <td>${i}</td>\n                  </tr>\n                `})).join("")}\n            </tbody>\n          </table>\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,l=window.open("","_blank","width=1200,height=800");l.document.write(o),l.document.close(),l.onload=()=>setTimeout((()=>l.print()),1e3),Zt(n("debtorsReportOpened","💳 تم فتح تقرير المدينين للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"💳"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("debtorsReport","تقرير المدينين")}),t.jsx("p",{children:n("debtorsReportDesc","العملاء المدينين والمستحقات")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:Ut(Ce.filter((e=>"دين"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0))}),t.jsx("small",{children:n("totalDebts","إجمالي الديون")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e="ar"===y,t="ar"===y?"ar":"fr"===y?"fr":"en",a={};Ce.forEach((e=>{a[e.customerName]||(a[e.customerName]={name:e.customerName,totalPurchases:0,invoiceCount:0,averageInvoice:0,lastPurchase:e.date,firstPurchase:e.date}),a[e.customerName].totalPurchases+=e.finalTotal,a[e.customerName].invoiceCount+=1,new Date(e.date)>new Date(a[e.customerName].lastPurchase)&&(a[e.customerName].lastPurchase=e.date),new Date(e.date)<new Date(a[e.customerName].firstPurchase)&&(a[e.customerName].firstPurchase=e.date)})),Object.values(a).forEach((e=>{e.averageInvoice=e.totalPurchases/e.invoiceCount}));const s=Object.values(a).sort(((e,t)=>t.totalPurchases-e.totalPurchases)).slice(0,10),i=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("topCustomers","تقرير أفضل العملاء")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${e?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .top-customer-highlight { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px; padding: 25px; margin-bottom: 30px; text-align: center; }\n          .top-customer-highlight h2 { margin: 0 0 15px 0; font-size: 24px; }\n          .top-customer-highlight .customer-name { font-size: 32px; font-weight: bold; margin-bottom: 10px; }\n          .top-customer-highlight .customer-value { font-size: 28px; margin-bottom: 5px; }\n          .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n          .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n          .stat-card h3 { margin: 0 0 10px 0; color: #34495e; }\n          .stat-card .value { font-size: 24px; font-weight: bold; color: #f39c12; }\n          .table-container { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          table { width: 100%; border-collapse: collapse; }\n          th { background: #f39c12; color: white; padding: 15px; text-align: center; font-weight: bold; }\n          td { padding: 12px; text-align: center; border-bottom: 1px solid #ecf0f1; }\n          tr:nth-child(even) { background: #f8f9fa; }\n          .rank-1 { background: #fff9c4 !important; font-weight: bold; }\n          .rank-2 { background: #f0f0f0 !important; }\n          .rank-3 { background: #ffeaa7 !important; }\n          .rank { font-weight: bold; font-size: 18px; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .header, .stat-card, .table-container { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>🌟 ${n("topCustomers","تقرير أفضل العملاء")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("topCustomersAnalysis","تحليل العملاء المميزين")}</p>\n        </div>\n\n        ${s.length>0?`\n          <div class="top-customer-highlight">\n            <h2>🏆 ${n("topCustomer","العميل الأول")}</h2>\n            <div class="customer-name">${s[0].name}</div>\n            <div class="customer-value">${Ut(s[0].totalPurchases)}</div>\n            <p>${s[0].invoiceCount} ${n("invoice","فاتورة")} | ${n("averageInvoice","متوسط الفاتورة")}: ${Ut(s[0].averageInvoice)}</p>\n          </div>\n        `:""}\n\n        <div class="stats-grid">\n          <div class="stat-card">\n            <h3>${n("totalSalesTopTen","إجمالي مبيعات أفضل 10 عملاء")}</h3>\n            <div class="value">${Ut(s.reduce(((e,t)=>e+t.totalPurchases),0))}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("averagePurchasesTopCustomer","متوسط المشتريات للعميل المميز")}</h3>\n            <div class="value">${Ut(s.length>0?s.reduce(((e,t)=>e+t.totalPurchases),0)/s.length:0)}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("totalInvoicesCount","إجمالي الفواتير")}</h3>\n            <div class="value">${s.reduce(((e,t)=>e+t.invoiceCount),0)}</div>\n          </div>\n          <div class="stat-card">\n            <h3>${n("percentageOfTotalSales","نسبة من إجمالي المبيعات")}</h3>\n            <div class="value">${Ce.length>0?(s.reduce(((e,t)=>e+t.totalPurchases),0)/Ce.reduce(((e,t)=>e+t.finalTotal),0)*100).toFixed(1):0}%</div>\n          </div>\n        </div>\n\n        <div class="table-container">\n          <table>\n            <thead>\n              <tr>\n                <th>${n("ranking","الترتيب")}</th>\n                <th>${n("customerName","اسم العميل")}</th>\n                <th>${n("totalPurchases","إجمالي المشتريات")}</th>\n                <th>${n("invoiceCount","عدد الفواتير")}</th>\n                <th>${n("averageInvoice","متوسط الفاتورة")}</th>\n                <th>${n("firstPurchase","أول شراء")}</th>\n                <th>${n("lastPurchase","آخر شراء")}</th>\n                <th>${n("membershipPeriod","فترة العضوية")}</th>\n              </tr>\n            </thead>\n            <tbody>\n              ${s.map(((e,a)=>{const s=Math.floor((new Date(e.lastPurchase)-new Date(e.firstPurchase))/864e5);return`\n                  <tr class="${0===a?"rank-1":1===a?"rank-2":2===a?"rank-3":""}">\n                    <td class="rank">${0===a?"🥇":1===a?"🥈":2===a?"🥉":""} ${a+1}</td>\n                    <td>${e.name}</td>\n                    <td>${Ut(e.totalPurchases)}</td>\n                    <td>${e.invoiceCount}</td>\n                    <td>${Ut(e.averageInvoice)}</td>\n                    <td>${new Date(e.firstPurchase).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</td>\n                    <td>${new Date(e.lastPurchase).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</td>\n                    <td>${s} ${n("days","يوم")}</td>\n                  </tr>\n                `})).join("")}\n            </tbody>\n          </table>\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,r=window.open("","_blank","width=1200,height=800");r.document.write(i),r.document.close(),r.onload=()=>setTimeout((()=>r.print()),1e3),Zt(n("topCustomersReportOpened","🌟 تم فتح تقرير أفضل العملاء للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"🌟"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("topCustomersReport","أفضل العملاء")}),t.jsx("p",{children:n("topCustomersReportDesc","العملاء الأكثر شراءً")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:"TOP 10"}),t.jsx("small",{children:n("customer","عميل")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e=new Date,t=e.getMonth(),a=e.getFullYear(),s=ba.length,i=ba.filter((e=>"نشط"===e.status)).length,r=ba.filter((e=>{const n=new Date(e.createdAt);return n.getMonth()===t&&n.getFullYear()===a})).length,o=[...new Set(Ce.map((e=>e.customerName)))].length,l=[...new Set(Ce.filter((e=>"دين"===e.paymentMethod)).map((e=>e.customerName)))].length,c={};Ce.forEach((e=>{c[e.customerName]||(c[e.customerName]=0),c[e.customerName]++}));const d=Object.values(c).filter((e=>1===e)).length,m=Object.values(c).filter((e=>e>=2&&e<=5)).length,h=Object.values(c).filter((e=>e>=6&&e<=15)).length,p=Object.values(c).filter((e=>e>15)).length,u={};ba.forEach((e=>{const t=new Date(e.createdAt),n=`${t.getFullYear()}-${String(t.getMonth()+1).padStart(2,"0")}`;u[n]||(u[n]=0),u[n]++}));const x="ar"===y,v="ar"===y?"ar":"fr"===y?"fr":"en",g=`\n      <!DOCTYPE html>\n      <html dir="${x?"rtl":"ltr"}" lang="${v}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("advancedCustomerAnalysis","تحليل العملاء المتقدم")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${x?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .analysis-section { background: white; border-radius: 10px; padding: 25px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .analysis-section h2 { color: #2c3e50; margin: 0 0 20px 0; font-size: 22px; border-bottom: 2px solid #3498db; padding-bottom: 10px; }\n          .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 25px; }\n          .stat-card { background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border-left: 4px solid #3498db; }\n          .stat-card h4 { margin: 0 0 8px 0; color: #34495e; font-size: 14px; }\n          .stat-card .value { font-size: 20px; font-weight: bold; color: #2c3e50; }\n          .frequency-chart { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; }\n          .frequency-item { background: #ecf0f1; padding: 15px; border-radius: 8px; text-align: center; }\n          .frequency-item h4 { margin: 0 0 8px 0; color: #34495e; }\n          .frequency-item .count { font-size: 24px; font-weight: bold; color: #e67e22; }\n          .frequency-item .percentage { font-size: 12px; color: #7f8c8d; }\n          .insights-list { list-style: none; padding: 0; }\n          .insights-list li { background: #e8f6f3; padding: 12px; margin-bottom: 8px; border-radius: 6px; border-left: 4px solid #27ae60; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .analysis-section { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>📊 ${n("advancedCustomerAnalysis","تحليل العملاء المتقدم")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===v?"ar-DZ":"fr"===v?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("businessIntelligence","ذكاء الأعمال")}</p>\n        </div>\n\n        <div class="analysis-section">\n          <h2>📈 ${n("generalCustomerStats","إحصائيات العملاء العامة")}</h2>\n          <div class="stats-grid">\n            <div class="stat-card">\n              <h4>${n("totalCustomers","إجمالي العملاء")}</h4>\n              <div class="value">${s}</div>\n            </div>\n            <div class="stat-card">\n              <h4>${n("activeCustomers","العملاء النشطون")}</h4>\n              <div class="value">${i}</div>\n            </div>\n            <div class="stat-card">\n              <h4>${n("newCustomersThisMonth","عملاء جدد هذا الشهر")}</h4>\n              <div class="value">${r}</div>\n            </div>\n            <div class="stat-card">\n              <h4>${n("customersWithPurchases","عملاء لديهم مشتريات")}</h4>\n              <div class="value">${o}</div>\n            </div>\n            <div class="stat-card">\n              <h4>${n("debtorCustomersCount","عملاء مدينون")}</h4>\n              <div class="value">${l}</div>\n            </div>\n            <div class="stat-card">\n              <h4>${n("conversionRate","معدل التحويل")}</h4>\n              <div class="value">${s>0?(o/s*100).toFixed(1):0}%</div>\n            </div>\n          </div>\n        </div>\n\n        <div class="analysis-section">\n          <h2>🔄 ${n("purchaseFrequencyAnalysis","تحليل تكرار الشراء")}</h2>\n          <div class="frequency-chart">\n            <div class="frequency-item">\n              <h4>${n("oneTimeBuyers","مشترون لمرة واحدة")}</h4>\n              <div class="count">${d}</div>\n              <div class="percentage">${o>0?(d/o*100).toFixed(1):0}%</div>\n            </div>\n            <div class="frequency-item">\n              <h4>${n("occasionalBuyers","مشترون أحياناً")}</h4>\n              <div class="count">${m}</div>\n              <div class="percentage">${o>0?(m/o*100).toFixed(1):0}%</div>\n            </div>\n            <div class="frequency-item">\n              <h4>${n("regularBuyers","مشترون منتظمون")}</h4>\n              <div class="count">${h}</div>\n              <div class="percentage">${o>0?(h/o*100).toFixed(1):0}%</div>\n            </div>\n            <div class="frequency-item">\n              <h4>${n("frequentBuyers","مشترون دائمون")}</h4>\n              <div class="count">${p}</div>\n              <div class="percentage">${o>0?(p/o*100).toFixed(1):0}%</div>\n            </div>\n          </div>\n        </div>\n\n        <div class="analysis-section">\n          <h2>💡 ${n("insightsAndRecommendations","رؤى وتوصيات")}</h2>\n          <ul class="insights-list">\n            <li><strong>${n("retentionRate","معدل الاحتفاظ")}:</strong> ${d>0?`${(100*(1-d/o)).toFixed(1)}%`:"0%"} ${n("customersReturnToBuy","من العملاء يعودون للشراء مرة أخرى")}</li>\n            <li><strong>${n("growthOpportunity","فرصة النمو")}:</strong> ${d} ${n("oneTimePurchaseOpportunity","عميل اشترى مرة واحدة فقط - فرصة لحملات إعادة الاستهداف")}</li>\n            <li><strong>${n("loyalCustomers","العملاء المخلصون")}:</strong> ${p} ${n("regularPurchasers","عميل يشترون بانتظام - يجب الاهتمام بهم وتقديم عروض خاصة")}</li>\n            <li><strong>${n("customerGrowth","نمو العملاء")}:</strong> ${r} ${n("newCustomersThisMonthInsight","عميل جديد هذا الشهر")} ${r>0?n("positiveGrowth","- نمو إيجابي"):n("needsMarketingImprovement","- يحتاج تحسين التسويق")}</li>\n            <li><strong>${n("debtManagement","إدارة الديون")}:</strong> ${l} ${n("debtFollowUpNeeded","عميل لديه ديون - يحتاج متابعة للتحصيل")}</li>\n          </ul>\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,b=window.open("","_blank","width=1200,height=800");b.document.write(g),b.document.close(),b.onload=()=>setTimeout((()=>b.print()),1e3),Zt(n("customerAnalysisReportOpened","📊 تم فتح تحليل العملاء المتقدم للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"📊"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("customerAnalysisReport","تحليل العملاء")}),t.jsx("p",{children:n("customerAnalysisReportDesc","إحصائيات مفصلة للعملاء")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:ba.filter((e=>"نشط"===e.status)).length}),t.jsx("small",{children:n("activeCustomer","عميل نشط")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]})]})]}),t.jsxs("div",{className:"report-category",children:[t.jsxs("div",{className:"category-header",children:[t.jsxs("h2",{children:["📈 ",n("performanceReports","تقارير الأداء")]}),t.jsx("p",{children:n("performanceReportsDesc","مؤشرات الأداء والإحصائيات")})]}),t.jsxs("div",{className:"reports-grid",children:[t.jsxs("div",{className:"report-card",onClick:()=>{h(Ce,Dn,tt,Ut,Zt,y)},children:[t.jsx("div",{className:"report-icon",children:"📅"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("dailyReport","التقرير اليومي")}),t.jsx("p",{children:n("dailyReportDesc","ملخص العمليات اليومية")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:(new Date).toLocaleDateString("ar"===y?"ar-DZ":"fr"===y?"fr-FR":"en-US")}),t.jsx("small",{children:n("today","اليوم")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card advanced-card",children:[t.jsx("div",{className:"report-icon",children:"📊"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("advancedReports","تقارير متقدمة")}),t.jsx("p",{children:n("advancedReportsDesc","تقارير مفصلة مع فلاتر متقدمة")}),t.jsxs("div",{className:"report-controls",children:[t.jsxs("div",{className:"date-selector",children:[t.jsx("label",{children:n("reportType","نوع التقرير:")}),t.jsxs("select",{value:Pn.reportType,onChange:e=>Pn.setReportType(e.target.value),children:[t.jsx("option",{value:"daily",children:n("daily","يومي")}),t.jsx("option",{value:"monthly",children:n("monthly","شهري")})]})]}),"daily"===Pn.reportType?t.jsxs("div",{className:"date-selector",children:[t.jsx("label",{children:n("date","التاريخ:")}),t.jsx("input",{type:"date",value:Pn.selectedDate,onChange:e=>Pn.setSelectedDate(e.target.value)})]}):t.jsxs("div",{className:"date-selector",children:[t.jsx("label",{children:n("month","الشهر:")}),t.jsx("input",{type:"month",value:Pn.selectedMonth,onChange:e=>Pn.setSelectedMonth(e.target.value)})]}),t.jsxs("button",{className:"btn btn-primary btn-sm",onClick:Pn.printReport,children:["🖨️ ",n("print","طباعة")]})]})]})]}),t.jsxs("div",{className:"report-card",onClick:()=>{p(Ce,Dn,tt,Ut,Zt,y)},children:[t.jsx("div",{className:"report-icon",children:"📆"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("monthlyReport","التقرير الشهري")}),t.jsx("p",{children:n("monthlyReportDesc","ملخص العمليات الشهرية")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:n(`month${(new Date).getMonth()+1}`,d((new Date).getMonth()))}),t.jsx("small",{children:n("currentMonth","الشهر الحالي")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>{u(Ce,Dn,tt,Ut,Zt,y)},children:[t.jsx("div",{className:"report-icon",children:"🗓️"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("yearlyReport","التقرير السنوي")}),t.jsx("p",{children:n("yearlyReportDesc","ملخص العمليات السنوية")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:(new Date).getFullYear()}),t.jsx("small",{children:n("currentYear","السنة الحالية")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]}),t.jsxs("div",{className:"report-card",onClick:()=>(()=>{const e="ar"===y,t="ar"===y?"ar":"fr"===y?"fr":"en",a=Ce.reduce(((e,t)=>e+t.finalTotal),0),s=a-m(Ce,Dn).totalCost-tt.reduce(((e,t)=>e+parseFloat(t.amount)),0),i=a>0?(s/a*100).toFixed(2):0,r=new Date,o=r.getMonth(),l=r.getFullYear(),c=r.toISOString().split("T")[0],d=Ce.filter((e=>e.date===c));m(d,Dn).totalCost,tt.filter((e=>e.date===c)).reduce(((e,t)=>e+parseFloat(t.amount)),0);const h=d.reduce(((e,t)=>e+t.finalTotal),0),p=Ce.filter((e=>{const t=new Date(e.date);return t.getMonth()===o&&t.getFullYear()===l}));m(p,Dn).totalCost,tt.filter((e=>{const t=new Date(e.date);return t.getMonth()===o&&t.getFullYear()===l})).reduce(((e,t)=>e+parseFloat(t.amount)),0);const u=p.reduce(((e,t)=>e+t.finalTotal),0),x=[...new Set(Ce.map((e=>e.customerName)))].length,v=Ce.length>0?a/Ce.length:0,g=[...new Set(Ce.filter((e=>"دين"===e.paymentMethod)).map((e=>e.customerName)))].length,b=Ce.filter((e=>"دين"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0),f=Dn.reduce(((e,t)=>e+t.stock*t.buyPrice),0),j=Dn.filter((e=>e.stock<=e.minStock)).length,N=Dn.filter((e=>0===e.stock)).length,$=f>0?(a/f).toFixed(2):0,w=0===o?11:o-1,S=0===o?l-1:l,k=Ce.filter((e=>{const t=new Date(e.date);return t.getMonth()===w&&t.getFullYear()===S})).reduce(((e,t)=>e+t.finalTotal),0),C=k>0?((u-k)/k*100).toFixed(1):0,I=Ce.filter((e=>"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0);Ce.filter((e=>"دين"===e.paymentMethod||"Crédit"===e.paymentMethod||"Credit"===e.paymentMethod)).reduce(((e,t)=>e+t.finalTotal),0);const D=a>0?(I/a*100).toFixed(1):0,T=Ce.length>0?a/[...new Set(Ce.map((e=>e.date)))].length:0;Ce.length,Math.max([...new Set(Ce.map((e=>e.date)))].length,1);const P=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${t}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("performanceIndicators","تقرير مؤشرات الأداء الرئيسية")} - ${n("accountingSystem","نظام المحاسبي")}</title>\n        <style>\n          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${e?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n          .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n          .header p { color: #7f8c8d; margin: 5px 0; }\n          .kpi-dashboard { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px; padding: 30px; margin-bottom: 30px; text-align: center; }\n          .kpi-dashboard h2 { margin: 0 0 20px 0; font-size: 32px; }\n          .kpi-overview { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }\n          .kpi-card { background: rgba(255,255,255,0.15); padding: 20px; border-radius: 10px; backdrop-filter: blur(10px); }\n          .kpi-card h4 { margin: 0 0 10px 0; font-size: 14px; opacity: 0.9; }\n          .kpi-card .value { font-size: 24px; font-weight: bold; margin-bottom: 5px; }\n          .kpi-card .trend { font-size: 12px; opacity: 0.8; }\n          .kpi-section { background: white; border-radius: 15px; padding: 25px; margin-bottom: 30px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n          .kpi-section h3 { color: #2c3e50; margin: 0 0 20px 0; font-size: 22px; border-bottom: 2px solid #3498db; padding-bottom: 10px; }\n          .kpi-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }\n          .metric-card { background: #f8f9fa; padding: 20px; border-radius: 10px; text-align: center; position: relative; overflow: hidden; }\n          .metric-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #3498db, #2ecc71); }\n          .metric-card h4 { margin: 0 0 10px 0; color: #34495e; font-size: 16px; }\n          .metric-card .metric-value { font-size: 28px; font-weight: bold; color: #2c3e50; margin-bottom: 8px; }\n          .metric-card .metric-desc { font-size: 14px; color: #7f8c8d; }\n          .metric-card .metric-icon { font-size: 40px; margin-bottom: 15px; }\n          .performance-indicator { display: inline-block; padding: 4px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; margin-top: 8px; }\n          .indicator-excellent { background: #d5f4e6; color: #27ae60; }\n          .indicator-good { background: #fff3cd; color: #f39c12; }\n          .indicator-warning { background: #f8d7da; color: #e74c3c; }\n          .insights-section { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border-radius: 15px; padding: 25px; margin-bottom: 30px; }\n          .insights-section h3 { margin: 0 0 20px 0; font-size: 22px; }\n          .insights-list { list-style: none; padding: 0; margin: 0; }\n          .insights-list li { background: rgba(255,255,255,0.1); padding: 15px; margin-bottom: 10px; border-radius: 8px; border-left: 4px solid rgba(255,255,255,0.3); }\n          .insights-list li strong { color: #fff; }\n          .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n          @media print { body { margin: 0; background: white; } .kpi-section, .insights-section { box-shadow: none; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>🎯 ${n("kpiDashboard","تقرير مؤشرات الأداء الرئيسية (KPI)")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}</p>\n          <p>${n("accountingSystem","نظام المحاسبي")} - ${n("performanceOverview","لوحة قيادة الأداء")}</p>\n        </div>\n\n        <div class="kpi-dashboard">\n          <h2>📊 ${n("performanceOverview","نظرة عامة على الأداء")}</h2>\n          <div class="kpi-overview">\n            <div class="kpi-card">\n              <h4>${n("totalSales","إجمالي المبيعات")}</h4>\n              <div class="value">${Ut(a)}</div>\n              <div class="trend">${n("allPeriods","جميع الفترات")}</div>\n            </div>\n            <div class="kpi-card">\n              <h4>${n("netProfit","صافي الربح")}</h4>\n              <div class="value">${Ut(s)}</div>\n              <div class="trend">${n("margin","هامش")} ${i}%</div>\n            </div>\n            <div class="kpi-card">\n              <h4>${n("todaySales","مبيعات اليوم")}</h4>\n              <div class="value">${Ut(h)}</div>\n              <div class="trend">${d.length} ${n("invoice","فاتورة")}</div>\n            </div>\n            <div class="kpi-card">\n              <h4>${n("monthlySales","مبيعات الشهر")}</h4>\n              <div class="value">${Ut(u)}</div>\n              <div class="trend">${n("growth","نمو")} ${C}%</div>\n            </div>\n          </div>\n        </div>\n\n        <div class="kpi-section">\n          <h3>💰 ${n("financialPerformance","المؤشرات المالية")}</h3>\n          <div class="kpi-grid">\n            <div class="metric-card">\n              <div class="metric-icon">💵</div>\n              <h4>${n("averageOrderValue","متوسط قيمة الطلب")}</h4>\n              <div class="metric-value">${Ut(v)}</div>\n              <div class="metric-desc">${n("averageInvoice","متوسط قيمة الفاتورة")}</div>\n              <div class="performance-indicator ${v>1e4?"indicator-excellent":v>5e3?"indicator-good":"indicator-warning"}">\n                ${v>1e4?n("excellent","ممتاز"):v>5e3?n("good","جيد"):n("warning","يحتاج تحسين")}\n              </div>\n            </div>\n            <div class="metric-card">\n              <div class="metric-icon">📈</div>\n              <h4>${n("profitMargin","هامش الربح")}</h4>\n              <div class="metric-value">${i}%</div>\n              <div class="metric-desc">${n("profitFromSales","نسبة الربح من المبيعات")}</div>\n              <div class="performance-indicator ${i>30?"indicator-excellent":i>15?"indicator-good":"indicator-warning"}">\n                ${i>30?n("excellent","ممتاز"):i>15?n("good","جيد"):n("warning","يحتاج تحسين")}\n              </div>\n            </div>\n            <div class="metric-card">\n              <div class="metric-icon">💳</div>\n              <h4>${n("cashRatio","نسبة المبيعات النقدية")}</h4>\n              <div class="metric-value">${D}%</div>\n              <div class="metric-desc">${n("cashVsCredit","المبيعات النقدية مقابل الآجلة")}</div>\n              <div class="performance-indicator ${D>70?"indicator-excellent":D>50?"indicator-good":"indicator-warning"}">\n                ${D>70?n("excellent","ممتاز"):D>50?n("good","جيد"):n("warning","يحتاج تحسين")}\n              </div>\n            </div>\n            <div class="metric-card">\n              <div class="metric-icon">📊</div>\n              <h4>${n("averageDailySales","متوسط المبيعات اليومية")}</h4>\n              <div class="metric-value">${Ut(T)}</div>\n              <div class="metric-desc">${n("salesVelocity","معدل المبيعات في اليوم")}</div>\n              <div class="performance-indicator ${T>5e4?"indicator-excellent":T>25e3?"indicator-good":"indicator-warning"}">\n                ${T>5e4?n("excellent","ممتاز"):T>25e3?n("good","جيد"):n("warning","يحتاج تحسين")}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class="kpi-section">\n          <h3>👥 ${n("customerMetrics","مؤشرات العملاء")}</h3>\n          <div class="kpi-grid">\n            <div class="metric-card">\n              <div class="metric-icon">👤</div>\n              <h4>${n("uniqueCustomers","إجمالي العملاء")}</h4>\n              <div class="metric-value">${x}</div>\n              <div class="metric-desc">${n("uniqueCustomersDesc","عدد العملاء الفريدين")}</div>\n              <div class="performance-indicator ${x>100?"indicator-excellent":x>50?"indicator-good":"indicator-warning"}">\n                ${x>100?n("largeCustomerBase","قاعدة عملاء كبيرة"):x>50?n("mediumCustomerBase","قاعدة عملاء متوسطة"):n("smallCustomerBase","قاعدة عملاء صغيرة")}\n              </div>\n            </div>\n            <div class="metric-card">\n              <div class="metric-icon">💳</div>\n              <h4>${n("customersWithDebt","العملاء المدينون")}</h4>\n              <div class="metric-value">${g}</div>\n              <div class="metric-desc">${n("customersWithDebtDesc","عدد العملاء الذين لديهم ديون")}</div>\n              <div class="performance-indicator ${g/x*100<20?"indicator-excellent":g/x*100<40?"indicator-good":"indicator-warning"}">\n                ${g/x*100<20?n("lowRatio","نسبة منخفضة"):g/x*100<40?n("mediumRatio","نسبة متوسطة"):n("highRatio","نسبة عالية")}\n              </div>\n            </div>\n            <div class="metric-card">\n              <div class="metric-icon">💰</div>\n              <h4>${n("totalDebt","إجمالي الديون")}</h4>\n              <div class="metric-value">${Ut(b)}</div>\n              <div class="metric-desc">${n("totalDebtDesc","المبالغ المستحقة من العملاء")}</div>\n              <div class="performance-indicator ${b/a*100<10?"indicator-excellent":b/a*100<25?"indicator-good":"indicator-warning"}">\n                ${b/a*100<10?n("lowRatio","نسبة منخفضة"):b/a*100<25?n("mediumRatio","نسبة متوسطة"):n("highRatio","نسبة عالية")}\n              </div>\n            </div>\n            <div class="metric-card">\n              <div class="metric-icon">🔄</div>\n              <h4>${n("purchaseFrequency","معدل تكرار الشراء")}</h4>\n              <div class="metric-value">${(Ce.length/x).toFixed(1)}</div>\n              <div class="metric-desc">${n("averageOrdersPerCustomer","متوسط الطلبات لكل عميل")}</div>\n              <div class="performance-indicator ${Ce.length/x>5?"indicator-excellent":Ce.length/x>3?"indicator-good":"indicator-warning"}">\n                ${Ce.length/x>5?n("highLoyalty","ولاء عالي"):Ce.length/x>3?n("mediumLoyalty","ولاء متوسط"):n("lowLoyalty","ولاء منخفض")}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class="kpi-section">\n          <h3>📦 ${n("inventoryMetrics","مؤشرات المخزون")}</h3>\n          <div class="kpi-grid">\n            <div class="metric-card">\n              <div class="metric-icon">💎</div>\n              <h4>${n("inventoryValue","قيمة المخزون")}</h4>\n              <div class="metric-value">${Ut(f)}</div>\n              <div class="metric-desc">${n("totalGoodsValue","إجمالي قيمة البضائع")}</div>\n              <div class="performance-indicator indicator-good">${n("inventoryInvestment","استثمار المخزون")}</div>\n            </div>\n            <div class="metric-card">\n              <div class="metric-icon">⚠️</div>\n              <h4>${n("lowStockItems","المنتجات منخفضة المخزون")}</h4>\n              <div class="metric-value">${j}</div>\n              <div class="metric-desc">${n("needsRestocking","منتجات تحتاج إعادة تموين")}</div>\n              <div class="performance-indicator ${j<5?"indicator-excellent":j<15?"indicator-good":"indicator-warning"}">\n                ${j<5?n("goodStock","مخزون جيد"):j<15?n("needsMonitoring","يحتاج متابعة"):n("urgentRestocking","يحتاج تموين عاجل")}\n              </div>\n            </div>\n            <div class="metric-card">\n              <div class="metric-icon">❌</div>\n              <h4>${n("outOfStockItems","المنتجات نفدت")}</h4>\n              <div class="metric-value">${N}</div>\n              <div class="metric-desc">${n("unavailableProducts","منتجات غير متوفرة")}</div>\n              <div class="performance-indicator ${0===N?"indicator-excellent":N<5?"indicator-good":"indicator-warning"}">\n                ${0===N?n("none","لا توجد"):N<5?n("few","قليلة"):n("many","كثيرة")}\n              </div>\n            </div>\n            <div class="metric-card">\n              <div class="metric-icon">🔄</div>\n              <h4>${n("inventoryTurnover","معدل دوران المخزون")}</h4>\n              <div class="metric-value">${$}</div>\n              <div class="metric-desc">${n("stockSalesSpeed","سرعة بيع المخزون")}</div>\n              <div class="performance-indicator ${$>4?"indicator-excellent":$>2?"indicator-good":"indicator-warning"}">\n                ${$>4?n("fastTurnover","دوران سريع"):$>2?n("mediumTurnover","دوران متوسط"):n("slowTurnover","دوران بطيء")}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class="insights-section">\n          <h3>💡 ${n("insightsAndRecommendations","رؤى الأداء والتوصيات")}</h3>\n          <ul class="insights-list">\n            <li><strong>${n("financialPerformance","الأداء المالي")}:</strong> ${i>20?n("excellentProfitMargin","هامش ربح ممتاز يدل على كفاءة التسعير"):n("improveProfitMargin","يمكن تحسين هامش الربح بمراجعة التكاليف والأسعار")}</li>\n            <li><strong>${n("customerManagement","إدارة العملاء")}:</strong> ${g/x*100<30?n("goodDebtManagement","إدارة جيدة للديون والائتمان"):n("improveCreditPolicies","يحتاج تحسين سياسات الائتمان ومتابعة التحصيل")}</li>\n            <li><strong>${n("inventoryManagement","إدارة المخزون")}:</strong> ${j<10?n("appropriateStockLevel","مستوى مخزون مناسب"):n("improveInventoryPlanning","يحتاج تحسين تخطيط المخزون وإعادة التموين")}</li>\n            <li><strong>${n("growth","النمو")}:</strong> ${C>0?`${n("positiveGrowth","نمو إيجابي")} ${C}% ${n("thisMonth","هذا الشهر")}`:n("needsGrowthStrategies","يحتاج استراتيجيات لتحفيز النمو")}</li>\n            <li><strong>${n("cashFlow","التدفق النقدي")}:</strong> ${D>60?n("healthyCashFlow","تدفق نقدي صحي مع نسبة مبيعات نقدية جيدة"):n("improveCashFlow","يحتاج تحسين التدفق النقدي وتقليل المبيعات الآجلة")}</li>\n          </ul>\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,M=window.open("","_blank","width=1200,height=800");M.document.write(P),M.document.close(),M.onload=()=>setTimeout((()=>M.print()),1e3),Zt(n("kpiReportOpened","🎯 تم فتح تقرير مؤشرات الأداء للطباعة"),"success",3e3)})(),children:[t.jsx("div",{className:"report-icon",children:"🎯"}),t.jsxs("div",{className:"report-info",children:[t.jsx("h3",{children:n("kpiReport","مؤشرات الأداء")}),t.jsx("p",{children:n("kpiReportDesc","KPIs ومقاييس الأداء")}),t.jsxs("div",{className:"report-stats",children:[t.jsx("span",{children:"KPI"}),t.jsx("small",{children:n("analysis","تحليل")})]})]}),t.jsx("div",{className:"report-action",children:"🖨️"})]})]})]})]}),Pn.reportData&&t.jsxs("div",{className:"advanced-report-display",children:[t.jsxs("h2",{children:["📊 ","daily"===Pn.reportData.type?n("dailyReport","التقرير اليومي"):n("monthlyReport","التقرير الشهري")]}),t.jsx("p",{className:"report-date",children:"daily"===Pn.reportData.type?`${n("date","تاريخ")}: ${new Date(Pn.reportData.date).toLocaleDateString("ar"===y?"ar-DZ":"fr"===y?"fr-FR":"en-US")}`:`${n("month","شهر")}: ${Pn.reportData.date}`}),t.jsxs("div",{className:"report-summary-cards",children:[t.jsxs("div",{className:"summary-card green",children:[t.jsx("h3",{children:n("totalSales","إجمالي المبيعات")}),t.jsx("div",{className:"summary-value",children:Ut(Pn.reportData.totalSales)})]}),t.jsxs("div",{className:"summary-card blue",children:[t.jsx("h3",{children:n("netProfit","صافي الربح")}),t.jsx("div",{className:"summary-value",children:Ut(Pn.reportData.netProfit)})]}),t.jsxs("div",{className:"summary-card orange",children:[t.jsx("h3",{children:n("profitMargin","هامش الربح")}),t.jsxs("div",{className:"summary-value",children:[Pn.reportData.profitMargin.toFixed(2),"%"]})]}),t.jsxs("div",{className:"summary-card purple",children:[t.jsx("h3",{children:n("invoiceCount","عدد الفواتير")}),t.jsx("div",{className:"summary-value",children:Pn.reportData.invoicesCount})]})]}),Pn.reportData.soldProducts.length>0&&t.jsxs("div",{className:"best-products-section",children:[t.jsxs("h3",{children:["🏆 ",n("topProducts","أفضل المنتجات مبيعاً")]}),t.jsx("div",{className:"products-table-container",children:t.jsxs("table",{className:"products-table",children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:n("product","المنتج")}),t.jsx("th",{children:n("category","الفئة")}),t.jsx("th",{children:n("quantitySold","الكمية المباعة")}),t.jsx("th",{children:n("totalRevenue","إجمالي الإيرادات")}),t.jsx("th",{children:n("profit","الربح")}),t.jsx("th",{children:n("profitMargin","هامش الربح")})]})}),t.jsx("tbody",{children:Pn.reportData.soldProducts.slice(0,10).map(((e,n)=>t.jsxs("tr",{className:0===n?"best-product":"",children:[t.jsxs("td",{children:[0===n&&t.jsx("span",{className:"crown",children:"👑"}),e.name]}),t.jsx("td",{children:e.category}),t.jsx("td",{children:e.totalQuantity}),t.jsx("td",{children:Ut(e.totalRevenue)}),t.jsx("td",{className:e.profit>0?"profit-positive":"profit-negative",children:Ut(e.profit)}),t.jsxs("td",{children:[e.totalRevenue>0?(e.profit/e.totalRevenue*100).toFixed(2):0,"%"]})]},e.id)))})]})})]})]}),t.jsxs("div",{className:"reports-summary",children:[t.jsxs("h2",{children:["📊 ",n("quickSummary","ملخص سريع")]}),t.jsxs("div",{className:"summary-stats",children:[t.jsxs("div",{className:"stat-item",children:[t.jsx("div",{className:"stat-icon",children:"💰"}),t.jsxs("div",{className:"stat-details",children:[t.jsx("h3",{children:n("totalSales","إجمالي المبيعات")}),t.jsx("p",{children:Ut(Ce.reduce(((e,t)=>e+t.finalTotal),0))})]})]}),t.jsxs("div",{className:"stat-item",children:[t.jsx("div",{className:"stat-icon",children:"📦"}),t.jsxs("div",{className:"stat-details",children:[t.jsx("h3",{children:n("totalPurchases","إجمالي المشتريات")}),t.jsx("p",{children:Ut(Ke.reduce(((e,t)=>e+t.finalTotal),0))})]})]}),t.jsxs("div",{className:"stat-item",children:[t.jsx("div",{className:"stat-icon",children:"💎"}),t.jsxs("div",{className:"stat-details",children:[t.jsx("h3",{children:n("netProfit","صافي الربح")}),t.jsx("p",{children:(()=>{const e=Ce.reduce(((e,t)=>e+t.finalTotal),0),t=m(Ce,Dn).totalCost,n=tt.reduce(((e,t)=>e+(parseFloat(t.amount)||0)),0);return Ut(e-t-n)})()})]})]}),t.jsxs("div",{className:"stat-item",children:[t.jsx("div",{className:"stat-icon",children:"📊"}),t.jsxs("div",{className:"stat-details",children:[t.jsx("h3",{children:n("inventoryValue","قيمة المخزون")}),t.jsx("p",{children:Ut(Dn.reduce(((e,t)=>e+t.stock*t.buyPrice),0))})]})]})]})]})]}),"reports"===Z&&"مدير"!==Ot.role&&"admin"!==Ot.role&&t.jsx("div",{className:"access-denied-page",children:t.jsxs("div",{className:"access-denied-content",children:[t.jsx("div",{className:"access-denied-icon",children:"🔒"}),t.jsx("h1",{children:n("accessRestricted","الوصول مقيد")}),t.jsx("p",{children:n("reportsManagerOnly","التقارير والإحصائيات متاحة للمدير فقط")}),t.jsx("p",{children:n("loginAsManager","يرجى تسجيل الدخول بحساب المدير للوصول إلى هذه الصفحة")}),t.jsx("button",{className:"btn btn-primary",onClick:()=>G("dashboard"),children:n("backToDashboard","العودة للوحة التحكم")})]})}),"settings"===Z&&t.jsxs("div",{className:`settings-page lang-${y}`,children:[t.jsxs("div",{className:"page-header "+("ar"!==y?"page-header-ltr-split":""),children:[t.jsx("div",{className:"page-title-section",children:t.jsx("h1",{children:n("settings","الإعدادات")})}),t.jsx("div",{className:"page-description-section",children:t.jsx("p",{children:n("storeAndSellersManagement","إدارة إعدادات المتجر والبائعين")})})]}),t.jsxs("div",{className:"settings-tabs",children:[t.jsxs("div",{className:"settings-section",children:[t.jsxs("div",{className:"section-header "+("ar"!==y?"section-header-ltr":""),children:[t.jsxs("h2",{children:["🏪 ",n("storeSettings","إعدادات المتجر")]}),t.jsxs("button",{className:"btn btn-primary",onClick:()=>{bt(!0)},children:["⚙️ ",n("editSettings","تعديل الإعدادات")]})]}),t.jsxs("div",{className:"settings-cards",children:[t.jsxs("div",{className:"setting-card",children:[t.jsx("div",{className:"setting-icon",children:"🏪"}),t.jsxs("div",{className:"setting-info",children:[t.jsx("h3",{children:n("storeName","اسم المتجر")}),t.jsx("p",{children:Pt.storeName})]})]}),t.jsxs("div",{className:"setting-card",children:[t.jsx("div",{className:"setting-icon",children:"📞"}),t.jsxs("div",{className:"setting-info",children:[t.jsx("h3",{children:n("phoneNumber","رقم الهاتف")}),t.jsx("p",{children:Pt.storePhone})]})]}),t.jsxs("div",{className:"setting-card",children:[t.jsx("div",{className:"setting-icon",children:"📍"}),t.jsxs("div",{className:"setting-info",children:[t.jsx("h3",{children:n("address","العنوان")}),t.jsx("p",{children:Pt.storeAddress})]})]}),t.jsxs("div",{className:"setting-card",children:[t.jsx("div",{className:"setting-icon",children:"💰"}),t.jsxs("div",{className:"setting-info",children:[t.jsx("h3",{children:n("taxRate","معدل الضريبة")}),t.jsxs("p",{children:[Pt.taxRate,"%"]})]})]})]})]}),t.jsxs("div",{className:"settings-section",children:[t.jsxs("div",{className:"section-header "+("ar"!==y?"section-header-ltr":""),children:[t.jsxs("h2",{children:["👥 ",n("sellersManagement","إدارة البائعين")]}),t.jsxs("button",{className:"btn btn-success",onClick:()=>{Bt({id:`S${String(Et.length+1).padStart(3,"0")}`,name:"",username:"",password:"",phone:"",email:"",role:"seller",isActive:!0}),Ft(!0)},children:["+ ",n("addNewSeller","إضافة بائع جديد")]})]}),Et.length>0&&t.jsxs("div",{className:"bulk-actions "+(Dt.length>0?"has-selection":""),children:[t.jsxs("div",{className:"select-all-container",children:[t.jsx("input",{type:"checkbox",className:"select-all-checkbox",checked:Et.length>0&&Dt.length===Et.length,onChange:()=>Wt("sellers",Et),id:"select-all-sellers"}),t.jsx("label",{htmlFor:"select-all-sellers",className:"select-all-label",children:n("selectAll","تحديد الكل")}),Dt.length>0&&t.jsxs("span",{className:"selected-count",children:["(",Dt.length," ",n("selected","محدد"),")"]})]}),Dt.length>0&&("مدير"===Ot.role||"admin"===Ot.role)&&t.jsxs("button",{className:"bulk-delete-btn",onClick:()=>Qt("sellers"),title:n("deleteSelected","حذف المحدد"),children:["🗑️ ",n("deleteSelected","حذف المحدد")," (",Dt.length,")"]})]}),t.jsx("div",{className:"sellers-table-container",children:t.jsxs("table",{className:"sellers-table "+("ar"!==y?"table-ltr":""),children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{className:"checkbox-column",children:t.jsx("input",{type:"checkbox",checked:Et.length>0&&Dt.length===Et.length,onChange:()=>Wt("sellers",Et),title:n("selectAll","تحديد الكل")})}),t.jsx("th",{children:n("sellerNumber","الرقم")}),t.jsx("th",{children:n("sellerName","الاسم")}),t.jsx("th",{children:n("username","اسم المستخدم")}),t.jsx("th",{children:n("phone","الهاتف")}),t.jsx("th",{children:n("role","الدور")}),t.jsx("th",{children:n("status","الحالة")}),t.jsx("th",{children:n("creationDate","تاريخ الإنشاء")}),t.jsx("th",{children:n("actions","الإجراءات")})]})}),t.jsx("tbody",{children:Et.map((e=>t.jsxs("tr",{className:Dt.includes(e.id)?"selected":"",children:[t.jsx("td",{className:"checkbox-column",children:t.jsx("input",{type:"checkbox",className:"row-checkbox",checked:Dt.includes(e.id),onChange:()=>Yt("sellers",e.id)})}),t.jsx("td",{children:e.id}),t.jsx("td",{children:e.name}),t.jsx("td",{children:e.username}),t.jsx("td",{children:e.phone}),t.jsx("td",{children:t.jsx("span",{className:`role-badge ${e.role}`,children:"admin"===e.role?n("admin","مدير"):n("seller","بائع")})}),t.jsx("td",{children:t.jsx("span",{className:"status-badge "+(e.isActive?"active":"inactive"),children:e.isActive?n("active","نشط"):n("inactive","غير نشط")})}),t.jsx("td",{children:new Date(e.createdAt).toLocaleDateString("ar"===y?"ar-DZ":"fr"===y?"fr-FR":"en-US")}),t.jsx("td",{children:t.jsxs("div",{className:"action-buttons",children:[t.jsx("button",{className:"btn-icon "+(e.isActive?"pause":"play"),onClick:()=>(e=>{const t=Et.map((t=>t.id===e?{...t,isActive:!t.isActive}:t));en(t),Zt(`✅ ${n("sellerStatusUpdated","تم تحديث حالة البائع")}`,"success",2e3)})(e.id),title:e.isActive?n("deactivate","إيقاف"):n("activate","تفعيل"),children:e.isActive?"⏸️":"▶️"}),"admin"!==e.role&&t.jsx("button",{className:"btn-icon delete",onClick:()=>(e=>{if(window.confirm(n("confirmDeleteSeller","هل أنت متأكد من حذف هذا البائع؟"))){const t=Et.filter((t=>t.id!==e));en(t),Zt(`🗑️ ${n("sellerDeletedSuccess","تم حذف البائع بنجاح")}`,"success",2e3)}})(e.id),title:n("delete","حذف"),children:"🗑️"})]})})]},e.id)))})]})})]})]})]})]}),K&&t.jsx("div",{className:"modal-overlay",onClick:Ha,children:t.jsxs("div",{className:`modal-content sales-modal-landscape lang-${y}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==y?"modal-header-ltr":""),children:[t.jsx("div",{className:"modal-title-section",children:t.jsxs("h2",{children:["🛒 ",n("newSalesInvoice","فاتورة مبيعات جديدة")]})}),t.jsx("button",{className:"modal-close",onClick:Ka,children:"×"})]}),t.jsxs("div",{className:"sales-invoice-landscape",children:[t.jsx("div",{className:"invoice-header-section",children:t.jsx("div",{className:"customer-info",children:t.jsxs("div",{className:"form-row",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("customer","الزبون")}),t.jsxs("select",{value:ra.customerId,onChange:e=>{const t=e.target.value;if("GUEST"===t){const e=ra.items.reduce(((e,t)=>e+t.total),0),t=e*(Pt.taxRate/100),n=e+t-0;oa({...ra,customerId:"GUEST",customerName:"زبون عابر",discount:0,total:e,tax:t,finalTotal:n})}else{const e=ba.find((e=>e.id===t));if(e){const a=ra.items.reduce(((e,t)=>e+t.total),0),s=ra.items.reduce(((e,t)=>{const n=Dn.find((e=>e.id===t.productId));if(n&&n.buyPrice&&n.sellPrice){return e+(n.sellPrice-n.buyPrice)*t.quantity}return e}),0)*(e.discountPercentage||0)/100,i=a*(Pt.taxRate/100),r=a+i-s;oa({...ra,customerId:t,customerName:e.name,discount:s,total:a,tax:i,finalTotal:r}),e.discountPercentage>0&&Zt(`💸 ${n("discountApplied","تم تطبيق خصم")} ${e.discountPercentage}% ${n("fromProfitMarginForCustomer","من هامش الربح للزبون")} ${e.name} (${Ut(s)})`,"success",4e3)}}},children:[t.jsx("option",{value:"GUEST",children:n("walkInCustomer","زبون عابر")}),t.jsx("option",{value:"",children:n("selectRegisteredCustomer","اختر زبون مسجل")}),ba.map((e=>t.jsxs("option",{value:e.id,children:[e.name," - ",e.phone]},e.id)))]})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("paymentMethod","طريقة الدفع")}),t.jsxs("select",{value:ra.paymentMethod,onChange:e=>oa({...ra,paymentMethod:e.target.value}),children:[t.jsx("option",{value:"نقداً",children:n("cash","نقداً")}),t.jsx("option",{value:"دين",children:n("credit","دين")})]})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("invoiceNumber","رقم الفاتورة")}),t.jsx("input",{type:"text",value:ra.invoiceNumber,readOnly:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("date","التاريخ")}),t.jsx("input",{type:"date",value:ra.date,onChange:e=>oa({...ra,date:e.target.value})})]})]})})}),t.jsxs("div",{className:"sales-barcode-scanner-row",children:[t.jsxs("div",{className:"scanner-input-row",children:[t.jsxs("h3",{className:"scanner-title",children:["📷 ",t.jsx("span",{className:"scanner-status-active",children:n("active","Actif")})," - ",n("scanBarcode","Scanner le code-barres")," 📷"]}),t.jsxs("div",{className:"barcode-input-container",children:[t.jsx("span",{className:"barcode-icon",children:"📷"}),t.jsx("input",{type:"text",placeholder:n("scanToAddProduct","امسح الباركود لإضافة منتج"),value:Ba,onChange:e=>{if(!K||Zn)return;const t=e.target.value,a=$(t);Ua(a),a.length>=3?(clearTimeout(window.salesScannerTimeout),window.salesScannerTimeout=setTimeout((()=>{Dn.find((e=>e.barcode===a&&""!==a.trim()))&&(rs(a),Ua(""))}),150)):a.length>0&&a.length<3&&(clearTimeout(window.salesScannerValidationTimeout),window.salesScannerValidationTimeout=setTimeout((()=>{Ba===a&&a.length>0&&a.length<3&&Zt(`❌ ${n("barcodeMinLength","الباركود قصير جداً - يجب أن يكون 3 أحرف على الأقل")}`,"warning",2e3)}),1e3))},onKeyDown:e=>{if(K&&!Zn&&"Enter"===e.key&&"keydown"===e.type){e.preventDefault(),e.stopPropagation();const t=e.target.value.trim();t.length>=3?(Ua(""),clearTimeout(window.salesScannerValidationTimeout)):t.length>0&&Zt(`❌ ${n("barcodeMinLength","الباركود قصير جداً - يجب أن يكون 3 أحرف على الأقل")}`,"warning",2e3)}},onFocus:()=>{window.barcodeShortcutManager&&(window.barcodeShortcutManager.isBarcodeActive=!0,window.barcodeShortcutManager.setShortcutsEnabled(!1))},onBlur:()=>{setTimeout((()=>{window.barcodeShortcutManager&&!window.barcodeShortcutManager.checkBarcodeInput(document.activeElement)&&(window.barcodeShortcutManager.isBarcodeActive=!1,window.barcodeShortcutManager.setShortcutsEnabled(!0))}),100)},className:"barcode-input",ref:Za,autoFocus:!0}),t.jsxs("button",{type:"button",className:"btn btn-warning btn-sm",onClick:()=>{Ua("")},title:n("clearBarcode","مسح الباركود"),children:["🗑️ ",n("clear","مسح")]})]})]}),t.jsx("div",{className:"lcd-display-row",children:t.jsx("div",{className:"lcd-screen-big",children:t.jsxs("div",{className:"total-final-display",children:[t.jsxs("div",{className:"total-final-label",children:[n("finalTotal","المجموع النهائي"),":"]}),t.jsx("div",{className:"total-final-amount",children:Ut(ra.finalTotal)}),t.jsx("div",{className:"total-final-currency",children:"DZD"})]})})})]}),t.jsx("div",{className:"product-selection-section",children:t.jsx("div",{className:"manual-selection",children:t.jsxs("div",{className:"form-row",children:[t.jsxs("div",{className:"form-group flex-2",children:[t.jsx("label",{children:n("product","المنتج")}),t.jsxs("div",{className:"product-selection-container",children:[t.jsx("input",{type:"text",placeholder:n("searchProductPlaceholder","ابحث عن منتج (الاسم، الرمز، الباركود)..."),className:"search-input product-search",value:ua,onChange:e=>{const t=e.target.value;if(xa(t),t.length>0){const e=Dn.filter((e=>{const n=t.toLowerCase();return e.name.toLowerCase().includes(n)||e.id.toLowerCase().includes(n)||e.barcode&&e.barcode.toLowerCase().includes(n)}));1===e.length?(ca(e[0].id),pa(e[0].sellPrice||e[0].price),ma(1)):0===e.length&&ca("")}else ca("")}}),t.jsxs("select",{value:la,onChange:e=>{ca(e.target.value);const t=Dn.find((t=>t.id===e.target.value));t&&(pa(t.sellPrice||t.price),ma(1))},children:[t.jsx("option",{value:"",children:n("selectProduct","اختر منتج")}),ts.map((e=>t.jsxs("option",{value:e.id,children:[e.name," - ",Ut(e.sellPrice||e.price)," - ",n("available","متوفر"),": ",e.stock]},e.id)))]})]})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("quantity","الكمية")}),t.jsx("input",{type:"number",value:da,onChange:e=>ma(parseInt(e.target.value)||1),min:"1"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("price","السعر")}),t.jsx("input",{type:"number",value:ha,onChange:e=>pa(parseFloat(e.target.value)||0),step:"0.01"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:" "}),t.jsxs("button",{type:"button",className:"btn btn-success btn-add",onClick:es,disabled:!la,children:["➕ ",n("add","إضافة")]})]})]})})}),t.jsxs("div",{className:"invoice-content-section",children:[t.jsxs("div",{className:"items-section",children:[t.jsxs("h3",{children:["🛍️ ",n("invoiceItems","عناصر الفاتورة")]}),0===ra.items.length?t.jsxs("div",{className:"no-items",children:[t.jsx("p",{children:n("noProductsAdded","لم يتم إضافة أي منتجات بعد")}),t.jsx("p",{children:n("selectProductsFromList","اختر المنتجات من القائمة أعلاه")})]}):t.jsx("div",{className:"items-table",children:t.jsxs("table",{className:""+("ar"!==y?"table-ltr":""),children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:n("productName","المنتج")}),t.jsx("th",{children:n("quantity","الكمية")}),t.jsx("th",{children:n("price","السعر")}),t.jsx("th",{children:n("total","المجموع")}),t.jsx("th",{children:n("action","إجراء")})]})}),t.jsx("tbody",{children:ra.items.map(((e,a)=>t.jsxs("tr",{children:[t.jsx("td",{children:e.productName}),t.jsx("td",{children:t.jsx("input",{type:"number",value:e.quantity,onChange:t=>{const s=parseInt(t.target.value)||1,i=Dn.find((t=>t.id===e.productId));if(i&&s<=i.stock){const t=[...ra.items];t[a].quantity=s,t[a].total=s*e.price;const n=t.reduce(((e,t)=>e+t.total),0),i=n*(Pt.taxRate/100),r=n+i-ra.discount;oa({...ra,items:t,total:n,tax:i,finalTotal:r})}else Zt(`❌ ${n("quantityRequiredExceedsStock","الكمية المطلوبة أكبر من المتوفر")} (${i?.stock||0})`,"error",3e3)},min:"1",className:"quantity-input"})}),t.jsx("td",{children:Ut(e.price)}),t.jsx("td",{children:Ut(e.total)}),t.jsx("td",{children:t.jsx("button",{className:"btn-delete",onClick:()=>(e=>{const t=ra.items.find((t=>t.productId===e||t.productId===String(e)||String(t.productId)===String(e))),a=ra.items.filter((t=>t.productId!==e&&t.productId!==String(e)&&String(t.productId)!==String(e))),s=a.reduce(((e,t)=>e+t.total),0),i=s*(Pt.taxRate/100),r=s+i-ra.discount;oa({...ra,items:a,total:s,tax:i,finalTotal:r}),j.play("deleteProduct",{showNotification:!1}),t&&Zt(`🗑️ ${n("productRemovedFromInvoice","تم حذف")} ${t.productName} ${n("fromInvoice","من الفاتورة")}`,"info",2e3)})(e.productId),title:n("deleteTitle","حذف"),children:"🗑️"})})]},a)))})]})})]}),t.jsx("div",{className:"totals-section",children:t.jsxs("div",{className:"totals-grid",children:[t.jsxs("div",{className:"discount-input",children:[t.jsxs("label",{children:["💸 ",n("discount","الخصم")]}),t.jsx("input",{type:"number",value:ra.discount,onChange:e=>{const t=parseFloat(e.target.value)||0,n=ra.items.reduce(((e,t)=>e+t.total),0),a=n*(Pt.taxRate/100),s=n+a-t;oa({...ra,discount:t,total:n,tax:a,finalTotal:s})},step:"0.01"})]}),t.jsxs("div",{className:"totals-summary",children:[t.jsxs("div",{className:"total-row",children:[t.jsxs("span",{children:[n("subtotal","المجموع الفرعي"),":"]}),t.jsx("span",{children:Ut(ra.total)})]}),t.jsxs("div",{className:"total-row",children:[t.jsxs("span",{children:[n("tax","الضريبة")," (",Pt.taxRate,"%):"]}),t.jsx("span",{children:Ut(ra.tax)})]}),t.jsxs("div",{className:"total-row",children:[t.jsxs("span",{children:[n("discount","الخصم"),":"]}),t.jsx("span",{children:Ut(ra.discount)})]}),t.jsxs("div",{className:"total-row final",children:[t.jsxs("span",{children:[n("finalTotal","المجموع النهائي"),":"]}),t.jsx("span",{children:Ut(ra.finalTotal)})]})]}),t.jsxs("div",{className:"action-buttons "+("ar"!==y?"action-buttons-ltr":""),children:[t.jsxs("button",{className:"btn btn-primary",onClick:as,children:["💾 ",n("saveInvoice","حفظ الفاتورة")]}),t.jsxs("button",{className:"btn btn-secondary",onClick:Ha,children:["❌ ",n("cancel","إلغاء")]})]})]})})]})]})]})}),ee&&t.jsx("div",{className:"modal-overlay",onClick:fn,children:t.jsxs("div",{className:`modal-content purchase-modal-landscape lang-${y}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==y?"modal-header-ltr":""),children:[t.jsx("div",{className:"modal-title-section",children:t.jsxs("h2",{children:["📦 ",Je?n("editPurchaseInvoiceTitle","تعديل فاتورة المشتريات"):n("newPurchaseInvoice","فاتورة مشتريات جديدة")]})}),t.jsx("button",{className:"modal-close",onClick:fn,children:"×"})]}),t.jsxs("div",{className:"modal-body purchase-modal-body",children:[t.jsx("div",{className:"invoice-header-section",children:t.jsx("div",{className:"invoice-info-grid",children:t.jsxs("div",{className:"form-row",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("supplier","المورد")}),t.jsxs("select",{value:We.supplierId,onChange:e=>{const t=e.target.value;if("NEW_SUPPLIER"===t)rn();else if("GENERAL"===t)Ye({...We,supplierId:"GENERAL",supplierName:n("generalSupplier","مورد عام")});else{const e=Qe.find((e=>e.id===t));e&&Ye({...We,supplierId:t,supplierName:e.name})}},children:[t.jsx("option",{value:"GENERAL",children:n("generalSupplier","مورد عام")}),t.jsx("option",{value:"",children:n("selectRegisteredSupplier","اختر مورد مسجل")}),Qe.map((e=>t.jsxs("option",{value:e.id,children:[Vt(e.name)," - ",e.phone]},e.id))),t.jsx("option",{value:"NEW_SUPPLIER",children:n("addNewSupplier","+ إضافة مورد جديد")})]})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("paymentMethod","طريقة الدفع")}),t.jsxs("select",{value:We.paymentMethod,onChange:e=>Ye({...We,paymentMethod:e.target.value}),children:[t.jsx("option",{value:"نقداً",children:n("cash","نقداً")}),t.jsx("option",{value:"دين",children:n("credit","دين")})]})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("invoiceNumber","رقم الفاتورة")}),t.jsx("input",{type:"text",value:We.invoiceNumber,readOnly:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("date","التاريخ")}),t.jsx("input",{type:"date",value:We.date,onChange:e=>Ye({...We,date:e.target.value})})]})]})})}),t.jsx("div",{className:"product-selection-section",children:t.jsx("div",{className:"manual-selection",children:t.jsxs("div",{className:"form-row",children:[t.jsxs("div",{className:"form-group flex-2",children:[t.jsx("label",{children:n("product","المنتج")}),t.jsxs("select",{value:la,onChange:e=>{if("NEW_PRODUCT"===e.target.value)ms(),Zt("📦 "+n("addNewProductOpened","تم فتح نافذة إضافة منتج جديد"),"success",2e3);else{ca(e.target.value);const t=Dn.find((t=>t.id===e.target.value));t&&(pa(t.buyPrice||0),ma(1))}},children:[t.jsx("option",{value:"",children:n("selectProduct","اختر منتج")}),Dn.map((e=>t.jsxs("option",{value:e.id,children:[e.name," - ",Ut(e.buyPrice||0)," - ",n("available","متوفر"),": ",e.stock]},e.id))),t.jsxs("option",{value:"NEW_PRODUCT",children:["📦 ",n("addNewProduct","إضافة منتج جديد")]})]})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("quantity","الكمية")}),t.jsx("input",{type:"number",value:da,onChange:e=>ma(parseInt(e.target.value)||1),min:"1"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("purchasePrice","سعر الشراء")}),t.jsx("input",{type:"number",value:ha,onChange:e=>pa(parseFloat(e.target.value)||0),step:"0.01"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:" "}),t.jsxs("button",{type:"button",className:"btn btn-success btn-add",onClick:jn,disabled:!la,children:["➕ ",n("add","إضافة")]})]})]})})}),t.jsxs("div",{className:"invoice-content-section",children:[t.jsxs("div",{className:"items-section",children:[t.jsxs("h3",{children:["📦 ",n("purchaseInvoiceItems","عناصر فاتورة المشتريات")]}),0===We.items.length?t.jsxs("div",{className:"no-items",children:[t.jsx("p",{children:n("noPurchaseProductsAdded","لم يتم إضافة أي منتجات بعد")}),t.jsx("p",{children:n("selectProductsFromList","اختر المنتجات من القائمة أعلاه")})]}):t.jsx("div",{className:"items-table",children:t.jsxs("table",{children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:n("productName","المنتج")}),t.jsx("th",{children:n("quantity","الكمية")}),t.jsx("th",{children:n("purchasePrice","سعر الشراء")}),t.jsx("th",{children:n("total","المجموع")}),t.jsx("th",{children:n("action","إجراء")})]})}),t.jsx("tbody",{children:We.items.map(((e,a)=>t.jsxs("tr",{children:[t.jsx("td",{children:e.productName}),t.jsx("td",{children:t.jsx("input",{type:"number",value:e.quantity,onChange:t=>{const n=parseInt(t.target.value)||1,s=[...We.items];s[a].quantity=n,s[a].total=n*e.price;const i=s.reduce(((e,t)=>e+t.total),0),r=i*(Pt.taxRate/100),o=i+r-We.discount;Ye({...We,items:s,total:i,tax:r,finalTotal:o})},min:"1",className:"quantity-input"})}),t.jsx("td",{children:Ut(e.price)}),t.jsx("td",{children:Ut(e.total)}),t.jsx("td",{children:t.jsx("button",{className:"btn-delete",onClick:()=>{const e=We.items.filter(((e,t)=>t!==a)),t=e.reduce(((e,t)=>e+t.total),0),n=t*(Pt.taxRate/100),s=t+n-We.discount;Ye({...We,items:e,total:t,tax:n,finalTotal:s})},title:n("delete","حذف"),children:"🗑️"})})]},a)))})]})})]}),t.jsx("div",{className:"totals-section",children:t.jsxs("div",{className:"totals-grid",children:[t.jsxs("div",{className:"discount-input",children:[t.jsxs("label",{children:["💸 ",n("discount","الخصم")]}),t.jsx("input",{type:"number",value:We.discount,onChange:e=>{const t=parseFloat(e.target.value)||0,n=We.items.reduce(((e,t)=>e+t.total),0),a=n*(Pt.taxRate/100),s=n+a-t;Ye({...We,discount:t,total:n,tax:a,finalTotal:s})},step:"0.01"})]}),t.jsxs("div",{className:"totals-summary",children:[t.jsxs("div",{className:"total-row",children:[t.jsxs("span",{children:[n("subtotal","المجموع الفرعي"),":"]}),t.jsx("span",{children:Ut(We.total)})]}),t.jsxs("div",{className:"total-row",children:[t.jsxs("span",{children:[n("tax","الضريبة")," (",Pt.taxRate,"%):"]}),t.jsx("span",{children:Ut(We.tax)})]}),t.jsxs("div",{className:"total-row",children:[t.jsxs("span",{children:[n("discount","الخصم"),":"]}),t.jsx("span",{children:Ut(We.discount)})]}),t.jsxs("div",{className:"total-row final",children:[t.jsxs("span",{children:[n("finalTotal","المجموع النهائي"),":"]}),t.jsx("span",{children:Ut(We.finalTotal)})]})]}),t.jsxs("div",{className:"action-buttons",children:[t.jsxs("button",{className:"btn btn-primary",onClick:yn,children:["💾 ",Je?n("updatePurchaseInvoice","تحديث فاتورة المشتريات"):n("savePurchaseInvoice","حفظ فاتورة المشتريات")]}),t.jsxs("button",{className:"btn btn-secondary",onClick:fn,children:["❌ ",n("cancel","إلغاء")]})]})]})})]})]})]})}),Q&&t.jsx("div",{className:"modal-overlay",onClick:()=>H(!1),children:t.jsxs("div",{className:`modal-content customer-modal lang-${y}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==y?"modal-header-ltr":""),children:[t.jsx("h2",{children:"ar"===y?"إضافة زبون جديد":"fr"===y?"Ajouter un nouveau client":"Add New Customer"}),t.jsx("button",{className:"modal-close",onClick:()=>H(!1),children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"form-grid",children:[t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:["ar"===y?"اسم الزبون":"fr"===y?"Nom du client":"Customer Name"," *"]}),t.jsx("input",{type:"text",value:ya.name,onChange:e=>Na({...ya,name:e.target.value}),placeholder:"ar"===y?"أدخل اسم الزبون":"fr"===y?"Entrez le nom du client":"Enter customer name",required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:"ar"===y?"رقم الهاتف":"fr"===y?"Numéro de téléphone":"Phone Number"}),t.jsx("input",{type:"tel",value:ya.phone,onChange:e=>Na({...ya,phone:e.target.value}),placeholder:"ar"===y?"رقم الهاتف":"fr"===y?"Numéro de téléphone":"Phone Number"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:"ar"===y?"البريد الإلكتروني":"fr"===y?"Adresse e-mail":"Email Address"}),t.jsx("input",{type:"email",value:ya.email,onChange:e=>Na({...ya,email:e.target.value}),placeholder:"ar"===y?"البريد الإلكتروني":"fr"===y?"Adresse e-mail":"Email Address"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:"ar"===y?"العنوان":"fr"===y?"Adresse":"Address"}),t.jsx("input",{type:"text",value:ya.address,onChange:e=>Na({...ya,address:e.target.value}),placeholder:"ar"===y?"العنوان الكامل":"fr"===y?"Adresse complète":"Full Address"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:"ar"===y?"الشركة":"fr"===y?"Entreprise":"Company"}),t.jsx("input",{type:"text",value:ya.company,onChange:e=>Na({...ya,company:e.target.value}),placeholder:"ar"===y?"اسم الشركة (اختياري)":"fr"===y?"Nom de l'entreprise (optionnel)":"Company Name (optional)"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:"ar"===y?"الرصيد الافتتاحي":"fr"===y?"Solde d'ouverture":"Opening Balance"}),t.jsx("input",{type:"number",value:ya.balance,onChange:e=>Na({...ya,balance:parseFloat(e.target.value)||0}),step:"0.01"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:"ar"===y?"حد الائتمان":"fr"===y?"Limite de crédit":"Credit Limit"}),t.jsx("input",{type:"number",value:ya.creditLimit,onChange:e=>Na({...ya,creditLimit:parseFloat(e.target.value)||0}),step:"0.01"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:"ar"===y?"فترة السداد (بالأيام)":"fr"===y?"Délai de paiement (en jours)":"Payment Term (in days)"}),t.jsx("input",{type:"number",value:ya.paymentTerm,onChange:e=>Na({...ya,paymentTerm:parseInt(e.target.value)||30}),min:"1"})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:["💸 ","ar"===y?"خصم هامش الربح (%)":"fr"===y?"Remise marge bénéficiaire (%)":"Profit Margin Discount (%)"]}),t.jsx("input",{type:"number",value:ya.discountPercentage,onChange:e=>Na({...ya,discountPercentage:parseFloat(e.target.value)||0}),placeholder:"ar"===y?"نسبة الخصم من هامش الربح":"fr"===y?"Pourcentage de remise sur marge":"Profit margin discount percentage",min:"0",max:"100",step:"0.1"}),t.jsx("small",{style:{color:"#666",fontSize:"12px"},children:"ar"===y?"يتم تطبيق الخصم على هامش الربح وليس إجمالي المبيعات":"fr"===y?"La remise est appliquée à la marge bénéficiaire et non au total des ventes":"Discount is applied to profit margin, not total sales"})]})]})}),t.jsxs("div",{className:"modal-footer "+("ar"!==y?"modal-footer-ltr":""),children:[t.jsx("button",{className:"btn btn-primary",onClick:()=>{if(!ya.name.trim())return void Zt(n("pleaseEnterCustomerName","يرجى إدخال اسم الزبون"),"error");const e=ya.name.trim();if(ba.find((t=>t.name.toLowerCase().trim()===e.toLowerCase()&&t.id!==ya.id)))return void Zt(`❌ ${n("customerNameAlreadyExists","اسم الزبون موجود بالفعل")}: "${e}" - ${n("pleaseChooseDifferentName","يرجى اختيار اسم مختلف")}`,"error",4e3);if(ya.phone&&""!==ya.phone.trim()){const e=ba.find((e=>e.phone===ya.phone.trim()&&e.id!==ya.id));if(e)return void Zt(`❌ ${n("phoneNumberAlreadyExists","رقم الهاتف موجود بالفعل")}: "${ya.phone}" - ${n("usedByCustomer","مستخدم بواسطة")}: "${e.name}"`,"error",4e3)}const t=`C${String(ba.length+1).padStart(3,"0")}`,a={...ya,id:t,createdAt:(new Date).toISOString()},s=[...ba,a];ja(s),Na({id:"",name:"",email:"",phone:"",address:"",company:"",balance:0,creditLimit:0,paymentTerm:30,discountPercentage:0,status:"نشط"}),H(!1),Zt(n("customerAddedSuccessfully","تم إضافة الزبون بنجاح"),"success")},children:"ar"===y?"حفظ الزبون":"fr"===y?"Enregistrer le client":"Save Customer"}),t.jsx("button",{className:"btn btn-secondary",onClick:()=>H(!1),children:"ar"===y?"إلغاء":"fr"===y?"Annuler":"Cancel"})]})]})}),$a&&Sa&&t.jsx("div",{className:"modal-overlay",onClick:za,children:t.jsxs("div",{className:`modal-content customer-modal lang-${y}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==y?"modal-header-ltr":""),children:[t.jsx("h2",{children:n("editCustomerData","تعديل بيانات الزبون")}),t.jsx("button",{className:"modal-close",onClick:za,children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"form-grid",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("customerID","رقم الزبون")}),t.jsx("input",{type:"text",value:Sa.id,readOnly:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("customerNameRequired","اسم الزبون")," *"]}),t.jsx("input",{type:"text",value:Sa.name,onChange:e=>ka({...Sa,name:e.target.value}),placeholder:n("enterCustomerName","أدخل اسم الزبون"),required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("phoneNumber","رقم الهاتف")}),t.jsx("input",{type:"tel",value:Sa.phone,onChange:e=>ka({...Sa,phone:e.target.value}),placeholder:n("phoneNumberPlaceholder","رقم الهاتف")})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("emailAddress","البريد الإلكتروني")}),t.jsx("input",{type:"email",value:Sa.email,onChange:e=>ka({...Sa,email:e.target.value}),placeholder:n("emailPlaceholder","البريد الإلكتروني")})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("address","العنوان")}),t.jsx("input",{type:"text",value:Sa.address,onChange:e=>ka({...Sa,address:e.target.value}),placeholder:n("addressPlaceholder","العنوان")})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("companyName","الشركة")}),t.jsx("input",{type:"text",value:Sa.company,onChange:e=>ka({...Sa,company:e.target.value}),placeholder:n("companyPlaceholder","اسم الشركة")})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("openingBalance","الرصيد الافتتاحي")}),t.jsx("input",{type:"number",value:Sa.balance,onChange:e=>ka({...Sa,balance:parseFloat(e.target.value)||0}),step:"0.01"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("creditLimit","حد الائتمان")}),t.jsx("input",{type:"number",value:Sa.creditLimit,onChange:e=>ka({...Sa,creditLimit:parseFloat(e.target.value)||0}),step:"0.01"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("paymentTermDays","فترة السداد (بالأيام)")}),t.jsx("input",{type:"number",value:Sa.paymentTerm,onChange:e=>ka({...Sa,paymentTerm:parseInt(e.target.value)||30}),min:"1"})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:["💸 ",n("profitMarginDiscount","خصم هامش الربح (%)")]}),t.jsx("input",{type:"number",value:Sa.discountPercentage,onChange:e=>ka({...Sa,discountPercentage:parseFloat(e.target.value)||0}),placeholder:n("discountPercentagePlaceholder","نسبة الخصم من هامش الربح"),min:"0",max:"100",step:"0.1"}),t.jsx("small",{style:{color:"#666",fontSize:"12px"},children:n("discountAppliedToProfit","يتم تطبيق الخصم على هامش الربح وليس إجمالي المبيعات")})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("status","الحالة")}),t.jsxs("select",{value:Sa.status,onChange:e=>ka({...Sa,status:e.target.value}),children:[t.jsx("option",{value:"نشط",children:n("active","نشط")}),t.jsx("option",{value:"غير نشط",children:n("inactive","غير نشط")})]})]})]})}),t.jsxs("div",{className:"modal-footer "+("ar"!==y?"modal-footer-ltr":""),children:[t.jsx("button",{className:"btn btn-primary",onClick:()=>{if(!Sa.name.trim())return void Zt(n("pleaseEnterCustomerName","يرجى إدخال اسم الزبون"),"error");const e=Sa.name.trim();if(ba.find((t=>t.name.toLowerCase().trim()===e.toLowerCase()&&t.id!==Sa.id)))return void Zt(`❌ ${n("customerNameAlreadyExists","اسم الزبون موجود بالفعل")}: "${e}" - ${n("pleaseChooseDifferentName","يرجى اختيار اسم مختلف")}`,"error",4e3);if(Sa.phone&&""!==Sa.phone.trim()){const e=ba.find((e=>e.phone===Sa.phone.trim()&&e.id!==Sa.id));if(e)return void Zt(`❌ ${n("phoneNumberAlreadyExists","رقم الهاتف موجود بالفعل")}: "${Sa.phone}" - ${n("usedByCustomer","مستخدم بواسطة")}: "${e.name}"`,"error",4e3)}const t=ba.map((e=>e.id===Sa.id?Sa:e));ja(t),za(),Zt(n("customerDataUpdatedSuccessfully","تم تحديث بيانات الزبون بنجاح"),"success")},children:n("saveChanges","حفظ التغييرات")}),t.jsx("button",{className:"btn btn-secondary",onClick:za,children:n("cancel","إلغاء")})]})]})}),ne&&se&&t.jsx("div",{className:"modal-overlay",onClick:()=>ae(!1),children:t.jsxs("div",{className:`modal-content large-modal lang-${y}`,onClick:e=>e.stopPropagation(),dir:"ar"===y?"rtl":"ltr",children:[t.jsxs("div",{className:"modal-header "+("ar"!==y?"modal-header-ltr":""),children:[t.jsxs("h2",{children:["📋 ",n("customerOperations","عمليات الزبون")," - ",se.customer.name]}),t.jsx("button",{className:"modal-close",onClick:()=>ae(!1),children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"customer-operations",children:[t.jsx("div",{className:"customer-info-summary",children:t.jsxs("div",{className:"info-card",children:[t.jsxs("h3",{children:["👤 ",n("customerInfo","معلومات الزبون")]}),t.jsxs("div",{className:"info-grid",children:[t.jsxs("div",{className:"info-item",children:[t.jsxs("strong",{children:[n("customerNumber","رقم الزبون"),":"]})," ",se.customer.id]}),t.jsxs("div",{className:"info-item",children:[t.jsxs("strong",{children:[n("customerName","اسم الزبون"),":"]})," ",se.customer.name]}),t.jsxs("div",{className:"info-item",children:[t.jsxs("strong",{children:[n("phone","الهاتف"),":"]})," ",se.customer.phone||"-"]}),t.jsxs("div",{className:"info-item",children:[t.jsxs("strong",{children:[n("email","البريد الإلكتروني"),":"]})," ",se.customer.email||"-"]}),t.jsxs("div",{className:"info-item",children:[t.jsxs("strong",{children:[n("balance","الرصيد"),":"]}),t.jsx("span",{className:se.customer.balance<=0?"text-success":"text-danger",children:Ut(se.customer.balance||0)})]}),t.jsxs("div",{className:"info-item",children:[t.jsxs("strong",{children:[n("profitMarginDiscount","خصم هامش الربح"),":"]})," ",se.customer.discountPercentage||0,"%"]})]})]})}),t.jsxs("div",{className:"operations-section",children:[t.jsxs("h3",{children:["📊 ",n("customerOperations","عمليات الزبون")]}),t.jsx("div",{className:"table-container",children:t.jsxs("table",{className:"data-table",children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:n("operationType","نوع العملية")}),t.jsx("th",{children:n("invoiceNumber","رقم الفاتورة")}),t.jsx("th",{children:n("date","التاريخ")}),t.jsx("th",{children:n("paymentMethod","طريقة الدفع")}),t.jsx("th",{children:n("amount","المبلغ")}),t.jsx("th",{children:n("status","الحالة")})]})}),t.jsx("tbody",{children:se.allOperations&&0!==se.allOperations.length?se.allOperations.map(((e,a)=>t.jsxs("tr",{children:[t.jsx("td",{children:t.jsx("span",{className:`operation-type ${e.type}`,children:"invoice"===e.type?`🧾 ${n("sale","مبيعة")}`:`💰 ${n("payment","دفعة")}`})}),t.jsx("td",{children:e.invoiceNumber}),t.jsx("td",{children:new Date(e.date).toLocaleDateString("ar"===y?"ar-DZ":"fr"===y?"fr-FR":"en-US")}),t.jsx("td",{children:t.jsx("span",{className:"payment-method "+("نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"cash":"credit"),children:"نقداً"===e.paymentMethod?n("cash","نقداً"):"دين"===e.paymentMethod?n("credit","دين"):e.paymentMethod||n("cash","نقداً")})}),t.jsx("td",{children:t.jsx("span",{className:"payment"===e.type?"payment-amount":"",children:"payment"===e.type?`-${Ut(Math.abs(e.finalTotal))}`:Ut(e.finalTotal)})}),t.jsx("td",{children:t.jsx("span",{className:"status "+("payment"===e.type?"payment":"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"paid":"pending"),children:"payment"===e.type?n("paymentReceived","دفعة مستلمة"):"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?n("paid","مدفوعة"):n("debt","دين")})})]},`${e.type}-${e.id||e.invoiceNumber}-${a}`))):t.jsx("tr",{children:t.jsx("td",{colSpan:"6",style:{textAlign:"center",padding:"20px"},children:n("noOperationsFound","لا توجد عمليات لهذا الزبون")})})})]})})]}),t.jsx("div",{className:"operations-summary",children:t.jsxs("div",{className:"summary-cards",children:[t.jsxs("div",{className:"summary-card green",children:[t.jsx("h4",{children:n("totalOperations","إجمالي العمليات")}),t.jsx("div",{className:"summary-value",children:se.totalOperations})]}),t.jsxs("div",{className:"summary-card blue",children:[t.jsx("h4",{children:n("totalAmount","إجمالي المبلغ")}),t.jsx("div",{className:"summary-value",children:Ut(se.totalAmount)})]}),t.jsxs("div",{className:"summary-card orange",children:[t.jsx("h4",{children:n("cashOperations","العمليات النقدية")}),t.jsxs("div",{className:"summary-value",children:[se.cashOperations.length," (",Ut(se.totalCash),")"]})]}),t.jsxs("div",{className:"summary-card red",children:[t.jsx("h4",{children:n("creditOperations","عمليات الدين")}),t.jsxs("div",{className:"summary-value",children:[se.creditOperations.length," (",Ut(se.totalCredit),")"]})]})]})})]})}),t.jsxs("div",{className:"modal-footer "+("ar"!==y?"modal-footer-ltr":""),children:[se.customer.balance>0&&t.jsxs("button",{className:"btn btn-success",onClick:()=>{ae(!1),Fa(se.customer)},children:["💰 ",n("payCustomerDebt","تسديد فواتير الزبون")]}),t.jsxs("button",{className:"btn btn-info",onClick:()=>(e=>{const t="ar"===y,a="ar"===y?"ar":"fr"===y?"fr":"en",s=`\n      <!DOCTYPE html>\n      <html dir="${t?"rtl":"ltr"}" lang="${a}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("customerTransactionsReport","تقرير معاملات الزبون")} - ${e.customer.name}</title>\n        <style>\n          body {\n            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n            direction: ${t?"rtl":"ltr"};\n            margin: 20px;\n            color: #333;\n          }\n          .header {\n            text-align: center;\n            margin-bottom: 30px;\n            border-bottom: 2px solid #3498db;\n            padding-bottom: 20px;\n          }\n          .header h1 {\n            color: #2c3e50;\n            margin: 0;\n            font-size: 28px;\n          }\n          .header p {\n            color: #7f8c8d;\n            margin: 5px 0;\n          }\n          .customer-info {\n            background: #f8f9fa;\n            padding: 20px;\n            border-radius: 10px;\n            margin-bottom: 20px;\n            border: 1px solid #dee2e6;\n          }\n          .customer-info h2 {\n            color: #2c3e50;\n            margin: 0 0 15px 0;\n          }\n          .info-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n            gap: 15px;\n          }\n          .info-item {\n            background: white;\n            padding: 15px;\n            border-radius: 8px;\n            border-left: 4px solid #3498db;\n          }\n          .info-item h4 {\n            margin: 0 0 8px 0;\n            color: #34495e;\n            font-size: 14px;\n          }\n          .info-item .value {\n            font-size: 18px;\n            font-weight: bold;\n            color: #2c3e50;\n          }\n          .transactions-table {\n            width: 100%;\n            border-collapse: collapse;\n            margin-top: 20px;\n          }\n          .transactions-table th, .transactions-table td {\n            border: 1px solid #ddd;\n            padding: 12px;\n            text-align: ${t?"right":"left"};\n          }\n          .transactions-table th {\n            background: #3498db;\n            color: white;\n            font-weight: bold;\n          }\n          .transactions-table tr:nth-child(even) {\n            background: #f9f9f9;\n          }\n          .cash {\n            color: #27ae60;\n            font-weight: bold;\n          }\n          .credit {\n            color: #e74c3c;\n            font-weight: bold;\n          }\n          .payment-row {\n            background-color: #f0f8ff !important;\n            border-left: 3px solid #28a745;\n          }\n          .sale-row {\n            background-color: #fff8f0 !important;\n            border-left: 3px solid #007bff;\n          }\n          .payment-amount {\n            color: #28a745;\n            font-weight: bold;\n          }\n          .sale-amount {\n            color: #007bff;\n            font-weight: bold;\n          }\n          .summary {\n            background: #ecf0f1;\n            padding: 20px;\n            border-radius: 10px;\n            margin-top: 20px;\n          }\n          .summary h3 {\n            color: #2c3e50;\n            margin: 0 0 15px 0;\n          }\n          .summary-grid {\n            display: grid;\n            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n            gap: 15px;\n          }\n          .summary-item {\n            text-align: center;\n          }\n          .summary-item .label {\n            font-size: 14px;\n            color: #7f8c8d;\n          }\n          .summary-item .value {\n            font-size: 20px;\n            font-weight: bold;\n            color: #2c3e50;\n          }\n          .footer {\n            text-align: center;\n            margin-top: 30px;\n            color: #7f8c8d;\n            border-top: 1px solid #ddd;\n            padding-top: 20px;\n          }\n          @media print {\n            body { margin: 0; }\n          }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>📋 ${n("customerTransactionsReport","تقرير معاملات الزبون")}</h1>\n          <p>${n("reportDate","تاريخ التقرير")}: ${(new Date).toLocaleDateString("ar"===a?"ar-DZ":"fr"===a?"fr-FR":"en-US")}</p>\n          <p>${Pt.storeName} - ${n("transactionsAnalysis","تحليل المعاملات")}</p>\n        </div>\n\n        <div class="customer-info">\n          <h2>👤 ${n("customerInformation","معلومات العميل")}</h2>\n          <div class="info-grid">\n            <div class="info-item">\n              <h4>${n("customerName","اسم العميل")}</h4>\n              <div class="value">${e.customer.name}</div>\n            </div>\n            <div class="info-item">\n              <h4>${n("phone","الهاتف")}</h4>\n              <div class="value">${e.customer.phone||n("notSpecified","غير محدد")}</div>\n            </div>\n            <div class="info-item">\n              <h4>${n("email","البريد الإلكتروني")}</h4>\n              <div class="value">${e.customer.email||n("notSpecified","غير محدد")}</div>\n            </div>\n            <div class="info-item">\n              <h4>${n("currentBalance","الرصيد الحالي")}</h4>\n              <div class="value ${e.customer.balance>0?"credit":"cash"}">${Ut(e.customer.balance||0)}</div>\n            </div>\n          </div>\n        </div>\n\n        <div class="summary">\n          <h3>📊 ${n("transactionsSummary","ملخص المعاملات")}</h3>\n          <div class="summary-grid">\n            <div class="summary-item">\n              <div class="label">${n("totalTransactions","إجمالي المعاملات")}</div>\n              <div class="value">${e.totalOperations}</div>\n            </div>\n            <div class="summary-item">\n              <div class="label">${n("totalSales","إجمالي المبيعات")}</div>\n              <div class="value">${Ut(e.totalAmount)}</div>\n            </div>\n            <div class="summary-item">\n              <div class="label">${n("totalPayments","إجمالي المدفوعات")}</div>\n              <div class="value">${Ut(e.totalPayments)}</div>\n            </div>\n            <div class="summary-item">\n              <div class="label">${n("cashTransactions","المعاملات النقدية")}</div>\n              <div class="value cash">${e.cashOperations.length}</div>\n            </div>\n            <div class="summary-item">\n              <div class="label">${n("creditTransactions","معاملات الدين")}</div>\n              <div class="value credit">${e.creditOperations.length}</div>\n            </div>\n            <div class="summary-item">\n              <div class="label">${n("totalCash","إجمالي النقدي")}</div>\n              <div class="value cash">${Ut(e.totalCash)}</div>\n            </div>\n            <div class="summary-item">\n              <div class="label">${n("totalCredit","إجمالي الدين")}</div>\n              <div class="value credit">${Ut(e.totalCredit)}</div>\n            </div>\n          </div>\n        </div>\n\n        <table class="transactions-table">\n          <thead>\n            <tr>\n              <th>${n("operationType","نوع العملية")}</th>\n              <th>${n("invoiceNumber","رقم الفاتورة")}</th>\n              <th>${n("date","التاريخ")}</th>\n              <th>${n("paymentMethod","طريقة الدفع")}</th>\n              <th>${n("amount","المبلغ")}</th>\n              <th>${n("status","الحالة")}</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${e.allOperations.map((e=>`\n              <tr class="${"payment"===e.type?"payment-row":"sale-row"}">\n                <td>\n                  ${"payment"===e.type?"💰 "+n("payment","دفعة"):"🧾 "+n("sale","مبيعة")}\n                </td>\n                <td>${e.invoiceNumber}</td>\n                <td>${new Date(e.date).toLocaleDateString("ar"===a?"ar-DZ":"fr"===a?"fr-FR":"en-US")}</td>\n                <td class="${"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"cash":"credit"}">\n                  ${"نقداً"===e.paymentMethod?n("cash","نقداً"):"دين"===e.paymentMethod?n("credit","دين"):e.paymentMethod||n("cash","نقداً")}\n                </td>\n                <td class="${"payment"===e.type?"payment-amount":"sale-amount"}">\n                  ${"payment"===e.type?`-${Ut(Math.abs(e.finalTotal))}`:`+${Ut(e.finalTotal)}`}\n                </td>\n                <td class="${"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?"cash":"credit"}">\n                  ${"payment"===e.type?n("paymentReceived","دفعة مستلمة"):"نقداً"===e.paymentMethod||"Espèces"===e.paymentMethod||"En espèces"===e.paymentMethod||"Cash"===e.paymentMethod?n("paid","مدفوعة"):n("debt","دين")}\n                </td>\n              </tr>\n            `)).join("")}\n          </tbody>\n        </table>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p>${n("allRightsReserved","© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,i=window.open("","_blank","width=1200,height=800");i.document.write(s),i.document.close(),i.onload=()=>setTimeout((()=>i.print()),1e3),Zt(`🖨️ ${n("transactionsReportOpened","تم فتح تقرير المعاملات للطباعة")} - ${e.customer.name}`,"success",3e3)})(se),children:["🖨️ ",n("printA4","طباعة A4")]}),t.jsxs("button",{className:"btn btn-warning",onClick:()=>(e=>{const t="ar"===y,a=`\n      <!DOCTYPE html>\n      <html dir="${t?"rtl":"ltr"}" lang="${y}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${n("customerTransactionsReport","تقرير معاملات الزبون")} - ${e.customer.name}</title>\n        <style>\n          @page {\n            size: 80mm auto;\n            margin: 0;\n          }\n\n          body {\n            font-family: 'Courier New', monospace;\n            direction: ${t?"rtl":"ltr"};\n            margin: 0;\n            padding: 3mm;\n            font-size: 14px;\n            font-weight: bold;\n            width: 74mm;\n            text-align: center;\n            color: black;\n            background: white;\n            line-height: 1.4;\n          }\n\n          .header {\n            text-align: center;\n            border-bottom: 2px solid black;\n            padding-bottom: 3mm;\n            margin-bottom: 4mm;\n          }\n\n          .header h1 {\n            font-size: 16px;\n            font-weight: bold;\n            margin: 0 0 2mm 0;\n            text-transform: uppercase;\n          }\n\n          .header p {\n            font-size: 12px;\n            font-weight: bold;\n            margin: 1mm 0;\n          }\n\n          .customer-info {\n            margin-bottom: 4mm;\n            border-bottom: 2px solid black;\n            padding-bottom: 3mm;\n            text-align: center;\n          }\n\n          .customer-info h2 {\n            font-size: 14px;\n            font-weight: bold;\n            margin: 0 0 2mm 0;\n            text-transform: uppercase;\n          }\n\n          .info-line {\n            font-size: 12px;\n            font-weight: bold;\n            margin: 2mm 0;\n            display: flex;\n            justify-content: space-between;\n          }\n\n          .summary {\n            margin-bottom: 4mm;\n            border-bottom: 2px solid black;\n            padding-bottom: 3mm;\n          }\n\n          .summary h3 {\n            font-size: 14px;\n            font-weight: bold;\n            margin: 0 0 2mm 0;\n            text-transform: uppercase;\n            text-align: center;\n          }\n\n          .summary-line {\n            font-size: 12px;\n            font-weight: bold;\n            margin: 2mm 0;\n            display: flex;\n            justify-content: space-between;\n            padding: 1mm 0;\n            border-bottom: 1px dashed #666;\n          }\n\n          .transactions {\n            margin-bottom: 4mm;\n          }\n\n          .transactions h3 {\n            font-size: 14px;\n            font-weight: bold;\n            margin: 0 0 3mm 0;\n            text-transform: uppercase;\n            text-align: center;\n          }\n\n          .transaction-item {\n            font-size: 11px;\n            font-weight: bold;\n            margin: 3mm 0;\n            border: 1px solid black;\n            padding: 2mm;\n            background: #f8f8f8;\n          }\n\n          .transaction-header {\n            font-weight: bold;\n            font-size: 12px;\n            text-align: center;\n            margin-bottom: 1mm;\n          }\n\n          .transaction-details {\n            margin: 1mm 0;\n            display: flex;\n            justify-content: space-between;\n            font-size: 11px;\n          }\n\n          .payment-transaction {\n            background-color: #e8f5e8;\n            border: 2px solid #28a745;\n          }\n\n          .sale-transaction {\n            background-color: #e8f4fd;\n            border: 2px solid #007bff;\n          }\n\n          .payment-amount {\n            color: #28a745;\n            font-weight: bold;\n            font-size: 12px;\n          }\n\n          .sale-amount {\n            color: #007bff;\n            font-weight: bold;\n            font-size: 12px;\n          }\n\n          .footer {\n            text-align: center;\n            font-size: 10px;\n            font-weight: bold;\n            margin-top: 4mm;\n            border-top: 2px solid black;\n            padding-top: 3mm;\n          }\n\n          .developer-footer {\n            margin-top: 3mm;\n            padding-top: 2mm;\n            border-top: 1px dashed black;\n            font-size: 10px;\n            font-weight: bold;\n          }\n\n          .developer-name {\n            margin-bottom: 1mm;\n          }\n\n          .developer-phone {\n            font-size: 12px;\n            font-weight: bold;\n          }\n\n          @media print {\n            body {\n              width: 80mm !important;\n              font-size: 14px !important;\n              font-weight: bold !important;\n            }\n          }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>📋 ${n("customerTransactionsReport","تقرير معاملات الزبون")}</h1>\n          <p>${(new Date).toLocaleDateString("ar"===y?"ar-DZ":"fr"===y?"fr-FR":"en-US")}</p>\n          <p>${Pt.storeName}</p>\n        </div>\n\n        <div class="customer-info">\n          <h2>👤 ${n("customerInformation","معلومات العميل")}</h2>\n          <div class="info-line">\n            <span>${n("name","الاسم")}:</span>\n            <span>${e.customer.name}</span>\n          </div>\n          <div class="info-line">\n            <span>${n("phone","الهاتف")}:</span>\n            <span>${e.customer.phone||n("notSpecified","غير محدد")}</span>\n          </div>\n          <div class="info-line" style="background: #f0f0f0; padding: 2mm; border: 2px solid black; font-size: 14px;">\n            <span>${n("currentBalance","الرصيد")}:</span>\n            <span style="font-size: 16px;">${Ut(e.customer.balance||0)}</span>\n          </div>\n        </div>\n\n        <div class="summary">\n          <h3>📊 ${n("transactionsSummary","ملخص المعاملات")}</h3>\n          <div class="summary-line">\n            <span>${n("totalTransactions","إجمالي المعاملات")}:</span>\n            <span>${e.totalOperations}</span>\n          </div>\n          <div class="summary-line">\n            <span>${n("totalSales","إجمالي المبيعات")}:</span>\n            <span>${Ut(e.totalAmount)}</span>\n          </div>\n          <div class="summary-line">\n            <span>${n("totalPayments","إجمالي المدفوعات")}:</span>\n            <span>${Ut(e.totalPayments)}</span>\n          </div>\n          <div class="summary-line">\n            <span>${n("cashTransactions","المعاملات النقدية")}:</span>\n            <span>${e.cashOperations.length} (${Ut(e.totalCash)})</span>\n          </div>\n          <div class="summary-line">\n            <span>${n("creditTransactions","معاملات الدين")}:</span>\n            <span>${e.creditOperations.length} (${Ut(e.totalCredit)})</span>\n          </div>\n        </div>\n\n        <div class="transactions">\n          <h3>📋 ${n("allTransactionsList","قائمة جميع المعاملات")}</h3>\n          ${e.allOperations.map((e=>`\n            <div class="transaction-item ${"payment"===e.type?"payment-transaction":"sale-transaction"}">\n              <div class="transaction-header">\n                ${"payment"===e.type?"💰 "+n("payment","دفعة"):"🧾 "+n("sale","مبيعة")} - ${e.invoiceNumber}\n              </div>\n              <div class="transaction-details">\n                ${n("date","التاريخ")}: ${new Date(e.date).toLocaleDateString()}\n              </div>\n              <div class="transaction-details">\n                ${n("paymentMethod","طريقة الدفع")}: ${"نقداً"===e.paymentMethod?n("cash","نقداً"):"دين"===e.paymentMethod?n("credit","دين"):e.paymentMethod}\n              </div>\n              <div class="transaction-details">\n                <span class="${"payment"===e.type?"payment-amount":"sale-amount"}">\n                  ${n("amount","المبلغ")}: ${"payment"===e.type?`-${Ut(Math.abs(e.finalTotal))}`:`+${Ut(e.finalTotal)}`}\n                </span>\n              </div>\n              ${e.time?`<div class="transaction-details">${n("time","الوقت")}: ${e.time}</div>`:""}\n            </div>\n          `)).join("")}\n        </div>\n\n        <div class="footer">\n          <p>${n("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي")}</p>\n\n          <div class="developer-footer">\n            <div class="developer-name">Developed by iCode DZ</div>\n            <div class="developer-phone">0551930589</div>\n          </div>\n        </div>\n      </body>\n      </html>\n    `,s=window.open("","_blank","width=500,height=700");s.document.write(a),s.document.close(),s.onload=()=>setTimeout((()=>s.print()),1e3),Zt(`🧾 ${n("thermalTransactionsReportOpened","تم فتح تقرير المعاملات للطباعة الحرارية")} - ${e.customer.name}`,"success",3e3)})(se),children:["🧾 ",n("printThermal","طباعة حرارية")]}),t.jsxs("button",{className:"btn btn-primary",onClick:()=>ae(!1),children:["✅ ",n("close","إغلاق")]})]})]})}),re&&le&&t.jsx("div",{className:"modal-overlay",onClick:()=>oe(!1),children:t.jsxs("div",{className:`modal-content confirmation-modal lang-${y}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==y?"modal-header-ltr":""),children:[t.jsxs("h2",{children:["🗑️ ",n("confirmDeleteCustomer","هل أنت متأكد من حذف هذا الزبون؟")]}),t.jsx("button",{className:"modal-close",onClick:()=>oe(!1),children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"confirmation-content",children:[t.jsx("div",{className:"warning-icon",children:"⚠️"}),t.jsxs("div",{className:"confirmation-text",children:[t.jsx("p",{children:t.jsxs("strong",{children:[n("customerToDelete","الزبون المراد حذفه"),":"]})}),t.jsxs("div",{className:"customer-details",children:[t.jsxs("p",{children:[t.jsxs("strong",{children:[n("customerName","اسم الزبون"),":"]})," ",le.name]}),t.jsxs("p",{children:[t.jsxs("strong",{children:[n("customerNumber","رقم الزبون"),":"]})," ",le.id]}),t.jsxs("p",{children:[t.jsxs("strong",{children:[n("phone","الهاتف"),":"]})," ",le.phone||"-"]}),t.jsxs("p",{children:[t.jsxs("strong",{children:[n("balance","الرصيد"),":"]}),t.jsxs("span",{className:le.balance<=0?"text-success":"text-danger",children:[le.balance.toLocaleString()," ",Pt.currency]})]})]}),t.jsx("div",{className:"warning-message",children:t.jsxs("p",{children:["⚠️ ",n("deleteWarning","تحذير: هذا الإجراء لا يمكن التراجع عنه!")]})})]})]})}),t.jsxs("div",{className:"modal-footer "+("ar"!==y?"modal-footer-ltr":""),children:[t.jsxs("button",{className:"btn btn-danger",onClick:()=>{if(le){const e=ba.filter((e=>e.id!==le.id));ja(e),Zt(`🗑️ ${n("customerDeletedSuccessfully","تم حذف الزبون بنجاح")} - ${le.name}`,"success",3e3),oe(!1),ce(null)}},children:["🗑️ ",n("yes","نعم")," - ",n("deleteCustomer","حذف الزبون")]}),t.jsxs("button",{className:"btn btn-secondary",onClick:()=>oe(!1),children:["❌ ",n("no","لا")," - ",n("cancel","إلغاء")]})]})]})}),de&&he&&t.jsx("div",{className:"modal-overlay",onClick:()=>me(!1),children:t.jsxs("div",{className:`modal-content payment-modal lang-${y}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==y?"modal-header-ltr":""),children:[t.jsxs("h2",{children:["💰 ",n("payCustomerDebt","تسديد فواتير الزبون")," - ",he.name]}),t.jsx("button",{className:"modal-close",onClick:()=>me(!1),children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"payment-content",children:[t.jsx("div",{className:"customer-payment-info",children:t.jsxs("div",{className:"payment-info-card",children:[t.jsxs("h3",{children:["👤 ",n("customerInfo","معلومات الزبون")]}),t.jsxs("div",{className:"payment-info-grid",children:[t.jsxs("div",{className:"payment-info-item",children:[t.jsxs("strong",{children:[n("customerName","اسم الزبون"),":"]})," ",he.name]}),t.jsxs("div",{className:"payment-info-item",children:[t.jsxs("strong",{children:[n("customerNumber","رقم الزبون"),":"]})," ",he.id]}),t.jsxs("div",{className:"payment-info-item",children:[t.jsxs("strong",{children:[n("phone","الهاتف"),":"]})," ",he.phone||"-"]}),t.jsxs("div",{className:"payment-info-item",children:[t.jsxs("strong",{children:[n("currentBalance","الرصيد الحالي"),":"]}),t.jsx("span",{className:"debt-amount",children:Ut(he.balance)})]})]})]})}),t.jsxs("div",{className:"payment-form",children:[t.jsxs("h3",{children:["💳 ",n("paymentDetails","تفاصيل الدفع")]}),t.jsxs("div",{className:"form-grid",children:[t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("paymentAmount","مبلغ الدفع")," *"]}),t.jsx("input",{type:"number",value:ue,onChange:e=>xe(parseFloat(e.target.value)||0),placeholder:"0.00",min:"0",max:he.balance,step:"0.01",required:!0,autoFocus:!0}),t.jsxs("small",{className:"payment-help",children:["💡 ",n("maxPaymentAmount","الحد الأقصى"),": ",Ut(he.balance)]})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("paymentMethod","طريقة الدفع")," *"]}),t.jsxs("select",{value:ve,onChange:e=>ge(e.target.value),required:!0,children:[t.jsx("option",{value:"نقداً",children:n("cash","نقداً")}),t.jsx("option",{value:"بطاقة",children:n("creditCard","بطاقة ائتمان")}),t.jsx("option",{value:"تحويل",children:n("bankTransfer","تحويل بنكي")}),t.jsx("option",{value:"شيك",children:n("check","شيك")})]})]})]}),t.jsxs("div",{className:"payment-summary",children:[t.jsxs("div",{className:"summary-row",children:[t.jsxs("span",{children:[n("currentDebt","الدين الحالي"),":"]}),t.jsx("span",{className:"debt-amount",children:Ut(he.balance)})]}),t.jsxs("div",{className:"summary-row",children:[t.jsxs("span",{children:[n("paymentAmount","مبلغ الدفع"),":"]}),t.jsxs("span",{className:"payment-amount",children:["-",Ut(ue)]})]}),t.jsx("div",{className:"summary-divider"}),t.jsxs("div",{className:"summary-row total",children:[t.jsxs("span",{children:[n("remainingBalance","الرصيد المتبقي"),":"]}),t.jsx("span",{className:he.balance-ue<=0?"text-success":"text-danger",children:Ut(he.balance-ue)})]})]})]})]})}),t.jsxs("div",{className:"modal-footer "+("ar"!==y?"modal-footer-ltr":""),children:[t.jsxs("button",{className:"btn btn-success",onClick:()=>{if(!he||ue<=0)return void Zt(n("pleaseEnterValidAmount","يرجى إدخال مبلغ صحيح"),"error");if(ue>he.balance)return void Zt(n("paymentExceedsBalance","المبلغ المدخل أكبر من رصيد الزبون"),"error");const e=ba.map((e=>e.id===he.id?{...e,balance:e.balance-ue}:e));ja(e);const t={id:"PAY-"+Date.now(),customerId:he.id,customerName:he.name,amount:ue,paymentMethod:ve,date:(new Date).toISOString().split("T")[0],time:(new Date).toLocaleTimeString(),type:"payment"},a=[...JSON.parse(localStorage.getItem("icaldz-payments")||"[]"),t];localStorage.setItem("icaldz-payments",JSON.stringify(a)),Zt(`💰 ${n("paymentProcessedSuccessfully","تم تسديد المبلغ بنجاح")} - ${Ut(ue)} ${n("for","لـ")} ${he.name}`,"success",4e3),Oa()},children:["💰 ",n("processPayment","تأكيد الدفع")," - ",Ut(ue)]}),t.jsxs("button",{className:"btn btn-secondary",onClick:Oa,children:["❌ ",n("cancel","إلغاء")]})]})]})}),at&&t.jsx("div",{className:"modal-overlay",children:t.jsxs("div",{className:`modal lang-${y}`,children:[t.jsxs("div",{className:"modal-header "+("ar"!==y?"modal-header-ltr":""),children:[t.jsx("h3",{children:it.id?n("editExpense","تعديل مصروف"):n("addNewExpense","إضافة مصروف جديد")}),t.jsx("button",{className:"close-button",onClick:()=>st(!1),children:"×"})]}),t.jsxs("div",{className:"modal-content",children:[t.jsxs("div",{className:"form-row",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("date","التاريخ")}),t.jsx("input",{type:"date",value:it.date,onChange:e=>rt({...it,date:e.target.value}),required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("category","الفئة")}),t.jsxs("select",{value:it.category,onChange:e=>rt({...it,category:e.target.value}),required:!0,children:[t.jsx("option",{value:"رواتب",children:n("salariesWages","رواتب وأجور")}),t.jsx("option",{value:"إيجار",children:n("rent","إيجار")}),t.jsx("option",{value:"مرافق",children:n("utilities","مرافق (كهرباء، ماء، إنترنت)")}),t.jsx("option",{value:"ضرائب",children:n("taxesFees","ضرائب ورسوم")}),t.jsx("option",{value:"تسويق",children:n("marketingAdvertising","تسويق وإعلان")}),t.jsx("option",{value:"صيانة",children:n("maintenanceRepairs","صيانة وإصلاحات")}),t.jsx("option",{value:"نقل",children:n("transportationTravel","نقل ومواصلات")}),t.jsx("option",{value:"أخرى",children:n("otherExpenses","مصاريف أخرى")})]})]})]}),t.jsxs("div",{className:"form-row",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("amount","المبلغ")}),t.jsx("input",{type:"number",value:it.amount,onChange:e=>rt({...it,amount:e.target.value}),required:!0,min:"0"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("paymentMethod","طريقة الدفع")}),t.jsxs("select",{value:it.paymentMethod,onChange:e=>rt({...it,paymentMethod:e.target.value}),children:[t.jsx("option",{value:"نقداً",children:n("cash","نقداً")}),t.jsx("option",{value:"بطاقة",children:n("creditCard","بطاقة ائتمان")}),t.jsx("option",{value:"تحويل",children:n("bankTransfer","تحويل بنكي")}),t.jsx("option",{value:"شيك",children:n("check","شيك")}),t.jsx("option",{value:"أخرى",children:n("otherPaymentMethod","طريقة أخرى")})]})]})]}),t.jsx("div",{className:"form-row",children:t.jsxs("div",{className:"form-group full-width",children:[t.jsx("label",{children:n("description","الوصف")}),t.jsx("textarea",{value:it.description,onChange:e=>rt({...it,description:e.target.value}),placeholder:n("expenseDescription","أدخل وصفاً تفصيلياً للمصروف"),rows:"3"})]})})]}),t.jsxs("div",{className:"modal-footer "+("ar"!==y?"modal-footer-ltr":""),children:[t.jsx("button",{className:"cancel-button",onClick:()=>st(!1),children:n("cancel","إلغاء")}),t.jsx("button",{className:"save-button",onClick:()=>{if(it.date&&it.category&&it.amount){if(it.id){const e=tt.map((e=>e.id===it.id?it:e));et(e),Zt(`✅ ${n("expenseUpdatedSuccess","تم تحديث المصروف بنجاح")}`,"success")}else{const e={...it,id:Date.now().toString(),amount:parseFloat(it.amount)};et([...tt,e]),Zt(`✅ ${n("expenseAddedSuccess","تم إضافة المصروف بنجاح")}`,"success")}rt({id:"",date:(new Date).toISOString().split("T")[0],category:"رواتب",amount:0,description:"",paymentMethod:"نقداً"}),st(!1)}else Zt(`❌ ${n("fillRequiredFields","يرجى ملء جميع الحقول المطلوبة")}`,"error")},children:n("save","حفظ")})]})]})}),ot&&t.jsx("div",{className:"modal-overlay",onClick:on,children:t.jsxs("div",{className:"modal-content supplier-modal "+("ar"!==y?"modal-ltr":""),onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==y?"modal-header-ltr":""),children:[t.jsxs("h2",{children:["🏭 ",n("addNewSupplier","إضافة مورّد جديد")]}),t.jsx("button",{className:"modal-close",onClick:on,children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"form-grid",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("supplierNumber","رقم المورد")}),t.jsx("input",{type:"text",value:xt.id,readOnly:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("supplierName","اسم المورد")," *"]}),t.jsx("input",{type:"text",value:xt.name,onChange:e=>vt({...xt,name:e.target.value}),placeholder:n("enterSupplierName","أدخل اسم المورد"),required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("supplierPhone","رقم الهاتف")}),t.jsx("input",{type:"tel",value:xt.phone,onChange:e=>vt({...xt,phone:e.target.value}),placeholder:n("phoneNumberPlaceholder","رقم الهاتف")})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("supplierEmail","البريد الإلكتروني")}),t.jsx("input",{type:"email",value:xt.email,onChange:e=>vt({...xt,email:e.target.value}),placeholder:n("emailPlaceholder","البريد الإلكتروني")})]}),t.jsxs("div",{className:"form-group full-width",children:[t.jsx("label",{children:n("supplierAddress","العنوان")}),t.jsx("input",{type:"text",value:xt.address,onChange:e=>vt({...xt,address:e.target.value}),placeholder:n("fullAddress","العنوان الكامل")})]})]})}),t.jsxs("div",{className:"modal-footer "+("ar"!==y?"modal-footer-ltr":""),children:[t.jsxs("button",{className:"btn btn-success",onClick:()=>{if(!xt.name.trim())return void Zt(n("pleaseEnterSupplierName","يرجى إدخال اسم المورد"),"error");const e=xt.name.trim();if(Qe.find((t=>t.name.toLowerCase().trim()===e.toLowerCase()&&t.id!==xt.id)))return void Zt(`❌ ${n("supplierNameAlreadyExists","اسم المورد موجود بالفعل")}: "${e}" - ${n("pleaseChooseDifferentName","يرجى اختيار اسم مختلف")}`,"error",4e3);if(xt.phone&&""!==xt.phone.trim()){const e=Qe.find((e=>e.phone===xt.phone.trim()&&e.id!==xt.id));if(e)return void Zt(`❌ ${n("phoneNumberAlreadyExists","رقم الهاتف موجود بالفعل")}: "${xt.phone}" - ${n("usedBySupplier","مستخدم بواسطة")}: "${e.name}"`,"error",4e3)}const t={...xt,createdAt:(new Date).toISOString()},a=[...Qe,t];nn(a),on(),Zt(n("supplierAddedSuccessfully","تم إضافة المورد بنجاح"),"success")},children:["✅ ",n("saveSupplier","حفظ المورد")]}),t.jsxs("button",{className:"btn btn-secondary",onClick:on,children:["❌ ",n("cancel","إلغاء")]})]})]})}),mt&&pt&&t.jsx("div",{className:"modal-overlay",onClick:cn,children:t.jsxs("div",{className:"modal-content supplier-modal "+("ar"!==y?"modal-ltr":""),onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==y?"modal-header-ltr":""),children:[t.jsxs("h2",{children:["✏️ ",n("editSupplier","تعديل المورد")]}),t.jsx("button",{className:"modal-close",onClick:cn,children:"×"})]}),t.jsxs("div",{className:"modal-body",children:[t.jsxs("div",{className:"form-grid",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("supplierNumber","رقم المورد")}),t.jsx("input",{type:"text",value:pt.id,readOnly:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("supplierName","اسم المورد")," *"]}),t.jsx("input",{type:"text",value:pt.name,onChange:e=>ut({...pt,name:e.target.value}),placeholder:n("enterSupplierName","أدخل اسم المورد"),autoFocus:!0})]})]}),t.jsxs("div",{className:"form-row",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("supplierPhone","رقم الهاتف")}),t.jsx("input",{type:"tel",value:pt.phone,onChange:e=>ut({...pt,phone:e.target.value}),placeholder:n("enterSupplierPhone","أدخل رقم الهاتف")})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("supplierEmail","البريد الإلكتروني")}),t.jsx("input",{type:"email",value:pt.email,onChange:e=>ut({...pt,email:e.target.value}),placeholder:n("enterSupplierEmail","أدخل البريد الإلكتروني")})]})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("supplierAddress","العنوان")}),t.jsx("textarea",{value:pt.address,onChange:e=>ut({...pt,address:e.target.value}),placeholder:n("enterSupplierAddress","أدخل العنوان"),rows:"3"})]})]}),t.jsxs("div",{className:"modal-footer",children:[t.jsx("button",{className:"btn btn-secondary",onClick:cn,children:n("cancel","إلغاء")}),t.jsx("button",{className:"btn btn-primary",onClick:()=>{if(!pt.name.trim())return void Zt(n("pleaseEnterSupplierName","يرجى إدخال اسم المورد"),"error");const e=Qe.map((e=>e.id===pt.id?pt:e));nn(e),cn(),Zt(n("supplierUpdatedSuccessfully","تم تحديث المورد بنجاح"),"success")},children:n("save","حفظ")})]})]})}),ct&&t.jsx("div",{className:"modal-overlay",onClick:()=>dt(!1),children:t.jsxs("div",{className:`modal-content large-modal lang-${y}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==y?"modal-header-ltr":""),children:[t.jsx("h2",{children:n("suppliersManagement","إدارة الموردين")}),t.jsx("button",{className:"modal-close",onClick:()=>dt(!1),children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"suppliers-management",children:[t.jsx("div",{className:"suppliers-header",children:t.jsxs("button",{className:"btn btn-success",onClick:rn,children:["➕ ",n("addNewSupplierButton","إضافة مورد جديد")]})}),t.jsx("div",{className:"suppliers-table-container",children:t.jsxs("table",{className:"data-table "+("ar"!==y?"table-ltr":""),children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:n("supplierID","رقم المورد")}),t.jsx("th",{children:n("supplierNameHeader","اسم المورد")}),t.jsx("th",{children:n("supplierPhoneHeader","رقم الهاتف")}),t.jsx("th",{children:n("supplierEmailHeader","البريد الإلكتروني")}),t.jsx("th",{children:n("supplierAddressHeader","العنوان")}),t.jsx("th",{children:n("actions","الإجراءات")})]})}),t.jsx("tbody",{children:0===Qe.length?t.jsx("tr",{children:t.jsx("td",{colSpan:"6",style:{textAlign:"center",padding:"20px"},children:n("noSuppliersAdded","لا توجد موردين مضافين")})}):Qe.map((e=>t.jsxs("tr",{children:[t.jsx("td",{children:e.id}),t.jsx("td",{children:e.name}),t.jsx("td",{children:e.phone}),t.jsx("td",{children:e.email}),t.jsx("td",{children:e.address}),t.jsx("td",{children:t.jsxs("button",{className:"btn btn-danger btn-sm",onClick:()=>ln(e.id),children:["🗑️ ",n("deleteSupplier","حذف")]})})]},e.id)))})]})})]})}),t.jsx("div",{className:"modal-footer "+("ar"!==y?"modal-footer-ltr":""),children:t.jsx("button",{className:"btn btn-secondary",onClick:()=>dt(!1),children:n("close","إغلاق")})})]})}),na&&t.jsx("div",{className:"modal-overlay",onClick:hs,children:t.jsxs("div",{className:`modal-content product-modal lang-${y}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==y?"modal-header-ltr":""),children:[t.jsx("h2",{children:Dn.find((e=>e.barcode===An.barcode&&""!==An.barcode.trim()))?`✏️ ${n("editExistingProduct","تعديل منتج موجود")}`:`📦 ${n("addNewProduct","إضافة منتج جديد")}`}),t.jsx("button",{className:"modal-close",onClick:hs,children:"×"})]}),t.jsxs("div",{className:"modal-body",children:[!Dn.find((e=>e.id===An.id))&&t.jsxs("div",{className:"barcode-scanner-section",children:[t.jsxs("h3",{children:["📷 ",n("scanBarcode","مسح الباركود")]}),t.jsxs("div",{className:"barcode-input-container",children:[t.jsx("span",{className:"barcode-icon",children:"📷"}),t.jsx("input",{type:"text",placeholder:n("scanBarcodeOrEnter","امسح الباركود أو أدخله يدوياً - سيتم التوليد التلقائي إذا تُرك فارغاً"),value:An.barcode,onChange:e=>{(e=>{if(Dn.find((e=>e.id===An.id)))return void Rn({...An,barcode:e});const t=$(e);if(Rn({...An,barcode:t}),t.length>=3){const e=Dn.find((e=>e.barcode===t&&""!==t.trim()));e?Zt(`ℹ️ ${n("productExistsWithBarcode","يوجد منتج بهذا الباركود")}: ${e.name}. ${n("checkBeforeSaving","تحقق قبل الحفظ")}`,"warning",4e3):t.length>=8&&/^\d+$/.test(t)?Zt(`✅ ${n("barcodeScannedSuccess","تم مسح الباركود بنجاح - باركود جديد")}: ${t}`,"success",2e3):Zt(`✅ ${n("barcodeAvailable","الباركود متاح للاستخدام - منتج جديد")}: ${t}`,"success",2e3)}})(e.target.value)},onKeyDown:e=>{if("Enter"===e.key){const t=e.target.value;t.length>=3&&(e=>{if(Dn.find((e=>e.id===An.id)))return void Zt(`📋 ${n("currentBarcode","الباركود الحالي")}: ${e}`,"info",2e3);const t=Dn.find((t=>t.barcode===e));t?Zt(`⚠️ ${n("productExistsWithBarcode","يوجد منتج بهذا الباركود")}: ${t.name}. ${n("checkBeforeSaving","تحقق قبل الحفظ")}`,"warning",4e3):Zt(`✅ ${n("barcodeAvailable","الباركود متاح للاستخدام - منتج جديد")}`,"success",2e3)})(t)}},onFocus:()=>{window.barcodeShortcutManager&&(window.barcodeShortcutManager.isBarcodeActive=!0,window.barcodeShortcutManager.setShortcutsEnabled(!1))},onBlur:()=>{setTimeout((()=>{window.barcodeShortcutManager&&!window.barcodeShortcutManager.checkBarcodeInput(document.activeElement)&&(window.barcodeShortcutManager.isBarcodeActive=!1,window.barcodeShortcutManager.setShortcutsEnabled(!0))}),100)},className:"barcode-input",autoFocus:!0})]}),t.jsxs("div",{className:"barcode-actions",children:[t.jsxs("button",{type:"button",className:"btn btn-secondary btn-sm",onClick:()=>{const e=ds();Rn({...An,barcode:e}),Zt(`🔢 ${n("autoBarcodeGenerated","تم توليد باركود تلقائي")}: ${e}`,"info",3e3)},children:["🔢 ",n("generateAutoBarcode","توليد باركود تلقائي")]}),t.jsxs("button",{type:"button",className:"btn btn-warning btn-sm",onClick:()=>{Rn({...An,barcode:""}),Zt(`🗑️ ${n("barcodeCleared","تم مسح الباركود")}`,"info",2e3)},children:["🗑️ ",n("clearBarcode","مسح الباركود")]})]}),t.jsxs("small",{className:"barcode-help",children:["💡 ",n("barcodeHelp","يمكنك مسح الباركود باستخدام قارئ الباركود أو إدخاله يدوياً. إذا كان الباركود موجود في المخزون، سيتم عرض معلومات المنتج للتعديل. إذا تُرك فارغاً، سيتم توليد باركود تلقائياً عند الحفظ.")]})]}),t.jsxs("div",{className:"form-grid",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("productNumber","رقم المنتج")}),t.jsx("input",{type:"text",value:An.id,readOnly:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("productName","اسم المنتج")," *"]}),t.jsx("input",{type:"text",value:An.name,onChange:e=>{const t=e.target.value;if(Rn({...An,name:t}),t.trim().length>2){Dn.find((e=>e.name.toLowerCase().trim()===t.toLowerCase().trim()&&e.id!==An.id))?(e.target.style.borderColor="#e74c3c",e.target.style.backgroundColor="#fdf2f2"):(e.target.style.borderColor="#27ae60",e.target.style.backgroundColor="#f0fff4")}else e.target.style.borderColor="#ddd",e.target.style.backgroundColor="white"},placeholder:n("enterProductName","أدخل اسم المنتج"),required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("category","الفئة")," *"]}),t.jsxs("div",{className:"category-input-group",children:[t.jsxs("select",{value:An.category,onChange:e=>Rn({...An,category:e.target.value}),required:!0,children:[t.jsx("option",{value:"",children:n("selectCategory","اختر الفئة")}),Fn.map((e=>t.jsx("option",{value:e,children:e},e)))]}),t.jsx("button",{type:"button",className:"btn btn-secondary btn-sm",onClick:Sn,title:n("manageCategories","إدارة الفئات"),children:"⚙️"})]})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:["📷 ",n("currentBarcode","الباركود الحالي")]}),t.jsxs("div",{className:"barcode-input-group",children:[t.jsx("input",{type:"text",value:An.barcode,onChange:e=>{const t=e.target.value;if((e=>{Rn({...An,barcode:e})})(t),t.trim().length>2){Dn.find((e=>e.barcode===t&&e.id!==An.id))?(e.target.style.borderColor="#e74c3c",e.target.style.backgroundColor="#fdf2f2"):(e.target.style.borderColor="#27ae60",e.target.style.backgroundColor="#f0fff4")}else e.target.style.borderColor="#ddd",e.target.style.backgroundColor="white"},placeholder:n("barcodeWillShow","سيتم عرض الباركود هنا"),className:"barcode-input-field"}),t.jsx("button",{type:"button",className:"btn btn-secondary btn-sm",onClick:()=>{const e=ds();Rn({...An,barcode:e}),Zt(`🔢 ${n("autoBarcodeGenerated","تم توليد باركود تلقائي")}: ${e}`,"info",3e3)},title:n("generateAutoBarcode","توليد باركود تلقائي"),children:"🔢"}),t.jsx("button",{type:"button",className:"btn btn-warning btn-sm",onClick:()=>{Rn({...An,barcode:""}),Zt(`🗑️ ${n("barcodeCleared","تم مسح الباركود")}`,"info",2e3)},title:n("clearBarcode","مسح الباركود"),children:"🗑️"})]})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("buyPrice","سعر الشراء")," *"]}),t.jsx("input",{type:"number",value:An.buyPrice,onChange:e=>Rn({...An,buyPrice:parseFloat(e.target.value)||0}),placeholder:"0.00",min:"0",step:"0.01",required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("sellPrice","سعر البيع")," *"]}),t.jsx("input",{type:"number",value:An.sellPrice,onChange:e=>Rn({...An,sellPrice:parseFloat(e.target.value)||0}),placeholder:"0.00",min:"0",step:"0.01",required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("currentQuantity","الكمية الحالية")}),t.jsx("input",{type:"number",value:An.stock,onChange:e=>Rn({...An,stock:parseInt(e.target.value)||0}),placeholder:"0",min:"0"})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("minStock","الحد الأدنى")}),t.jsx("input",{type:"number",value:An.minStock,onChange:e=>Rn({...An,minStock:parseInt(e.target.value)||0}),placeholder:"5",min:"0"})]})]})]}),t.jsxs("div",{className:"modal-footer",children:[t.jsx("button",{className:"btn btn-success",onClick:ps,children:Dn.find((e=>e.barcode===An.barcode&&""!==An.barcode.trim()))?`✅ ${n("saveChanges","حفظ التعديلات")}`:`✅ ${n("saveProduct","حفظ المنتج")}`}),t.jsxs("button",{className:"btn btn-secondary",onClick:hs,children:["❌ ",n("cancel","إلغاء")]})]})]})}),Ln&&t.jsx("div",{className:"modal-overlay",onClick:kn,children:t.jsxs("div",{className:"modal-content",onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header",children:[t.jsx("h2",{children:n("categoryManagement","إدارة فئات المنتجات")}),t.jsx("button",{className:"modal-close",onClick:kn,children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"category-management",children:[t.jsxs("div",{className:"add-category-section",children:[t.jsx("h3",{children:n("addNewCategory","إضافة فئة جديدة")}),t.jsxs("div",{className:"form-row",children:[t.jsx("input",{type:"text",value:Bn,onChange:e=>Un(e.target.value),placeholder:n("newCategoryName","اسم الفئة الجديدة"),onKeyPress:e=>"Enter"===e.key&&Cn()}),t.jsxs("button",{className:"btn btn-success",onClick:Cn,children:["➕ ",n("add","إضافة")]})]})]}),t.jsxs("div",{className:"categories-list",children:[t.jsx("h3",{children:n("existingCategories","الفئات الموجودة")}),t.jsx("div",{className:"categories-grid",children:Fn.map(((e,a)=>t.jsx("div",{className:"category-item",children:qn===e?t.jsx("div",{className:"edit-category",children:t.jsx("input",{type:"text",defaultValue:e,onKeyPress:t=>{"Enter"===t.key&&In(e,t.target.value)},onBlur:t=>In(e,t.target.value),autoFocus:!0})}):t.jsxs("div",{className:"category-display",children:[t.jsx("span",{className:"category-name",children:e}),t.jsxs("div",{className:"category-actions",children:[t.jsx("button",{className:"btn btn-sm btn-info",onClick:()=>Vn(e),title:n("edit","تعديل"),children:"✏️"}),t.jsx("button",{className:"btn btn-sm btn-danger",onClick:()=>(e=>{const t=Dn.filter((t=>t.category===e));if(t.length>0){if(!window.confirm(`هذه الفئة مستخدمة في ${t.length} منتج. هل تريد حذفها؟ سيتم تحويل المنتجات إلى فئة "غير مصنف"`))return;const n=Dn.map((t=>t.category===e?{...t,category:"غير مصنف"}:t));Mn(n);const a=Fn.filter((t=>t!==e));a.includes("غير مصنف")||a.push("غير مصنف"),wn(a)}else{const t=Fn.filter((t=>t!==e));wn(t)}Zt(`✅ تم حذف الفئة "${e}" بنجاح`,"success")})(e),title:n("delete","حذف"),children:"🗑️"})]})]})},a)))})]})]})}),t.jsx("div",{className:"modal-footer",children:t.jsx("button",{className:"btn btn-secondary",onClick:kn,children:"إغلاق"})})]})}),Zn&&Jn&&t.jsx("div",{className:"modal-overlay",onClick:xn,children:t.jsxs("div",{className:`modal-content large-modal lang-${y}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==y?"modal-header-ltr":""),children:[t.jsxs("h2",{children:[n("editInvoiceTitle","تعديل الفاتورة")," ",Jn.invoiceNumber]}),t.jsx("button",{className:"modal-close",onClick:xn,children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"invoice-edit-form",children:[t.jsxs("div",{className:"form-row",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("invoiceNumber","رقم الفاتورة")}),t.jsx("input",{type:"text",value:Jn.invoiceNumber,readOnly:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("date","التاريخ")}),t.jsx("input",{type:"date",value:Jn.date,onChange:e=>_n({...Jn,date:e.target.value})})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("customer","العميل")}),t.jsxs("select",{value:Jn.customerId||"GUEST",onChange:e=>{const t=e.target.value,a=ba.find((e=>e.id===t));_n({...Jn,customerId:t,customerName:"GUEST"===t?n("walkInCustomer","زبون عابر"):a?.name||""})},children:[t.jsx("option",{value:"GUEST",children:n("walkInCustomer","زبون عابر")}),ba.map((e=>t.jsxs("option",{value:e.id,children:[e.name," - ",e.phone]},e.id)))]})]})]}),t.jsx("div",{className:"sales-barcode-scanner-row "+("ar"!==y?"edit-invoice-layout-ltr":""),children:"ar"!==y?t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"lcd-display-row lcd-left",children:t.jsx("div",{className:"lcd-screen-big",children:t.jsxs("div",{className:"total-final-display",children:[t.jsxs("div",{className:"total-final-label",children:[n("finalTotal","المجموع النهائي"),":"]}),t.jsx("div",{className:"total-final-amount",children:Ut(Jn.finalTotal)}),t.jsx("div",{className:"total-final-currency",children:"DZD"})]})})}),t.jsxs("div",{className:"scanner-input-row scanner-right",children:[t.jsxs("h3",{className:"scanner-title",children:["📷 ",n("activeBarcodeScanner","Actif - Scanner le code-barres")," 📷"]}),t.jsxs("div",{className:"barcode-input-container",children:[t.jsx("span",{className:"barcode-icon",children:"📷"}),t.jsx("input",{type:"text",placeholder:n("scanToAddProduct","امسح الباركود لإضافة منتج"),value:Ga,onChange:os,onKeyDown:ls,onFocus:()=>{window.barcodeShortcutManager&&(window.barcodeShortcutManager.isBarcodeActive=!0,window.barcodeShortcutManager.setShortcutsEnabled(!1))},onBlur:()=>{setTimeout((()=>{window.barcodeShortcutManager&&!window.barcodeShortcutManager.checkBarcodeInput(document.activeElement)&&(window.barcodeShortcutManager.isBarcodeActive=!1,window.barcodeShortcutManager.setShortcutsEnabled(!0))}),100)},className:"barcode-input",ref:Ya,autoFocus:!0}),t.jsxs("button",{type:"button",className:"btn btn-warning btn-sm",onClick:()=>{Ja("")},title:n("clearBarcode","مسح الباركود"),children:["🗑️ ",n("clear","مسح")]})]})]})]}):t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"scanner-input-row",children:[t.jsxs("h3",{className:"scanner-title",children:["📷 ",n("activeBarcodeScanner","نشط - مسح الباركود")," 📷"]}),t.jsxs("div",{className:"barcode-input-container",children:[t.jsx("span",{className:"barcode-icon",children:"📷"}),t.jsx("input",{type:"text",placeholder:n("scanToAddProduct","امسح الباركود لإضافة منتج"),value:Ga,onChange:os,onKeyDown:ls,onFocus:()=>{window.barcodeShortcutManager&&(window.barcodeShortcutManager.isBarcodeActive=!0,window.barcodeShortcutManager.setShortcutsEnabled(!1))},onBlur:()=>{setTimeout((()=>{window.barcodeShortcutManager&&!window.barcodeShortcutManager.checkBarcodeInput(document.activeElement)&&(window.barcodeShortcutManager.isBarcodeActive=!1,window.barcodeShortcutManager.setShortcutsEnabled(!0))}),100)},className:"barcode-input",ref:Ya,autoFocus:!0}),t.jsxs("button",{type:"button",className:"btn btn-warning btn-sm",onClick:()=>{Ja("")},title:n("clearBarcode","مسح الباركود"),children:["🗑️ ",n("clear","مسح")]})]})]}),t.jsx("div",{className:"lcd-display-row",children:t.jsx("div",{className:"lcd-screen-big",children:t.jsxs("div",{className:"total-final-display",children:[t.jsxs("div",{className:"total-final-label",children:[n("finalTotal","المجموع النهائي"),":"]}),t.jsx("div",{className:"total-final-amount",children:Ut(Jn.finalTotal)}),t.jsx("div",{className:"total-final-currency",children:"DZD"})]})})})]})}),t.jsxs("div",{className:"product-selection-section",children:[t.jsxs("h3",{children:["🛒 ",n("addProductFromList","إضافة منتج من القائمة")]}),t.jsx("div",{className:"product-add-form",children:t.jsxs("div",{className:"form-row",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("selectProduct","اختر المنتج")}),t.jsxs("select",{value:la,onChange:e=>ca(e.target.value),children:[t.jsx("option",{value:"",children:n("selectProductOption","اختر منتج")}),Dn.map((e=>t.jsxs("option",{value:e.id,children:[e.name," - ",Ut(e.sellPrice)," (",n("available","متوفر"),": ",e.stock,")"]},e.id)))]})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("quantity","الكمية")}),t.jsx("input",{type:"number",value:da,onChange:e=>ma(parseInt(e.target.value)||1),min:"1",placeholder:"1"})]}),t.jsx("div",{className:"form-group",children:t.jsxs("button",{className:"btn btn-success",onClick:()=>{if(!la)return void Zt(`⚠️ ${n("pleaseSelectProduct","يرجى اختيار منتج")}`,"warning",3e3);const e=Dn.find((e=>e.id===la));if(!e)return void Zt(`❌ ${n("productNotFound","لم يتم العثور على المنتج")}`,"error",3e3);if(e.stock<da)return void Zt(`❌ ${n("insufficientStock","كمية غير كافية في المخزون")}: ${e.name} (${n("available","متوفر")}: ${e.stock})`,"error",3e3);const t=Jn.items.findIndex((t=>t.productId===e.id));if(-1!==t){const a=[...Jn.items],s=a[t].quantity+da;if(s>e.stock)return void Zt(`❌ ${n("insufficientStock","كمية غير كافية في المخزون")}: ${e.name} (${n("available","متوفر")}: ${e.stock})`,"error",3e3);a[t]={...a[t],quantity:s,total:s*a[t].price};const i=a.reduce(((e,t)=>e+t.total),0),r=i*(Pt.taxRate/100),o=i+r-Jn.discount;_n({...Jn,items:a,total:i,tax:r,finalTotal:o}),Zt(`✅ ${n("quantityUpdated","تم تحديث الكمية")}: ${e.name} (${s})`,"success",2e3)}else{const t={id:Date.now(),productId:e.id,productName:e.name,name:e.name,price:e.sellPrice,quantity:da,total:e.sellPrice*da},a=[...Jn.items,t],s=a.reduce(((e,t)=>e+t.total),0),i=s*(Pt.taxRate/100),r=s+i-Jn.discount;_n({...Jn,items:a,total:s,tax:i,finalTotal:r}),Zt(`✅ ${n("productAdded","تم إضافة المنتج")}: ${e.name} (${n("quantity","الكمية")}: ${da})`,"success",2e3)}ca(""),ma(1),j.play("addProduct")},disabled:!la,children:["➕ ",n("addProduct","إضافة المنتج")]})})]})})]}),t.jsxs("div",{className:"invoice-items-section",children:[t.jsx("h3",{children:n("invoiceItems","عناصر الفاتورة")}),t.jsx("div",{className:"items-table",children:t.jsxs("table",{className:"data-table "+("ar"!==y?"table-ltr":""),children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:n("product","المنتج")}),t.jsx("th",{children:n("price","السعر")}),t.jsx("th",{children:n("quantity","الكمية")}),t.jsx("th",{children:n("total","المجموع")}),t.jsx("th",{children:n("actions","الإجراءات")})]})}),t.jsx("tbody",{children:Jn.items.map(((e,n)=>t.jsxs("tr",{children:[t.jsx("td",{children:e.productName}),t.jsx("td",{children:Ut(e.price)}),t.jsx("td",{children:t.jsx("input",{type:"number",value:e.quantity,onChange:e=>((e,t)=>{if(t<=0)return;const n=[...Jn.items];n[e]={...n[e],quantity:t,total:n[e].price*t};const a=n.reduce(((e,t)=>e+t.total),0),s=a*(Pt.taxRate/100),i=a+s-Jn.discount;_n({...Jn,items:n,total:a,tax:s,finalTotal:i})})(n,parseInt(e.target.value)||1),min:"1",style:{width:"80px"}})}),t.jsx("td",{children:Ut(e.total)}),t.jsx("td",{children:t.jsx("button",{className:"btn btn-danger btn-sm",onClick:()=>(e=>{const t=Jn.items.filter(((t,n)=>n!==e)),n=t.reduce(((e,t)=>e+t.total),0),a=n*(Pt.taxRate/100),s=n+a-Jn.discount;_n({...Jn,items:t,total:n,tax:a,finalTotal:s})})(n),children:"🗑️"})})]},n)))})]})})]}),t.jsxs("div",{className:"invoice-totals",children:[t.jsxs("div",{className:"totals-row",children:[t.jsx("span",{children:n("subtotalLabel","المجموع الفرعي:")}),t.jsx("span",{children:Ut(Jn.total)})]}),t.jsxs("div",{className:"totals-row",children:[t.jsxs("span",{children:[n("taxLabel","الضريبة")," (",Pt.taxRate,"%):"]}),t.jsx("span",{children:Ut(Jn.tax)})]}),t.jsxs("div",{className:"totals-row total-final",children:[t.jsx("span",{children:n("finalTotalLabel","المجموع النهائي:")}),t.jsx("span",{children:Ut(Jn.finalTotal)})]})]})]})}),t.jsxs("div",{className:"modal-footer "+("ar"!==y?"modal-footer-ltr":""),children:[t.jsxs("button",{className:"btn btn-success",onClick:()=>{if(!Jn)return;const e={};Wn.forEach((t=>{e[t.productId]||(e[t.productId]=0),e[t.productId]+=t.quantity})),Jn.items.forEach((t=>{e[t.productId]||(e[t.productId]=0),e[t.productId]-=t.quantity}));const t=Dn.map((t=>{if(e[t.id]){const a=t.stock+e[t.id];return a<0?(Zt(`❌ ${n("insufficientStockForProduct","المخزون غير كافي للمنتج")} ${t.name}`,"error"),t):{...t,stock:a}}return t})),a=Ce.map((e=>e.invoiceNumber===Jn.invoiceNumber?Jn:e));Mn(t),Ie(a),localStorage.setItem("icaldz-invoices",JSON.stringify(a)),Zt(`✅ ${n("invoiceUpdatedAndStockAdjusted","تم تحديث الفاتورة")} ${Jn.invoiceNumber} ${n("andStockAdjusted","وتحديث المخزون")}`,"success"),xn()},children:["✅ ",n("saveChanges","حفظ التعديلات")]}),t.jsxs("button",{className:"btn btn-secondary",onClick:xn,children:["❌ ",n("cancel","إلغاء")]})]})]})}),Qn&&Kn&&t.jsx("div",{className:"modal-overlay",onClick:gn,children:t.jsxs("div",{className:"modal-content large-modal",onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header",children:[t.jsxs("h2",{children:[n("returnProducts","إرجاع منتجات من الفاتورة")," ",Kn.invoiceNumber]}),t.jsx("button",{className:"modal-close",onClick:gn,children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"return-form",children:[t.jsxs("div",{className:"return-info",children:[t.jsxs("p",{children:[t.jsxs("strong",{children:[n("invoiceDate","تاريخ الفاتورة"),":"]})," ",new Date(Kn.date).toLocaleDateString("ar-DZ")]}),t.jsxs("p",{children:[t.jsxs("strong",{children:[n("customer","العميل"),":"]})," ",Kn.customerName]}),t.jsxs("p",{children:[t.jsxs("strong",{children:[n("originalTotal","المجموع الأصلي"),":"]})," ",Ut(Kn.finalTotal)]})]}),t.jsxs("div",{className:"return-items-section",children:[t.jsx("h3",{children:n("selectProductsToReturn","تحديد المنتجات المراد إرجاعها")}),t.jsx("div",{className:"items-table",children:t.jsxs("table",{className:"data-table",children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:n("product","المنتج")}),t.jsx("th",{children:n("price","السعر")}),t.jsx("th",{children:n("originalQuantity","الكمية الأصلية")}),t.jsx("th",{children:n("returnQuantity","كمية الإرجاع")}),t.jsx("th",{children:n("returnValue","قيمة الإرجاع")})]})}),t.jsx("tbody",{children:ea.map(((e,n)=>t.jsxs("tr",{children:[t.jsx("td",{children:e.productName}),t.jsx("td",{children:Ut(e.price)}),t.jsx("td",{children:e.maxReturnQuantity}),t.jsx("td",{children:t.jsx("input",{type:"number",value:e.returnQuantity,onChange:e=>((e,t)=>{const n=[...ea],a=n[e].maxReturnQuantity;t<0&&(t=0),t>a&&(t=a),n[e].returnQuantity=t,ta(n)})(n,parseInt(e.target.value)||0),min:"0",max:e.maxReturnQuantity,style:{width:"80px"}})}),t.jsx("td",{children:Ut(e.price*e.returnQuantity)})]},n)))})]})})]}),t.jsx("div",{className:"return-summary",children:t.jsxs("div",{className:"summary-row",children:[t.jsxs("span",{children:[n("totalReturnValue","إجمالي قيمة الإرجاع"),":"]}),t.jsx("span",{children:Ut(ea.reduce(((e,t)=>e+t.price*t.returnQuantity),0))})]})})]})}),t.jsxs("div",{className:"modal-footer",children:[t.jsxs("button",{className:"btn btn-success",onClick:()=>{const e=ea.filter((e=>e.returnQuantity>0));if(0===e.length)return void Zt(`⚠️ ${n("pleaseSelectAtLeastOneProduct","يرجى تحديد كمية الإرجاع لمنتج واحد على الأقل")}`,"warning");const t=Dn.map((t=>{const n=e.find((e=>e.productId===t.id));return n?{...t,stock:t.stock+n.returnQuantity}:t})),a=Kn.items.map((t=>{const n=e.find((e=>e.productId===t.productId));if(n){const e=t.quantity-n.returnQuantity;return{...t,quantity:e,total:t.price*e}}return t})).filter((e=>e.quantity>0)),s=a.reduce(((e,t)=>e+t.total),0),i=s*(Pt.taxRate/100),r=s+i-Kn.discount,o={...Kn,items:a,total:s,tax:i,finalTotal:r},l=Ce.map((e=>e.invoiceNumber===Kn.invoiceNumber?o:e)),c=[{id:"RET-"+Date.now(),originalInvoiceNumber:Kn.invoiceNumber,date:(new Date).toISOString().split("T")[0],items:e.map((e=>({productId:e.productId,productName:e.productName,quantity:e.returnQuantity,price:e.price,total:e.price*e.returnQuantity}))),totalAmount:e.reduce(((e,t)=>e+t.price*t.returnQuantity),0),processedBy:Ot.name},...JSON.parse(localStorage.getItem("icaldz-returns")||"[]")];localStorage.setItem("icaldz-returns",JSON.stringify(c)),Mn(t),Ie(l),localStorage.setItem("icaldz-invoices",JSON.stringify(l));const d=e.map((e=>`${e.productName}: ${e.returnQuantity}`)).join(", ");Zt(`✅ ${n("productsReturnedSuccessfully","تم إرجاع المنتجات")}: ${d} ${n("andStockUpdated","وتحديث المخزون")}`,"success",5e3),gn()},children:["✅ ",n("confirmReturn","تأكيد الإرجاع")]}),t.jsxs("button",{className:"btn btn-secondary",onClick:gn,children:["❌ ",n("cancel","إلغاء")]})]})]})}),Rt&&t.jsx("div",{className:"modal-overlay",onClick:$n,children:t.jsxs("div",{className:`modal-content lang-${y}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==y?"modal-header-ltr":""),children:[t.jsx("h2",{children:n("addNewSellerTitle","إضافة بائع جديد")}),t.jsx("button",{className:"modal-close",onClick:$n,children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"form-grid",children:[t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("sellerID","رقم البائع")}),t.jsx("input",{type:"text",value:zt.id,readOnly:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("sellerNameLabel","اسم البائع")," *"]}),t.jsx("input",{type:"text",value:zt.name,onChange:e=>Bt({...zt,name:e.target.value}),placeholder:n("enterSellerName","أدخل اسم البائع"),required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("usernameLabel","اسم المستخدم")," *"]}),t.jsx("input",{type:"text",value:zt.username,onChange:e=>Bt({...zt,username:e.target.value}),placeholder:n("enterUsername","أدخل اسم المستخدم"),required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:[n("passwordLabel","كلمة المرور")," *"]}),t.jsx("input",{type:"password",value:zt.password,onChange:e=>Bt({...zt,password:e.target.value}),placeholder:n("enterPassword","أدخل كلمة المرور"),required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("phoneLabel","رقم الهاتف")}),t.jsx("input",{type:"tel",value:zt.phone,onChange:e=>Bt({...zt,phone:e.target.value}),placeholder:n("enterPhone","+*********** 456")})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("emailLabel","البريد الإلكتروني")}),t.jsx("input",{type:"email",value:zt.email,onChange:e=>Bt({...zt,email:e.target.value}),placeholder:n("enterEmail","أدخل البريد الإلكتروني")})]}),t.jsxs("div",{className:"form-group",children:[t.jsx("label",{children:n("roleLabel","الدور")}),t.jsxs("select",{value:zt.role,onChange:e=>Bt({...zt,role:e.target.value}),children:[t.jsx("option",{value:"seller",children:n("seller","بائع")}),t.jsx("option",{value:"admin",children:n("admin","مدير")})]})]}),t.jsx("div",{className:"form-group",children:t.jsxs("label",{className:"checkbox-label",children:[t.jsx("input",{type:"checkbox",checked:zt.isActive,onChange:e=>Bt({...zt,isActive:e.target.checked})}),n("active","حساب نشط")]})})]})}),t.jsxs("div",{className:"modal-footer "+("ar"!==y?"modal-footer-ltr":""),children:[t.jsxs("button",{className:"btn btn-success",onClick:()=>{if(!zt.name||!zt.username||!zt.password)return void Zt(`⚠️ ${n("fillRequiredFields","يرجى ملء جميع الحقول المطلوبة")}`,"warning",3e3);const e=zt.name.trim();if(Et.find((t=>t.name.toLowerCase().trim()===e.toLowerCase()&&t.id!==zt.id)))return void Zt(`❌ ${n("sellerNameAlreadyExists","اسم البائع موجود بالفعل")}: "${e}" - ${n("pleaseChooseDifferentName","يرجى اختيار اسم مختلف")}`,"error",4e3);const t=Et.find((e=>e.username===zt.username&&e.id!==zt.id));if(t)return void Zt(`❌ ${n("usernameExists","اسم المستخدم موجود مسبقاً")}: "${zt.username}" - ${n("usedBySeller","مستخدم بواسطة")}: "${t.name}"`,"error",4e3);const a={...zt,createdAt:(new Date).toISOString()},s=[...Et,a];en(s),Zt(`✅ ${n("sellerAddedSuccess","تم إضافة البائع")} ${zt.name} ${n("sellerAddedSuccess","بنجاح!")}`,"success",3e3),$n()},children:["✅ ",n("addNewSeller","إضافة البائع")]}),t.jsxs("button",{className:"btn btn-secondary",onClick:$n,children:["❌ ",n("cancel","إلغاء")]})]})]})}),qe&&Ze&&t.jsx("div",{className:"modal-overlay",onClick:un,children:t.jsxs("div",{className:"modal-content invoice-modal",onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header",children:[t.jsxs("h2",{children:["📄 ",n("invoiceDetails","تفاصيل الفاتورة")," ",Ze.invoiceNumber]}),t.jsx("button",{className:"modal-close",onClick:un,children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"invoice-view",children:[t.jsxs("div",{className:"invoice-header-view",children:[t.jsx("div",{className:"store-info",children:t.jsxs("div",{className:"store-details",children:[t.jsx("h1",{children:Pt.storeName}),t.jsxs("p",{children:["📞 ",Pt.storePhone]}),t.jsxs("p",{children:["📍 ",Pt.storeAddress]})]})}),t.jsxs("div",{className:"invoice-info",children:[t.jsx("h2",{children:n("salesInvoice","فاتورة مبيعات")}),t.jsxs("p",{children:[t.jsx("strong",{children:n("invoiceNumberColon","رقم الفاتورة:")})," ",Ze.invoiceNumber]}),t.jsxs("p",{children:[t.jsx("strong",{children:n("dateColon","التاريخ:")})," ",Ze.date]}),t.jsxs("p",{children:[t.jsx("strong",{children:n("creationTimeColon","وقت الإنشاء:")})," ",Ze.createdAt||(new Date).toLocaleString("ar-DZ")]})]})]}),t.jsxs("div",{className:"customer-info",children:[t.jsxs("h3",{children:["👤 ",n("customerInfoTitle","معلومات العميل")]}),t.jsxs("div",{className:"info-grid",children:[t.jsxs("div",{className:"info-item",children:[t.jsx("strong",{children:n("customerNameColon","اسم العميل:")})," ",Ze.customerName||n("walkInCustomer","زبون عابر")]}),t.jsxs("div",{className:"info-item",children:[t.jsx("strong",{children:n("paymentMethodColon","طريقة الدفع:")}),t.jsx("span",{className:"payment-method "+("نقداً"===Ze.paymentMethod||"Espèces"===Ze.paymentMethod||"En espèces"===Ze.paymentMethod||"Cash"===Ze.paymentMethod?"cash":"credit"),children:"نقداً"===Ze.paymentMethod?n("cash","نقداً"):"دين"===Ze.paymentMethod?n("credit","دين"):Ze.paymentMethod||n("cash","نقداً")})]})]})]}),t.jsxs("div",{className:"invoice-items",children:[t.jsxs("h3",{children:["🛒 ",n("productsTitle","المنتجات")]}),t.jsx("div",{className:"table-container",children:t.jsxs("table",{className:"data-table",children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:n("productColumn","المنتج")}),t.jsx("th",{children:n("quantityColumn","الكمية")}),t.jsx("th",{children:n("priceColumn","السعر")}),t.jsx("th",{children:n("totalColumn","المجموع")})]})}),t.jsx("tbody",{children:Ze.items&&Ze.items.length>0?Ze.items.map(((e,n)=>t.jsxs("tr",{children:[t.jsx("td",{children:e.productName}),t.jsx("td",{children:e.quantity}),t.jsx("td",{children:Ut(e.price)}),t.jsx("td",{children:Ut(e.total)})]},n))):t.jsx("tr",{children:t.jsx("td",{colSpan:"4",style:{textAlign:"center",padding:"20px"},children:n("noProductsInInvoice","لا توجد منتجات في هذه الفاتورة")})})})]})})]}),t.jsx("div",{className:"invoice-totals",children:t.jsxs("div",{className:"totals-section",children:[t.jsxs("div",{className:"total-row",children:[t.jsx("span",{children:n("subtotalColon","المجموع الفرعي:")}),t.jsx("span",{children:Ut(Ze.total||0)})]}),t.jsxs("div",{className:"total-row",children:[t.jsxs("span",{children:[n("taxColon","الضريبة")," (",Pt.taxRate,"%):"]}),t.jsx("span",{children:Ut(Ze.tax||0)})]}),t.jsxs("div",{className:"total-row",children:[t.jsx("span",{children:n("discountColon","الخصم:")}),t.jsx("span",{children:Ut(Ze.discount||0)})]}),t.jsxs("div",{className:"total-row final-total",children:[t.jsx("span",{children:n("finalTotalColon","المجموع النهائي:")}),t.jsx("span",{children:Ut(Ze.finalTotal||0)})]})]})})]})}),t.jsxs("div",{className:"modal-footer",children:[t.jsxs("button",{className:"btn btn-secondary",onClick:()=>pn(Ze),title:n("normalPrintButton","طباعة عادية"),children:["🖨️ ",n("normalPrintButton","طباعة عادية")]}),t.jsxs("button",{className:"btn btn-success",onClick:()=>mn(Ze),title:n("thermalPrintButton","طباعة حرارية"),children:["🧾 ",n("thermalPrintButton","طباعة حرارية")]}),t.jsxs("button",{className:"btn btn-primary",onClick:un,children:["✅ ",n("closeButton","إغلاق")]})]})]})}),t.jsxs("div",{className:"toast-container",children:[Be.map((e=>t.jsxs("div",{className:`toast toast-${e.type}`,onClick:t=>{t.preventDefault(),t.stopPropagation(),Gt(e.id)},children:[t.jsxs("div",{className:"toast-content",children:[t.jsx("span",{className:"toast-message",children:e.message}),t.jsx("button",{className:"toast-close",onClick:t=>{t.preventDefault(),t.stopPropagation(),Gt(e.id)},onMouseDown:e=>{e.preventDefault(),e.stopPropagation()},onTouchStart:e=>{e.preventDefault(),e.stopPropagation()},children:"×"})]}),t.jsx("div",{className:"toast-progress",children:t.jsx("div",{className:"toast-progress-bar",style:{animation:`toast-progress ${e.duration}ms linear forwards`}})})]},e.id))),Be.length>2&&t.jsx("div",{className:"toast-clear-all",children:t.jsxs("button",{className:"btn btn-sm btn-secondary",onClick:e=>{e.preventDefault(),e.stopPropagation(),Jt()},children:["🗑️ ",n("clearAllNotifications","مسح جميع الإشعارات")]})})]}),gt&&t.jsx("div",{className:"modal-overlay",onClick:Nn,children:t.jsxs("div",{className:`modal-content settings-modal lang-${y}`,onClick:e=>e.stopPropagation(),children:[t.jsxs("div",{className:"modal-header "+("ar"!==y?"modal-header-ltr":""),children:[t.jsxs("h2",{children:["⚙️ ",n("storeSettingsModal","إعدادات المتجر")]}),t.jsx("button",{className:"modal-close",onClick:Nn,children:"×"})]}),t.jsx("div",{className:"modal-body",children:t.jsxs("div",{className:"form-grid",children:[t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:["🏪 ",n("storeNameRequired","اسم المتجر")," *"]}),t.jsx("input",{type:"text",value:Pt.storeName,onChange:e=>Mt({...Pt,storeName:e.target.value}),placeholder:n("enterStoreName","أدخل اسم المتجر"),required:!0})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:["📞 ",n("phoneNumberLabel","رقم الهاتف")]}),t.jsx("input",{type:"tel",value:Pt.storePhone,onChange:e=>Mt({...Pt,storePhone:e.target.value}),placeholder:n("phoneNumberPlaceholder","+*********** 456")})]}),t.jsxs("div",{className:"form-group full-width",children:[t.jsxs("label",{children:["📍 ",n("addressLabel","العنوان")]}),t.jsx("input",{type:"text",value:Pt.storeAddress,onChange:e=>Mt({...Pt,storeAddress:e.target.value}),placeholder:n("fullStoreAddress","العنوان الكامل للمتجر")})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:["💰 ",n("taxRateLabel","معدل الضريبة (%)")]}),t.jsx("input",{type:"number",value:Pt.taxRate,onChange:e=>Mt({...Pt,taxRate:parseFloat(e.target.value)||0}),placeholder:n("taxRatePlaceholder","19"),min:"0",max:"100",step:"0.1"})]}),t.jsxs("div",{className:"form-group",children:[t.jsxs("label",{children:["💱 ",n("currencyLabel","العملة")]}),t.jsxs("select",{value:Pt.currency,onChange:e=>Mt({...Pt,currency:e.target.value}),children:[t.jsx("option",{value:"DZD",children:n("algerianDinar","دينار جزائري (DZD)")}),t.jsx("option",{value:"USD",children:n("usDollar","دولار أمريكي (USD)")}),t.jsx("option",{value:"EUR",children:n("euro","يورو (EUR)")})]})]}),t.jsxs("div",{className:"form-group full-width",children:[t.jsxs("label",{children:["🖼️ ",n("storeLogo","شعار المتجر")]}),t.jsx("input",{type:"file",accept:"image/*",onChange:e=>{const t=e.target.files[0];if(t){const e=new FileReader;e.onload=e=>{Mt({...Pt,storeLogo:e.target.result})},e.readAsDataURL(t)}}}),Pt.storeLogo&&t.jsx("div",{className:"logo-preview",children:t.jsx("img",{src:Pt.storeLogo,alt:n("logoPreview","شعار المتجر")})})]})]})}),t.jsxs("div",{className:"modal-footer "+("ar"!==y?"modal-footer-ltr":""),children:[t.jsxs("button",{className:"btn btn-success",onClick:()=>{var e;e=Pt,localStorage.setItem("icaldz-settings",JSON.stringify(e)),Mt(e),Zt(`✅ ${n("storeSettingsSaved","تم حفظ إعدادات المتجر بنجاح")}`,"success",3e3),Nn()},children:["✅ ",n("saveSettings","حفظ الإعدادات")]}),t.jsxs("button",{className:"btn btn-secondary",onClick:Nn,children:["❌ ",n("cancel","إلغاء")]})]})]})}),t.jsx("div",{className:"toast-container",children:Be.map((e=>t.jsxs("div",{className:`toast toast-${e.type}`,onClick:()=>Gt(e.id),children:[t.jsx("div",{className:"toast-content",children:e.message}),t.jsx("button",{className:"toast-close",onClick:t=>{t.stopPropagation(),Gt(e.id)},children:"×"})]},e.id)))})]})," ",B&&t.jsx("button",{className:"scroll-to-top",onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},title:n("scrollToTop","العودة إلى الأعلى"),children:"↑"}),T&&!I&&t.jsx(f,{onActivationSuccess:e=>{D(!0)}})]}):t.jsxs("div",{className:"login-container",children:[t.jsxs("div",{className:"login-card",children:[t.jsxs("div",{className:"login-header",children:[t.jsx("div",{className:"login-avatar",children:"👤"}),t.jsx("h2",{children:n("welcome","مرحباً بك")}),t.jsx("p",{children:n("loginPrompt","قم بتسجيل الدخول للوصول إلى حسابك")})]}),t.jsxs("form",{onSubmit:gs,className:"login-form",children:[t.jsx("div",{className:"form-group",children:t.jsx("input",{type:"text",placeholder:n("username","اسم المستخدم"),value:we.username,onChange:e=>Se({...we,username:e.target.value}),required:!0})}),t.jsx("div",{className:"form-group",children:t.jsx("input",{type:"password",placeholder:n("password","كلمة المرور"),value:we.password,onChange:e=>Se({...we,password:e.target.value}),required:!0})}),t.jsx("div",{className:"form-group",children:t.jsxs("label",{className:"checkbox-label",children:[t.jsx("input",{type:"checkbox"}),n("rememberMe","تذكرني")]})}),t.jsx("button",{type:"submit",className:"login-btn",children:n("login","تسجيل الدخول")})]}),t.jsxs("div",{className:"login-footer",children:[t.jsxs("p",{children:["© 2025 ",n("systemName","نظام المحاسبي")," - ",n("allRightsReserved","جميع الحقوق محفوظة")]}),t.jsxs("p",{children:[n("version","الإصدار")," 1.0.0"]})]})]}),t.jsx("div",{className:"login-info",children:t.jsxs("div",{className:"system-info",children:[t.jsx("div",{className:"system-icon",children:"🏛️"}),t.jsx("h1",{children:n("systemName","نظام المحاسبي")}),t.jsx("p",{children:n("systemDescription","النظام المحاسبي المتكامل لإدارة أعمالك")}),t.jsxs("div",{className:"features-list",children:[t.jsxs("div",{className:"feature",children:["✅ ",n("feature1","إدارة المبيعات والمشتريات بكفاءة عالية")]}),t.jsxs("div",{className:"feature",children:["✅ ",n("feature2","إدارة المخزون ومراقبة الأصناف")]}),t.jsxs("div",{className:"feature",children:["✅ ",n("feature3","التقارير المالية والمحاسبية المتكاملة")]}),t.jsxs("div",{className:"feature",children:["✅ ",n("feature4","إدارة العملاء والموردين بسهولة")]})]})]})})]}):t.jsx(g,{onLanguageSelected:q})}function N(){return t.jsx(v,{children:t.jsx(y,{})})}n.createRoot(document.getElementById("root")).render(t.jsx(a.StrictMode,{children:t.jsx(N,{})}));
