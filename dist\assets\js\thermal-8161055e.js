import{g as t}from"./i18n-7eb290f1.js";const e=new class{constructor(){this.isInitialized=!1,this.thermalPrinterDetected=!1,this.printerSettings={width:"80mm",fontSize:"14px",fontFamily:"Courier New, monospace",lineHeight:"1.4",margin:"3mm",autocut:!0,encoding:"UTF-8"},this.init()}async init(){try{await this.detectThermalPrinter(),this.isInitialized=!0,console.log("🖨️ Thermal Printer: System initialized successfully")}catch(t){console.warn("🖨️ Thermal Printer: Initialization failed, using fallback mode",t),this.isInitialized=!0}}async detectThermalPrinter(){try{const t=["thermal","receipt","pos","epson","star","citizen","bixolon","custom","zebra","datamax","tsc"];if(navigator.mediaDevices&&navigator.mediaDevices.enumerateDevices){const e=await navigator.mediaDevices.enumerateDevices();this.thermalPrinterDetected=e.some((e=>t.some((t=>e.label.toLowerCase().includes(t)))))}return!this.thermalPrinterDetected&&window.print&&(this.thermalPrinterDetected=!0),console.log("🖨️ Thermal Printer: Detection result - "+(this.thermalPrinterDetected?"Found":"Not found")),this.thermalPrinterDetected}catch(t){return console.warn("🖨️ Thermal Printer: Detection failed",t),this.thermalPrinterDetected=!1,!1}}printInvoice(e,n={}){const{language:i="ar",showToast:r=()=>{},formatPrice:o=t=>t.toFixed(2),directPrint:a=!0,storeSettings:l={}}=n;try{const t=this.generateInvoiceContent(e,i,o,l);a&&this.thermalPrinterDetected?this.printDirectly(t,i,r):this.printWithWindow(t,i,r)}catch(s){console.error("🖨️ Thermal Printer: Print failed",s),r(`❌ ${t("printError",i)||"خطأ في الطباعة"}`,"error",3e3)}}generateInvoiceContent(e,n,i,r={}){const o=(e,i)=>t(e,n)||i;return`\n      <!DOCTYPE html>\n      <html dir="${"ar"===n?"rtl":"ltr"}" lang="${n}">\n      <head>\n        <meta charset="UTF-8">\n        <meta name="viewport" content="width=device-width, initial-scale=1.0">\n        <title>${o("thermalInvoice","فاتورة حرارية")}</title>\n        <style>\n          ${this.getThermalCSS(n)}\n        </style>\n      </head>\n      <body>\n        <div class="thermal-receipt">\n          ${this.generateHeader(n,o,r)}\n          ${this.generateInvoiceInfo(e,n,o)}\n          ${this.generateItemsTable(e,n,o,i)}\n          ${this.generateTotals(e,n,o,i)}\n          ${this.generateFooter(n,o)}\n        </div>\n      </body>\n      </html>\n    `}getThermalCSS(t){const e="ar"===t;return`\n      @page {\n        size: 80mm auto;\n        margin: 0;\n      }\n      \n      * {\n        margin: 0;\n        padding: 0;\n        box-sizing: border-box;\n      }\n      \n      body {\n        font-family: ${this.printerSettings.fontFamily};\n        font-size: ${this.printerSettings.fontSize};\n        font-weight: bold;\n        line-height: ${this.printerSettings.lineHeight};\n        color: #000;\n        background: #fff;\n        width: 74mm;\n        margin: 0 auto;\n        padding: ${this.printerSettings.margin};\n        direction: ${e?"rtl":"ltr"};\n        text-align: center;\n      }\n      \n      .thermal-receipt {\n        width: 100%;\n      }\n      \n      .thermal-header {\n        font-size: 18px;\n        font-weight: bold;\n        text-transform: uppercase;\n        margin-bottom: 4mm;\n        border-bottom: 2px solid #000;\n        padding-bottom: 2mm;\n      }\n      \n      .thermal-info {\n        font-size: 12px;\n        margin: 2mm 0;\n        text-align: ${e?"right":"left"};\n      }\n      \n      .thermal-table {\n        width: 100%;\n        border-collapse: collapse;\n        margin: 3mm 0;\n        font-size: 11px;\n      }\n      \n      .thermal-table th,\n      .thermal-table td {\n        padding: 1mm;\n        text-align: ${e?"right":"left"};\n        border-bottom: 1px solid #000;\n      }\n      \n      .thermal-table th {\n        background: #000;\n        color: #fff;\n        font-weight: bold;\n      }\n      \n      .thermal-total {\n        font-size: 16px;\n        font-weight: bold;\n        border: 3px double #000;\n        padding: 3mm;\n        margin: 3mm 0;\n        background: #f0f0f0;\n      }\n      \n      .thermal-footer {\n        font-size: 10px;\n        margin-top: 4mm;\n        border-top: 2px solid #000;\n        padding-top: 3mm;\n        text-align: center;\n      }\n      \n      .center { text-align: center; }\n      .right { text-align: right; }\n      .left { text-align: left; }\n      .bold { font-weight: bold; }\n      .separator { \n        border-top: 1px dashed #000; \n        margin: 2mm 0; \n        height: 1px; \n      }\n      \n      @media print {\n        body { \n          -webkit-print-color-adjust: exact;\n          print-color-adjust: exact;\n        }\n        .no-print { display: none !important; }\n      }\n    `}generateHeader(t,e,n={}){const i=n.storeName||"iCalDZ Store",r=n.storePhone||"+213 555 123 456";return`\n      <div class="thermal-header">\n        <div class="store-logo-section" style="text-align: center; margin-bottom: 3mm;">\n          <img src="${n.storeLogo||"./assets/logo2png.png"}" alt="${i}"\n               style="max-width: 60px; max-height: 60px; object-fit: contain; border-radius: 8px;"\n               onerror="this.style.display='none'; this.nextElementSibling.style.display='block';" />\n          <div style="display: none; font-size: 24px; font-weight: bold;">🏛️</div>\n        </div>\n        <div class="store-info" style="text-align: center; margin-bottom: 3mm;">\n          <div class="bold" style="font-size: 16px; margin-bottom: 1mm;">${i}</div>\n          <div style="font-size: 12px; margin-bottom: 1mm;">${r}</div>\n        </div>\n        <div class="thermal-title">\n          <div class="bold">${e("salesInvoiceTitle","فاتورة مبيعات")}</div>\n          <div style="font-size: 12px; margin-top: 2mm;">\n            ${e("accountingSystem","نظام المحاسبي")} - iCalDZ\n          </div>\n        </div>\n      </div>\n    `}generateInvoiceInfo(t,e,n){const i=new Date(t.date).toLocaleDateString("ar"===e?"ar-DZ":"fr"===e?"fr-FR":"en-US"),r=new Date(t.createdAt||t.date).toLocaleTimeString("ar"===e?"ar-DZ":"fr"===e?"fr-FR":"en-US");return`\n      <div class="thermal-info">\n        <div><strong>${n("invoiceNumberLabel","فاتورة رقم:")} ${t.invoiceNumber}</strong></div>\n        <div>${n("dateLabel","التاريخ:")} ${i}</div>\n        <div>${n("timeLabel","الوقت:")} ${r}</div>\n        <div>${n("customerLabel","الزبون:")} ${t.customerName||n("walkInCustomer","زبون عابر")}</div>\n        <div>${n("paymentMethodLabel","طريقة الدفع:")} ${t.paymentMethod}</div>\n      </div>\n      <div class="separator"></div>\n    `}generateItemsTable(t,e,n,i){const r=t.items||[];let o=`\n      <table class="thermal-table">\n        <thead>\n          <tr>\n            <th>${n("productColumn","المنتج")}</th>\n            <th>${n("quantityColumn","الكمية")}</th>\n            <th>${n("priceColumn","السعر")}</th>\n            <th>${n("totalColumn","المجموع")}</th>\n          </tr>\n        </thead>\n        <tbody>\n    `;return r.forEach((t=>{o+=`\n        <tr>\n          <td>${t.productName}</td>\n          <td>${t.quantity}</td>\n          <td>${i(t.price)}</td>\n          <td>${i(t.total)}</td>\n        </tr>\n      `})),o+='\n        </tbody>\n      </table>\n      <div class="separator"></div>\n    ',o}generateTotals(t,e,n,i){return`\n      <div class="thermal-info">\n        <div>${n("subtotalLabel","المجموع الفرعي:")} ${i(t.subtotal||0)}</div>\n        ${t.discount>0?`<div>${n("discountLabel","الخصم:")} ${i(t.discount)}</div>`:""}\n        ${t.tax>0?`<div>${n("taxLabel","الضريبة:")} ${i(t.tax)}</div>`:""}\n      </div>\n      <div class="thermal-total">\n        ${n("finalTotalLabel","المجموع النهائي:")} ${i(t.finalTotal)}\n      </div>\n    `}generateFooter(t,e){return`\n      <div class="thermal-footer">\n        <div class="bold">${e("thankYouMessage","شكراً لزيارتكم")}</div>\n        <div style="margin-top: 2mm;">\n          ${e("developedBy","تم التطوير بواسطة")} iCode DZ\n        </div>\n        <div>📞 0551930589</div>\n        <div style="margin-top: 2mm; font-size: 8px;">\n          ${e("printedAtLabel","طُبعت في:")} ${(new Date).toLocaleString("ar"===t?"ar-DZ":"fr"===t?"fr-FR":"en-US")}\n        </div>\n      </div>\n    `}printDirectly(e,n,i){try{const r=document.createElement("iframe");r.style.position="absolute",r.style.left="-9999px",r.style.top="-9999px",r.style.width="1px",r.style.height="1px",document.body.appendChild(r);const o=r.contentDocument||r.contentWindow.document;o.open(),o.write(e),o.close(),r.onload=()=>{setTimeout((()=>{try{r.contentWindow.focus(),r.contentWindow.print(),setTimeout((()=>{document.body.removeChild(r)}),1e3);i(`🖨️ ${((e,i)=>t(e,n)||i)("invoiceSentToThermalPrinter","تم إرسال الفاتورة للطباعة الحرارية")}`,"success",3e3)}catch(o){console.error("🖨️ Direct print failed, falling back to window method",o),this.printWithWindow(e,n,i)}}),500)}}catch(r){console.error("🖨️ Direct printing failed",r),this.printWithWindow(e,n,i)}}printWithWindow(e,n,i){const r=window.open("","_blank","width=400,height=700,scrollbars=yes");if(!r){return void i(`❌ ${((e,i)=>t(e,n)||i)("popupBlocked","تم حظر النافذة المنبثقة - يرجى السماح بالنوافذ المنبثقة")}`,"error",5e3)}r.document.write(e),r.document.close(),r.onload=()=>{setTimeout((()=>{r.focus(),r.print(),setTimeout((()=>{r.close()}),2e3)}),1e3)};var o,a;i(`🖨️ ${o="printWindowOpened",a="تم فتح نافذة الطباعة",t(o,n)||a}`,"info",3e3)}isThermalPrinterAvailable(){return this.isInitialized&&this.thermalPrinterDetected}getStatus(){return{initialized:this.isInitialized,thermalDetected:this.thermalPrinterDetected,settings:this.printerSettings}}};export{e as t};
