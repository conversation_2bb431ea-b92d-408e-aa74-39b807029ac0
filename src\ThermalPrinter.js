/**
 * 🖨️ THERMAL PRINTER MODULE - DIRECT PRINTING WITH ARABIC RTL SUPPORT
 * 
 * Features:
 * - Direct printing without dialog windows
 * - Arabic RTL language support
 * - Multi-language translations (AR/FR/EN)
 * - Thermal printer optimization (80mm)
 * - Auto-detection of thermal printer
 * - Fallback to regular printing if thermal not available
 */

import { getTranslation } from './translations.js';

export class ThermalPrinter {
  constructor() {
    this.isInitialized = false;
    this.thermalPrinterDetected = false;
    this.printerSettings = {
      width: '80mm',
      fontSize: '14px',
      fontFamily: 'Courier New, monospace',
      lineHeight: '1.4',
      margin: '3mm',
      autocut: true,
      encoding: 'UTF-8'
    };
    
    this.init();
  }

  /**
   * Initialize thermal printer system
   */
  async init() {
    try {
      // Check if thermal printer is available
      await this.detectThermalPrinter();
      this.isInitialized = true;
      console.log('🖨️ Thermal Printer: System initialized successfully');
    } catch (error) {
      console.warn('🖨️ Thermal Printer: Initialization failed, using fallback mode', error);
      this.isInitialized = true;
    }
  }

  /**
   * Detect if thermal printer is installed
   */
  async detectThermalPrinter() {
    try {
      // Check for common thermal printer names
      const thermalPrinterNames = [
        'thermal', 'receipt', 'pos', 'epson', 'star', 'citizen',
        'bixolon', 'custom', 'zebra', 'datamax', 'tsc'
      ];

      // Try to detect printer through various methods
      if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
        const devices = await navigator.mediaDevices.enumerateDevices();
        this.thermalPrinterDetected = devices.some(device => 
          thermalPrinterNames.some(name => 
            device.label.toLowerCase().includes(name)
          )
        );
      }

      // Alternative detection method using print capabilities
      if (!this.thermalPrinterDetected && window.print) {
        // Assume thermal printer is available if system supports printing
        this.thermalPrinterDetected = true;
      }

      console.log(`🖨️ Thermal Printer: Detection result - ${this.thermalPrinterDetected ? 'Found' : 'Not found'}`);
      return this.thermalPrinterDetected;
    } catch (error) {
      console.warn('🖨️ Thermal Printer: Detection failed', error);
      this.thermalPrinterDetected = false;
      return false;
    }
  }

  /**
   * Print invoice directly to thermal printer
   */
  printInvoice(invoice, options = {}) {
    const {
      language = 'ar',
      showToast = () => {},
      formatPrice = (price) => price.toFixed(2),
      directPrint = true,
      storeSettings = null
    } = options;

    try {
      const content = this.generateInvoiceContent(invoice, language, formatPrice, storeSettings);

      if (directPrint && this.thermalPrinterDetected) {
        this.printDirectly(content, language, showToast);
      } else {
        this.printWithWindow(content, language, showToast);
      }
    } catch (error) {
      console.error('🖨️ Thermal Printer: Print failed', error);
      showToast(`❌ ${getTranslation('printError', language) || 'خطأ في الطباعة'}`, 'error', 3000);
    }
  }

  /**
   * Generate thermal-optimized invoice content
   */
  generateInvoiceContent(invoice, language, formatPrice, storeSettings = null) {
    const isRTL = language === 'ar';
    const t = (key, fallback) => getTranslation(key, language) || fallback;

    return `
      <!DOCTYPE html>
      <html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${language}">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${t('thermalInvoice', 'فاتورة حرارية')}</title>
        <style>
          ${this.getThermalCSS(language)}
        </style>
      </head>
      <body>
        <div class="thermal-receipt">
          ${this.generateHeader(language, t, storeSettings)}
          ${this.generateInvoiceInfo(invoice, language, t)}
          ${this.generateItemsTable(invoice, language, t, formatPrice)}
          ${this.generateTotals(invoice, language, t, formatPrice)}
          ${this.generateFooter(language, t)}
        </div>
      </body>
      </html>
    `;
  }

  /**
   * Generate thermal-optimized CSS
   */
  getThermalCSS(language) {
    const isRTL = language === 'ar';
    
    return `
      @page {
        size: 80mm auto;
        margin: 0;
      }
      
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: ${this.printerSettings.fontFamily};
        font-size: ${this.printerSettings.fontSize};
        font-weight: bold;
        line-height: ${this.printerSettings.lineHeight};
        color: #000;
        background: #fff;
        width: 74mm;
        margin: 0 auto;
        padding: ${this.printerSettings.margin};
        direction: ${isRTL ? 'rtl' : 'ltr'};
        text-align: center;
      }
      
      .thermal-receipt {
        width: 100%;
      }
      
      .thermal-header {
        font-size: 18px;
        font-weight: bold;
        text-transform: uppercase;
        margin-bottom: 4mm;
        border-bottom: 2px solid #000;
        padding-bottom: 2mm;
        text-align: center;
      }

      .thermal-logo {
        text-align: center;
        margin-bottom: 2mm;
      }

      .thermal-logo img {
        max-width: 60mm;
        max-height: 20mm;
        width: auto;
        height: auto;
      }

      .thermal-logo-fallback {
        font-size: 24px;
        font-weight: bold;
        border: 2px solid #000;
        padding: 2mm;
        display: inline-block;
        background: #000;
        color: #fff;
      }

      .thermal-store-name {
        font-size: 16px;
        font-weight: bold;
        text-align: center;
        margin: 2mm 0;
      }

      .thermal-store-number {
        font-size: 14px;
        font-weight: bold;
        text-align: center;
        margin: 1mm 0 3mm 0;
        border: 1px solid #000;
        padding: 1mm;
        background: #f0f0f0;
      }
      
      .thermal-info {
        font-size: 12px;
        margin: 2mm 0;
        text-align: ${isRTL ? 'right' : 'left'};
      }
      
      .thermal-table {
        width: 100%;
        border-collapse: collapse;
        margin: 3mm 0;
        font-size: 11px;
      }
      
      .thermal-table th,
      .thermal-table td {
        padding: 1mm;
        text-align: ${isRTL ? 'right' : 'left'};
        border-bottom: 1px solid #000;
      }
      
      .thermal-table th {
        background: #000;
        color: #fff;
        font-weight: bold;
      }
      
      .thermal-total {
        font-size: 16px;
        font-weight: bold;
        border: 3px double #000;
        padding: 3mm;
        margin: 3mm 0;
        background: #f0f0f0;
      }
      
      .thermal-footer {
        font-size: 10px;
        margin-top: 4mm;
        border-top: 2px solid #000;
        padding-top: 3mm;
        text-align: center;
      }
      
      .center { text-align: center; }
      .right { text-align: right; }
      .left { text-align: left; }
      .bold { font-weight: bold; }
      .separator { 
        border-top: 1px dashed #000; 
        margin: 2mm 0; 
        height: 1px; 
      }
      
      @media print {
        body { 
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
        .no-print { display: none !important; }
      }
    `;
  }

  /**
   * Generate header section with logo, store name, and store number
   */
  generateHeader(language, t, storeSettings = null) {
    const defaultStoreSettings = {
      storeName: 'iCalDZ Store',
      storeNumber: 'ST001',
      storeLogo: '',
      storePhone: '+213 555 123 456'
    };

    const settings = storeSettings || defaultStoreSettings;

    return `
      <div class="thermal-header">
        <!-- Logo at top middle -->
        <div class="thermal-logo">
          ${settings.storeLogo ?
            `<img src="${settings.storeLogo}" alt="${settings.storeName}" />` :
            `<div class="thermal-logo-fallback">iC</div>`
          }
        </div>

        <!-- Store name below logo -->
        <div class="thermal-store-name">${settings.storeName}</div>

        <!-- Store number under store name -->
        <div class="thermal-store-number">
          ${t('storeNumber', 'رقم المتجر')}: ${settings.storeNumber || 'ST001'}
        </div>

        <!-- Invoice title -->
        <div class="bold" style="margin-top: 3mm;">${t('salesInvoiceTitle', 'فاتورة مبيعات')}</div>
        <div style="font-size: 12px; margin-top: 2mm;">
          ${t('accountingSystem', 'نظام المحاسبي')} - iCalDZ
        </div>
      </div>
    `;
  }

  /**
   * Generate invoice information section
   */
  generateInvoiceInfo(invoice, language, t) {
    const isRTL = language === 'ar';
    const date = new Date(invoice.date).toLocaleDateString(
      language === 'ar' ? 'ar-DZ' : language === 'fr' ? 'fr-FR' : 'en-US'
    );
    const time = new Date(invoice.createdAt || invoice.date).toLocaleTimeString(
      language === 'ar' ? 'ar-DZ' : language === 'fr' ? 'fr-FR' : 'en-US'
    );

    return `
      <div class="thermal-info">
        <div><strong>${t('invoiceNumberLabel', 'فاتورة رقم:')} ${invoice.invoiceNumber}</strong></div>
        <div>${t('dateLabel', 'التاريخ:')} ${date}</div>
        <div>${t('timeLabel', 'الوقت:')} ${time}</div>
        <div>${t('customerLabel', 'الزبون:')} ${invoice.customerName || t('walkInCustomer', 'زبون عابر')}</div>
        <div>${t('paymentMethodLabel', 'طريقة الدفع:')} ${invoice.paymentMethod}</div>
      </div>
      <div class="separator"></div>
    `;
  }

  /**
   * Generate items table
   */
  generateItemsTable(invoice, language, t, formatPrice) {
    const items = invoice.items || [];
    
    let tableHTML = `
      <table class="thermal-table">
        <thead>
          <tr>
            <th>${t('productColumn', 'المنتج')}</th>
            <th>${t('quantityColumn', 'الكمية')}</th>
            <th>${t('priceColumn', 'السعر')}</th>
            <th>${t('totalColumn', 'المجموع')}</th>
          </tr>
        </thead>
        <tbody>
    `;

    items.forEach(item => {
      tableHTML += `
        <tr>
          <td>${item.productName}</td>
          <td>${item.quantity}</td>
          <td>${formatPrice(item.price)}</td>
          <td>${formatPrice(item.total)}</td>
        </tr>
      `;
    });

    tableHTML += `
        </tbody>
      </table>
      <div class="separator"></div>
    `;

    return tableHTML;
  }

  /**
   * Generate totals section
   */
  generateTotals(invoice, language, t, formatPrice) {
    return `
      <div class="thermal-info">
        <div>${t('subtotalLabel', 'المجموع الفرعي:')} ${formatPrice(invoice.subtotal || 0)}</div>
        ${invoice.discount > 0 ? `<div>${t('discountLabel', 'الخصم:')} ${formatPrice(invoice.discount)}</div>` : ''}
        ${invoice.tax > 0 ? `<div>${t('taxLabel', 'الضريبة:')} ${formatPrice(invoice.tax)}</div>` : ''}
      </div>
      <div class="thermal-total">
        ${t('finalTotalLabel', 'المجموع النهائي:')} ${formatPrice(invoice.finalTotal)}
      </div>
    `;
  }

  /**
   * Generate footer section
   */
  generateFooter(language, t) {
    return `
      <div class="thermal-footer">
        <div class="bold">${t('thankYouMessage', 'شكراً لزيارتكم')}</div>
        <div style="margin-top: 2mm;">
          ${t('developedBy', 'تم التطوير بواسطة')} iCode DZ
        </div>
        <div>📞 0551930589</div>
        <div style="margin-top: 2mm; font-size: 8px;">
          ${t('printedAtLabel', 'طُبعت في:')} ${new Date().toLocaleString(
            language === 'ar' ? 'ar-DZ' : language === 'fr' ? 'fr-FR' : 'en-US'
          )}
        </div>
      </div>
    `;
  }

  /**
   * Print directly without dialog (when thermal printer detected)
   */
  printDirectly(content, language, showToast) {
    try {
      // Create a hidden iframe for direct printing
      const iframe = document.createElement('iframe');
      iframe.style.position = 'absolute';
      iframe.style.left = '-9999px';
      iframe.style.top = '-9999px';
      iframe.style.width = '1px';
      iframe.style.height = '1px';
      
      document.body.appendChild(iframe);
      
      const doc = iframe.contentDocument || iframe.contentWindow.document;
      doc.open();
      doc.write(content);
      doc.close();
      
      // Wait for content to load then print
      iframe.onload = () => {
        setTimeout(() => {
          try {
            iframe.contentWindow.focus();
            iframe.contentWindow.print();
            
            // Clean up
            setTimeout(() => {
              document.body.removeChild(iframe);
            }, 1000);
            
            const t = (key, fallback) => getTranslation(key, language) || fallback;
            showToast(`🖨️ ${t('invoiceSentToThermalPrinter', 'تم إرسال الفاتورة للطباعة الحرارية')}`, 'success', 3000);
          } catch (printError) {
            console.error('🖨️ Direct print failed, falling back to window method', printError);
            this.printWithWindow(content, language, showToast);
          }
        }, 500);
      };
    } catch (error) {
      console.error('🖨️ Direct printing failed', error);
      this.printWithWindow(content, language, showToast);
    }
  }

  /**
   * Print with window (fallback method)
   */
  printWithWindow(content, language, showToast) {
    const printWindow = window.open('', '_blank', 'width=400,height=700,scrollbars=yes');
    
    if (!printWindow) {
      const t = (key, fallback) => getTranslation(key, language) || fallback;
      showToast(`❌ ${t('popupBlocked', 'تم حظر النافذة المنبثقة - يرجى السماح بالنوافذ المنبثقة')}`, 'error', 5000);
      return;
    }
    
    printWindow.document.write(content);
    printWindow.document.close();
    
    printWindow.onload = () => {
      setTimeout(() => {
        printWindow.focus();
        printWindow.print();
        
        // Auto-close after printing (optional)
        setTimeout(() => {
          printWindow.close();
        }, 2000);
      }, 1000);
    };
    
    const t = (key, fallback) => getTranslation(key, language) || fallback;
    showToast(`🖨️ ${t('printWindowOpened', 'تم فتح نافذة الطباعة')}`, 'info', 3000);
  }

  /**
   * Check if thermal printer is available
   */
  isThermalPrinterAvailable() {
    return this.isInitialized && this.thermalPrinterDetected;
  }

  /**
   * Get printer status
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      thermalDetected: this.thermalPrinterDetected,
      settings: this.printerSettings
    };
  }
}

// Create singleton instance
export const thermalPrinter = new ThermalPrinter();

// Export default
export default thermalPrinter;
