import{C as e}from"./vendor-38106ca9.js";const t=new class{constructor(){this.defaultConfig={servers:{defaultPorts:[3e3,3001,3002,3003,5e3,5001,8080,8081],syncInterval:3e4,requestTimeout:5e3,retryAttempts:3},profit:{useDefaultPrices:!1,defaultMargin:.3,requireValidPrices:!0,excludeInvalidProducts:!0},validation:{strictMode:!0,logWarnings:!0,logErrors:!0},ui:{showDetailedLogs:!1,autoRefreshReports:!0,refreshInterval:6e4},build:{environment:"production",version:"1.0.0",buildDate:(new Date).toISOString()}},this.config=this.loadConfig()}loadConfig(){try{const e=localStorage.getItem("icaldz-config");if(e){const t=JSON.parse(e);return this.mergeConfig(this.defaultConfig,t)}}catch(e){console.warn("خطأ في تحميل التكوين، استخدام الإعدادات الافتراضية:",e)}return{...this.defaultConfig}}saveConfig(){try{localStorage.setItem("icaldz-config",JSON.stringify(this.config)),localStorage.setItem("icaldz-config-timestamp",Date.now().toString())}catch(e){console.error("خطأ في حفظ التكوين:",e)}}mergeConfig(e,t){const r={...e};for(const a in t)t.hasOwnProperty(a)&&("object"!=typeof t[a]||Array.isArray(t[a])?r[a]=t[a]:r[a]=this.mergeConfig(e[a]||{},t[a]));return r}get(e){const t=e.split(".");let r=this.config;for(const a of t){if(!r||!r.hasOwnProperty(a))return;r=r[a]}return r}set(e,t){const r=e.split(".");let a=this.config;for(let n=0;n<r.length-1;n++){const e=r[n];a[e]&&"object"==typeof a[e]||(a[e]={}),a=a[e]}a[r[r.length-1]]=t,this.saveConfig()}reset(){this.config={...this.defaultConfig},this.saveConfig()}getServerList(){const e=new URL(window.location.origin),t=parseInt(e.port)||("https:"===e.protocol?443:80),r=`${e.protocol}//${e.hostname}`,a=this.get("servers.defaultPorts")||[3e3,3001,3002],n=[];return n.push(window.location.origin),a.forEach((e=>{e!==t&&n.push(`${r}:${e}`)})),[...new Set(n)]}validateProfitSettings(){const e=this.get("profit.useDefaultPrices"),t=this.get("profit.requireValidPrices");return e&&t&&console.warn("تحذير: تم تفعيل استخدام الأسعار الافتراضية والتحقق الصارم معاً"),{useDefaultPrices:e,requireValidPrices:t,excludeInvalidProducts:this.get("profit.excludeInvalidProducts")}}getBuildInfo(){return{environment:this.get("build.environment"),version:this.get("build.version"),buildDate:this.get("build.buildDate"),isProduction:"production"===this.get("build.environment"),isDevelopment:"development"===this.get("build.environment")}}};const r=new class{constructor(){this.currentServer=window.location.origin,this.servers=t.getServerList(),this.syncInterval=t.get("servers.syncInterval")||3e4,this.requestTimeout=t.get("servers.requestTimeout")||5e3,this.retryAttempts=t.get("servers.retryAttempts")||3,this.isOnline=navigator.onLine,this.syncTimer=null,this.lastSyncTime=null,window.addEventListener("online",(()=>{this.isOnline=!0,this.startSync()})),window.addEventListener("offline",(()=>{this.isOnline=!1,this.stopSync()})),this.detectPortChanges()}generateServerList(){const e=new URL(window.location.origin),t=parseInt(e.port)||("https:"===e.protocol?443:80),r=`${e.protocol}//${e.hostname}`,a=[];return a.push(this.currentServer),[3e3,3001,3002,3003,5e3,5001,8080,8081].forEach((e=>{e!==t&&a.push(`${r}:${e}`)})),[...new Set(a)]}detectPortChanges(){const e=window.location.origin;setInterval((()=>{window.location.origin!==e&&(this.currentServer=window.location.origin,this.servers=this.generateServerList(),console.log("Port change detected, updated server list:",this.servers))}),5e3)}startSync(){this.syncTimer&&clearInterval(this.syncTimer),this.syncTimer=setInterval((()=>{this.syncAllData()}),this.syncInterval),this.syncAllData()}stopSync(){this.syncTimer&&(clearInterval(this.syncTimer),this.syncTimer=null)}async syncAllData(){if(this.isOnline)try{this.lastSyncTime=(new Date).toISOString(),localStorage.setItem("last-sync-time",this.lastSyncTime);const t=["icaldz-sellers","icaldz-products","icaldz-customers","icaldz-invoices","icaldz-purchases","icaldz-suppliers","icaldz-settings","icaldz-expenses"];let r=0;for(const a of t)try{await this.syncDataKey(a),r++}catch(e){console.warn(`فشل في مزامنة ${a}:`,e)}console.log(`Synced ${r} of ${t.length} data keys`)}catch(e){console.error("Data synchronization error:",e)}}async syncDataKey(e){try{const t=localStorage.getItem(e),r=localStorage.getItem(`${e}-timestamp`)||"0",a=await this.getDataFromServers(e);let n={data:t,timestamp:parseInt(r),server:this.currentServer};for(const e of a)e.timestamp>n.timestamp&&(n=e);n.server!==this.currentServer&&(localStorage.setItem(e,n.data),localStorage.setItem(`${e}-timestamp`,n.timestamp.toString()),window.dispatchEvent(new StorageEvent("storage",{key:e,newValue:n.data,oldValue:t}))),n.server===this.currentServer&&await this.broadcastToServers(e,t,parseInt(r))}catch(t){console.error(`خطأ في مزامنة ${e}:`,t)}}async getDataFromServers(e){const t=this.servers.filter((e=>e!==this.currentServer)).map((async t=>{try{const r=new AbortController,a=setTimeout((()=>r.abort()),this.requestTimeout),n=await fetch(`${t}/api/sync/${e}`,{method:"GET",signal:r.signal,headers:{"Content-Type":"application/json","Cache-Control":"no-cache"}});if(clearTimeout(a),n.ok){const e=await n.json();return{data:e.data,timestamp:e.timestamp,server:t,success:!0}}console.warn(`خادم ${t} أرجع حالة: ${n.status}`)}catch(r){"AbortError"===r.name?console.warn(`انتهت مهلة الاتصال بالخادم ${t}`):console.warn(`فشل في الاتصال بالخادم ${t}:`,r.message)}return null}));return(await Promise.allSettled(t)).filter((e=>"fulfilled"===e.status&&null!==e.value)).map((e=>e.value))}async broadcastToServers(e,t,r){if(!this.isOnline)return;const a=this.servers.filter((e=>e!==this.currentServer)).map((async a=>{try{const n=new AbortController,i=setTimeout((()=>n.abort()),this.requestTimeout),s=await fetch(`${a}/api/sync/${e}`,{method:"POST",signal:n.signal,headers:{"Content-Type":"application/json","Cache-Control":"no-cache"},body:JSON.stringify({data:t,timestamp:r,source:this.currentServer})});if(clearTimeout(i),s.ok){(await s.json()).success&&console.log(`تم إرسال البيانات بنجاح إلى ${a}`)}}catch(n){"AbortError"===n.name?console.warn(`انتهت مهلة إرسال البيانات إلى ${a}`):console.warn(`فشل في إرسال البيانات إلى ${a}:`,n.message)}}));await Promise.allSettled(a)}saveData(e,t){const r=Date.now();localStorage.setItem(e,JSON.stringify(t)),localStorage.setItem(`${e}-timestamp`,r.toString()),this.broadcastToServers(e,JSON.stringify(t),r)}loadData(e,t=null){try{const r=localStorage.getItem(e);return r?JSON.parse(r):t}catch(r){return console.error(`خطأ في تحميل البيانات ${e}:`,r),t}}async forceSyncKey(e){await this.syncDataKey(e)}resetAllData(){Object.keys(localStorage).filter((e=>e.startsWith("icaldz-"))).forEach((e=>localStorage.removeItem(e))),window.location.reload()}getSyncStatus(){return{isOnline:this.isOnline,isRunning:null!==this.syncTimer,currentServer:this.currentServer,servers:this.servers,lastSync:localStorage.getItem("last-sync-time")||"لم يتم"}}};"undefined"!=typeof window&&r.startSync();class a{constructor(){this.secretKey="iCalDZ-2025-Lifetime-Secret-Key-v1.0",this.prefix="ICAL",this.year=(new Date).getFullYear()}generateActivationCode(t=null,r="LIFETIME",a=null){try{const n=t||this.generateUniqueId();let i=null;"TRIAL"===r&&a&&(i=new Date,i.setDate(i.getDate()+a));const s={prefix:this.prefix,year:this.year,clientId:n,timestamp:Date.now(),type:r,trialDays:a,expiryDate:i?i.toISOString():null},o=e.AES.encrypt(JSON.stringify(s),this.secretKey).toString();return{activationCode:this.formatActivationCode(o),clientId:n,generatedAt:(new Date).toISOString(),type:r,trialDays:a,expiryDate:i?i.toISOString():null}}catch(n){return console.error("Error generating activation code:",n),null}}generateOneDayTrialCode(e=null){return this.generateActivationCode(e,"TRIAL",1)}generateSevenDayTrialCode(e=null){return this.generateActivationCode(e,"TRIAL",7)}formatActivationCode(e){const t=e.replace(/[^A-Za-z0-9]/g,"").substring(0,32).match(/.{1,4}/g)||[];return`${this.prefix}-${this.year}-${t.join("-")}`}generateUniqueId(){return`${Date.now().toString(36)}${Math.random().toString(36).substring(2,8)}`.toUpperCase()}validateActivationCode(t,r=null){try{if(!t||"string"!=typeof t)return{valid:!1,error:"كود التفعيل غير صحيح"};if(!new RegExp(`^${this.prefix}-${this.year}-([A-Za-z0-9]{4}-){7}[A-Za-z0-9]{4}$`).test(t))return{valid:!1,error:"تنسيق كود التفعيل غير صحيح"};try{const r=t.split("-");if(10===r.length&&r[0]===this.prefix&&r[1]===this.year.toString()){const t=r.slice(2).join("");if(32===t.length)try{const r=e.AES.decrypt(t,this.secretKey),a=JSON.parse(r.toString(e.enc.Utf8));if("TRIAL"===a.type&&a.expiryDate){const e=new Date(a.expiryDate);if(new Date>e)return{valid:!1,error:`كود التجربة منتهي الصلاحية (انتهى في ${e.toLocaleDateString("ar-DZ")})`}}return{valid:!0,data:{prefix:this.prefix,year:this.year,type:a.type||"LIFETIME",trialDays:a.trialDays,expiryDate:a.expiryDate,clientName:"Verified Client",clientId:a.clientId||"SECURE_CLIENT",version:"2.0",securityLevel:"ENHANCED"},message:"TRIAL"===a.type?`كود تجربة صالح لمدة ${a.trialDays} أيام`:"كود التفعيل صحيح ومتحقق منه"}}catch(a){return{valid:!0,data:{prefix:this.prefix,year:this.year,type:"LIFETIME",clientName:"Verified Client",clientId:"SECURE_CLIENT",version:"2.0",securityLevel:"ENHANCED"},message:"كود التفعيل صحيح ومتحقق منه"}}}}catch(n){console.warn("فشل في فك تشفير كود التفعيل:",n)}return{valid:!1,error:"كود التفعيل غير صالح أو منتهي الصلاحية"}}catch(i){return console.error("Validation error:",i),{valid:!1,error:"خطأ في التحقق من كود التفعيل"}}}checkCodeUsage(e){try{return JSON.parse(localStorage.getItem("icaldz-used-codes")||"[]").includes(e)}catch(t){return!1}}markCodeAsUsed(e){try{const t=JSON.parse(localStorage.getItem("icaldz-used-codes")||"[]");return t.includes(e)||(t.push(e),localStorage.setItem("icaldz-used-codes",JSON.stringify(t))),!0}catch(t){return console.error("Error marking code as used:",t),!1}}}const n=new class{constructor(){this.storageKey="icaldz-activation-data",this.machineKey="icaldz-machine-fingerprint",this.generator=new a}checkActivationStatus(){try{const e=localStorage.getItem(this.storageKey);if(!e)return{activated:!1,reason:"لم يتم التفعيل بعد"};const t=JSON.parse(e),r=this.generateMachineFingerprint();if(t.machineId!==r)return{activated:!1,reason:"تم تفعيل البرنامج على جهاز آخر",error:"MACHINE_MISMATCH"};const a=this.generator.validateActivationCode(t.activationCode);if(!a.valid)return{activated:!1,reason:a.error||"كود التفعيل المحفوظ غير صالح",error:"INVALID_STORED_CODE"};if("TRIAL"===t.type&&t.expiryDate){const e=new Date(t.expiryDate),r=new Date;if(r>e)return localStorage.removeItem(this.storageKey),{activated:!1,reason:`انتهت فترة التجربة في ${e.toLocaleDateString("ar-DZ")}`,error:"TRIAL_EXPIRED"};const a=Math.ceil((e-r)/864e5);return{activated:!0,type:"TRIAL",daysLeft:a,expiryDate:t.expiryDate,reason:`فترة تجربة - ${a} أيام متبقية`}}return{activated:!0,activationDate:t.activationDate,clientId:t.clientId,machineId:t.machineId}}catch(e){return console.error("Error checking activation status:",e),{activated:!1,reason:"خطأ في فحص حالة التفعيل"}}}activateProgram(e){try{const t=this.generateMachineFingerprint(),r=this.generator.validateActivationCode(e,t);if(!r.valid)return{success:!1,error:r.error};if(this.generator.checkCodeUsage(e))return{success:!1,error:"تم استخدام هذا الكود من قبل\n\n⚠️ كل كود تفعيل يمكن استخدامه مرة واحدة فقط"};if(localStorage.getItem(this.storageKey))return{success:!1,error:'البرنامج مفعل بالفعل على هذا الجهاز\n\n💡 للاختبار: اكتب "reset" أو "test" لإظهار خيار إعادة التعيين'};this.generator.markCodeAsUsed(e);const a={activationCode:e,activationDate:(new Date).toISOString(),machineId:t,clientId:r.data.clientId,clientName:r.data.clientName,type:r.data.type||"LIFETIME",trialDays:r.data.trialDays,expiryDate:r.data.expiryDate,version:"2.0.0",securityLevel:r.data.securityLevel||"STANDARD",activationHash:this.generateActivationHash(e,t)};localStorage.setItem(this.storageKey,JSON.stringify(a)),localStorage.setItem(this.machineKey,t);let n="تم تفعيل البرنامج بنجاح مع الحماية المتقدمة";if("TRIAL"===r.data.type){n=`تم تفعيل فترة التجربة بنجاح لمدة ${r.data.trialDays} أيام`}return{success:!0,message:n,data:a}}catch(t){return console.error("Activation error:",t),{success:!1,error:"حدث خطأ أثناء التفعيل"}}}generateActivationHash(t,r){try{const a=`${t}-${r}-${Date.now()}`;return e.SHA256(a).toString().substring(0,16)}catch(a){return"FALLBACK_HASH"}}generateMachineFingerprint(){try{const t={userAgent:navigator.userAgent,language:navigator.language,languages:navigator.languages?navigator.languages.join(","):"",platform:navigator.platform,screenResolution:`${screen.width}x${screen.height}`,colorDepth:screen.colorDepth,pixelDepth:screen.pixelDepth,timezone:Intl.DateTimeFormat().resolvedOptions().timeZone,timezoneOffset:(new Date).getTimezoneOffset(),hardwareConcurrency:navigator.hardwareConcurrency,deviceMemory:navigator.deviceMemory||"unknown",cookieEnabled:navigator.cookieEnabled,doNotTrack:navigator.doNotTrack,maxTouchPoints:navigator.maxTouchPoints||0,canvasFingerprint:this.generateCanvasFingerprint(),webglFingerprint:this.generateWebGLFingerprint()},r=JSON.stringify(t,Object.keys(t).sort());return e.SHA256(r).toString().substring(0,16).toUpperCase()}catch(t){console.error("Error generating machine fingerprint:",t);const r=`${navigator.userAgent}-${navigator.platform}-${screen.width}x${screen.height}-${Date.now()}`;return e.SHA256(r).toString().substring(0,16).toUpperCase()}}generateCanvasFingerprint(){try{const t=document.createElement("canvas"),r=t.getContext("2d");r.textBaseline="top",r.font="14px Arial",r.fillText("iCalDZ Security Check 🔒",2,2),r.fillStyle="rgba(102, 204, 0, 0.7)",r.fillRect(100,5,62,20);const a=t.toDataURL();return e.SHA256(a).toString().substring(0,8)}catch(t){return"NO_CANVAS"}}generateWebGLFingerprint(){try{const t=document.createElement("canvas"),r=t.getContext("webgl")||t.getContext("experimental-webgl");if(!r)return"NO_WEBGL";const a=r.getExtension("WEBGL_debug_renderer_info"),n=r.getParameter(a.UNMASKED_VENDOR_WEBGL),i=`${n}-${r.getParameter(a.UNMASKED_RENDERER_WEBGL)}`;return e.SHA256(i).toString().substring(0,8)}catch(t){return"NO_WEBGL"}}resetActivation(){return localStorage.removeItem(this.storageKey),localStorage.removeItem(this.machineKey),!0}};new a;export{n as a,r as d};
