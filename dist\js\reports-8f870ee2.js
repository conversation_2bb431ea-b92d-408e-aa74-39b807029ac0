import{g as t,A as e,a as n,u as o}from"./i18n-aa72aa57.js";import{r as a}from"./react-vendor-03c8a839.js";function r(t,e){console.log("🧮 Starting cost calculation...");let n=0,o=0;const a=[],r=[],s=[],l={};e.forEach((t=>{if(t&&t.id){const e=parseFloat(t.buyPrice)||0,n=parseFloat(t.sellPrice||t.price)||0;e>0&&n>0?l[t.id]={id:t.id,name:t.name||t.id,buyPrice:e,sellPrice:n}:r.push({id:t.id,name:t.name||t.id,buyPrice:e,sellPrice:n})}})),console.log(`📦 Valid products: ${Object.keys(l).length}`),console.log(`⚠️ Invalid products: ${r.length}`),t.forEach(((t,e)=>{t&&t.items&&Array.isArray(t.items)?(console.log(`📄 Processing invoice ${t.invoiceNumber||t.id||e}`),t.items.forEach(((r,i)=>{if(!r||!r.productId)return void console.warn(`Item ${i} in invoice ${e} has no product ID`);const d=l[r.productId];if(!d)return a.push({productId:r.productId,invoiceId:t.invoiceNumber||t.id||e,quantity:r.quantity}),void console.warn(`Product ${r.productId} not found`);const c=parseFloat(r.quantity)||0;if(c<=0)return void console.warn(`Invalid quantity ${c} for product ${r.productId}`);const p=d.buyPrice*c;n+=p,o++,s.push({productId:d.id,productName:d.name,buyPrice:d.buyPrice,quantity:c,itemCost:p}),console.log(`✅ ${d.name}: ${c} × ${d.buyPrice} = ${p}`)}))):console.warn(`Invoice ${e} has no valid items`)}));const i={totalCost:parseFloat(n.toFixed(2)),processedItems:o,missingProducts:a,invalidProducts:r,processedDetails:s,summary:{validProducts:Object.keys(l).length,invalidProducts:r.length,missingProducts:a.length,processedItems:o}};return console.log("💰 Cost calculation complete:",i.totalCost),i}function s(t,e,n,o=new Date){console.log("📊 === CLEAN DAILY REPORT GENERATION ===");const a=o.toISOString().split("T")[0];console.log(`📅 Report date: ${a}`);const s=t.filter((t=>{if(!t||!t.date)return!1;return new Date(t.date).toISOString().split("T")[0]===a}));console.log(`📄 Found ${s.length} invoices for ${a}`);const l=s.reduce(((t,e)=>t+(parseFloat(e.finalTotal)||0)),0);console.log(`💰 Total sales: ${l}`);const i=r(s,e),d=i.totalCost,c=function(t,e){console.log(`💸 Calculating expenses for ${e}...`);const n=t.filter((t=>!(!t||!t.date)&&new Date(t.date).toISOString().split("T")[0]===e)),o=n.reduce(((t,e)=>t+(parseFloat(e.amount)||0)),0),a={totalExpenses:parseFloat(o.toFixed(2)),expenseCount:n.length,expenses:n.map((t=>({id:t.id,description:t.description||"No description",amount:parseFloat(t.amount)||0,date:t.date})))};return console.log(`💸 Daily expenses: ${a.totalExpenses} (${a.expenseCount} items)`),a}(n,a),p=c.totalExpenses,u=l-d,g=u-p,m=l>0?g/l*100:0,h={date:a,totalSales:parseFloat(l.toFixed(2)),costOfGoodsSold:parseFloat(d.toFixed(2)),dailyExpenses:parseFloat(p.toFixed(2)),grossProfit:parseFloat(u.toFixed(2)),netProfit:parseFloat(g.toFixed(2)),profitMargin:parseFloat(m.toFixed(2)),invoiceCount:s.length,expenseCount:c.expenseCount,processedItems:i.processedItems,costDetails:i,expenseDetails:c,isValid:s.length>0,hasErrors:i.missingProducts.length>0,warnings:[...i.invalidProducts.map((t=>`Product ${t.name} has invalid prices`)),...i.missingProducts.map((t=>`Product ${t.productId} not found`))]};return console.log("📊 === DAILY REPORT SUMMARY ==="),console.log(`Total Sales: ${h.totalSales}`),console.log(`Cost of Goods Sold: ${h.costOfGoodsSold}`),console.log(`Daily Expenses: ${h.dailyExpenses}`),console.log(`Gross Profit: ${h.grossProfit}`),console.log(`Net Profit: ${h.netProfit}`),console.log(`Profit Margin: ${h.profitMargin}%`),console.log("📊 === END REPORT ==="),h}function l(e,n,o,a,r,l="ar"){console.log("🚀 NEW CLEAN DAILY REPORT SYSTEM STARTING...");const i=(e,n)=>t(e,l)||n,d="ar"===l,c="ar"===l?"ar":"fr"===l?"fr":"en";if(!Array.isArray(e)||!Array.isArray(n)||!Array.isArray(o))return void r(i("invalidDataReload","❌ Invalid data, please reload the page"),"error");const p=s(e,n,o,new Date);console.log("📊 CLEAN REPORT GENERATED:",p);const u=function(t){const e=t.totalSales-t.costOfGoodsSold,n=e-t.dailyExpenses,o=Math.abs(t.grossProfit-e)<.01,a=Math.abs(t.netProfit-n)<.01;return o||console.error(`Gross profit calculation error: Expected ${e}, got ${t.grossProfit}`),a||console.error(`Net profit calculation error: Expected ${n}, got ${t.netProfit}`),o&&a}(p);u||r(i("reportValidationFailed","⚠️ Report calculation validation failed"),"warning"),p.hasErrors&&r(i("missingProductsWarning",`⚠️ Warning: ${p.costDetails.missingProducts.length} missing products`),"warning",5e3),p.warnings.length>0&&console.warn("Report warnings:",p.warnings);const g=`\n    <!DOCTYPE html>\n    <html dir="${d?"rtl":"ltr"}" lang="${c}">\n    <head>\n      <meta charset="UTF-8">\n      <title>${i("dailyReport","التقرير اليومي")} - ${i("accountingSystem","نظام المحاسبي")}</title>\n      <style>\n        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${d?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n        .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n        .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n        .header p { color: #7f8c8d; margin: 5px 0; }\n        .calculation-verification { background: #e8f5e8; border: 2px solid #27ae60; border-radius: 10px; padding: 20px; margin: 20px 0; }\n        .calculation-verification h3 { color: #27ae60; margin: 0 0 15px 0; }\n        .calculation-step { margin: 10px 0; padding: 10px; background: white; border-radius: 5px; }\n        .daily-stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n        .stat-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n        .stat-card h3 { margin: 0 0 10px 0; color: #34495e; }\n        .stat-card .value { font-size: 24px; font-weight: bold; margin-bottom: 5px; }\n        .stat-card.sales .value { color: #3498db; }\n        .stat-card.cogs .value { color: #e74c3c; }\n        .stat-card.gross-profit .value { color: #f39c12; }\n        .stat-card.expenses .value { color: #9b59b6; }\n        .stat-card.net-profit .value { color: #27ae60; }\n        .stat-card.net-profit.negative .value { color: #e74c3c; }\n        .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n        @media print { body { margin: 0; background: white; } .header, .stat-card { box-shadow: none; } }\n      </style>\n    </head>\n    <body>\n      <div class="header">\n        <h1>📊 ${i("dailyReport","التقرير اليومي")} - ${i("newSystem","نظام جديد")}</h1>\n        <p>${i("reportDate","تاريخ التقرير")}: ${new Date(p.date).toLocaleDateString("ar"===c?"ar-DZ":"fr"===c?"fr-FR":"en-US")}</p>\n        <p>${i("accountingSystem","نظام المحاسبي")} - ${i("accurateCalculations","حسابات دقيقة 100%")}</p>\n      </div>\n\n      <div class="daily-stats">\n        <div class="stat-card sales">\n          <h3>${i("totalSales","إجمالي المبيعات")}</h3>\n          <div class="value">${a(p.totalSales)}</div>\n          <small>${p.invoiceCount} ${i("invoice","فاتورة")}</small>\n        </div>\n        <div class="stat-card cogs">\n          <h3>${i("costOfGoodsSold","تكلفة البضاعة المباعة")}</h3>\n          <div class="value">${a(p.costOfGoodsSold)}</div>\n          <small>${p.processedItems} ${i("item","عنصر")}</small>\n        </div>\n        <div class="stat-card gross-profit">\n          <h3>${i("grossProfit","الربح الإجمالي")}</h3>\n          <div class="value">${a(p.grossProfit)}</div>\n          <small>${i("beforeExpenses","قبل المصاريف")}</small>\n        </div>\n        <div class="stat-card expenses">\n          <h3>${i("operatingExpenses","المصاريف التشغيلية")}</h3>\n          <div class="value">${a(p.dailyExpenses)}</div>\n          <small>${p.expenseCount} ${i("expense","مصروف")}</small>\n        </div>\n        <div class="stat-card net-profit ${p.netProfit<0?"negative":""}">\n          <h3>${i("dailyNetProfit","صافي الربح اليومي")}</h3>\n          <div class="value">${a(p.netProfit)}</div>\n          <small>${p.netProfit>=0?i("profit","ربح"):i("loss","خسارة")}</small>\n        </div>\n        <div class="stat-card">\n          <h3>${i("profitMargin","هامش الربح")}</h3>\n          <div class="value">${p.profitMargin}%</div>\n          <small>${i("fromSales","من المبيعات")}</small>\n        </div>\n      </div>\n\n      <div class="footer">\n        <p>${i("reportGeneratedBy","تم إنشاء هذا التقرير بواسطة النظام الجديد المحسن - حسابات دقيقة 100%")}</p>\n        <p><strong>Developed by iCode DZ</strong></p>\n        <p><strong>📞 0551930589</strong></p>\n        <p>${i("allRightsReserved","© 2025 iCode DZ - جميع الحقوق محفوظة")}</p>\n        <p><small>${i("processedItems","تم معالجة")} ${p.processedItems} ${i("item","عنصر")} ${i("from","من")} ${p.invoiceCount} ${i("invoice","فاتورة")}</small></p>\n      </div>\n    </body>\n    </html>\n  `,m=window.open("","_blank","width=1200,height=800");m.document.write(g),m.document.close(),m.onload=()=>setTimeout((()=>m.print()),1e3);r(p.hasErrors||p.warnings.length>0?i("dailyReportOpenedWithWarnings","📊 تم فتح التقرير اليومي الجديد مع تحذيرات - يرجى مراجعة البيانات"):i("dailyReportOpenedSuccessfully","📊 تم فتح التقرير اليومي الجديد بنجاح - حسابات دقيقة 100%"),p.hasErrors?"warning":"success",3e3)}function i(n,o,a,s,l,i="ar"){console.log("🚀 NEW CLEAN ANNUAL REPORT SYSTEM STARTING...");const d=(e,n)=>t(e,i)||n,c="ar"===i,p="ar"===i?"ar":"fr"===i?"fr":"en",u=(new Date).getFullYear(),g=n.filter((t=>new Date(t.date).getFullYear()===u)),m=a.filter((t=>new Date(t.date).getFullYear()===u)),h=g.reduce(((t,e)=>t+(parseFloat(e.finalTotal)||0)),0),f=r(g,o),$=f.totalCost,v=m.reduce(((t,e)=>t+(parseFloat(e.amount)||0)),0),y=h-$-v,x=h>0?y/h*100:0,b={};for(let t=0;t<12;t++){const e=g.filter((e=>new Date(e.date).getMonth()===t)),n=m.filter((e=>new Date(e.date).getMonth()===t)),a=e.reduce(((t,e)=>t+(parseFloat(e.finalTotal)||0)),0),s=r(e,o).totalCost,l=n.reduce(((t,e)=>t+(parseFloat(e.amount)||0)),0);b[t]={sales:a,costs:s,expenses:l,grossProfit:a-s,netProfit:a-s-l,invoiceCount:e.length}}const w=`\n    <!DOCTYPE html>\n    <html dir="${c?"rtl":"ltr"}" lang="${p}">\n    <head>\n      <meta charset="UTF-8">\n      <title>${d("annualReport","التقرير السنوي")} - ${d("accountingSystem","نظام المحاسبي")}</title>\n      <style>\n        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${c?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n        .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n        .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n        .annual-summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n        .summary-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n        .summary-card h3 { margin: 0 0 10px 0; color: #34495e; }\n        .summary-card .value { font-size: 28px; font-weight: bold; margin-bottom: 5px; }\n        .summary-card.sales .value { color: #3498db; }\n        .summary-card.profit .value { color: #27ae60; }\n        .summary-card.profit.negative .value { color: #e74c3c; }\n        .monthly-table { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 30px; }\n        table { width: 100%; border-collapse: collapse; direction: ${c?"rtl":"ltr"}; }\n        th { background: #34495e; color: white; padding: 15px; text-align: center; font-weight: bold; }\n        td { padding: 12px; text-align: center; border-bottom: 1px solid #ecf0f1; }\n        tr:nth-child(even) { background: #f8f9fa; }\n        .positive { color: #27ae60; font-weight: bold; }\n        .negative { color: #e74c3c; font-weight: bold; }\n        .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n      </style>\n    </head>\n    <body class="lang-${i}">\n      <div class="header">\n        <h1>📊 ${d("annualReport","التقرير السنوي")} ${u}</h1>\n        <p>${d("annualOperationsSummary","ملخص العمليات السنوية")} - ${d("improvedNewSystem","نظام جديد محسن")}</p>\n        <p>${d("accurateCalculations","حسابات دقيقة 100%")} - ${d("noErrors","بدون أخطاء")}</p>\n      </div>\n\n      <div class="annual-summary">\n        <div class="summary-card sales">\n          <h3>${d("totalSales","إجمالي المبيعات")}</h3>\n          <div class="value">${s(h)}</div>\n          <small>${g.length} ${d("invoice","فاتورة")}</small>\n        </div>\n        <div class="summary-card">\n          <h3>${d("costOfGoodsSold","تكلفة البضاعة المباعة")}</h3>\n          <div class="value" style="color: #e74c3c;">${s($)}</div>\n          <small>${f.processedItems} ${d("item","عنصر")}</small>\n        </div>\n        <div class="summary-card">\n          <h3>${d("totalExpenses","إجمالي المصاريف")}</h3>\n          <div class="value" style="color: #9b59b6;">${s(v)}</div>\n          <small>${m.length} ${d("expense","مصروف")}</small>\n        </div>\n        <div class="summary-card profit ${y<0?"negative":""}">\n          <h3>${d("annualNetProfit","صافي الربح السنوي")}</h3>\n          <div class="value">${s(y)}</div>\n          <small>${d("margin","هامش")} ${x.toFixed(1)}%</small>\n        </div>\n      </div>\n\n      <div class="monthly-table">\n        <h3 style="padding: 20px; margin: 0; background: #34495e; color: white;">📅 ${d("monthlyPerformance","الأداء الشهري")}</h3>\n        <table>\n          <thead>\n            <tr>\n              <th>${d("month","الشهر")}</th>\n              <th>${d("sales","المبيعات")}</th>\n              <th>${d("cost","التكلفة")}</th>\n              <th>${d("expenses","المصاريف")}</th>\n              <th>${d("netProfit","صافي الربح")}</th>\n              <th>${d("invoiceCount","عدد الفواتير")}</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${Object.entries(b).map((([t,n])=>`\n                <tr>\n                  <td>${d(`month${parseInt(t)+1}`,e[parseInt(t)])}</td>\n                  <td>${s(n.sales)}</td>\n                  <td>${s(n.costs)}</td>\n                  <td>${s(n.expenses)}</td>\n                  <td class="${n.netProfit>=0?"positive":"negative"}">${s(n.netProfit)}</td>\n                  <td>${n.invoiceCount}</td>\n                </tr>\n              `)).join("")}\n          </tbody>\n        </table>\n      </div>\n\n      <div class="footer">\n        <p>${d("reportGeneratedBy","تم إنشاء هذا التقرير بواسطة النظام الجديد المحسن - حسابات دقيقة 100%")}</p>\n        <p><strong>Developed by iCode DZ</strong></p>\n        <p><strong>📞 0551930589</strong></p>\n        <p>${d("allRightsReserved","© 2025 iCode DZ - جميع الحقوق محفوظة")}</p>\n      </div>\n    </body>\n    </html>\n  `,P=window.open("","_blank","width=1200,height=800");P.document.write(w),P.document.close(),P.onload=()=>setTimeout((()=>P.print()),1e3),l(d("annualReportOpenedSuccessfully","📊 تم فتح التقرير السنوي الجديد بنجاح - حسابات دقيقة 100%"),"success",3e3)}function d(e,o,a,s,l,i="ar"){console.log("🚀 NEW CLEAN MONTHLY REPORT SYSTEM STARTING...");const d=(e,n)=>t(e,i)||n,c="ar"===i,p="ar"===i?"ar":"fr"===i?"fr":"en",u=new Date,g=u.getMonth(),m=u.getFullYear(),h=e.filter((t=>{const e=new Date(t.date);return e.getMonth()===g&&e.getFullYear()===m})),f=a.filter((t=>{const e=new Date(t.date);return e.getMonth()===g&&e.getFullYear()===m})),$=h.reduce(((t,e)=>t+(parseFloat(e.finalTotal)||0)),0),v=r(h,o),y=v.totalCost,x=f.reduce(((t,e)=>t+(parseFloat(e.amount)||0)),0),b=$-y-x,w=$>0?b/$*100:0,P=new Date(m,g+1,0).getDate(),S={};for(let t=1;t<=P;t++){const e=`${m}-${String(g+1).padStart(2,"0")}-${String(t).padStart(2,"0")}`,n=h.filter((t=>t.date===e)),a=f.filter((t=>t.date===e)),s=n.reduce(((t,e)=>t+(parseFloat(e.finalTotal)||0)),0),l=r(n,o).totalCost,i=a.reduce(((t,e)=>t+(parseFloat(e.amount)||0)),0);S[e]={sales:s,costs:l,expenses:i,grossProfit:s-l,netProfit:s-l-i,invoiceCount:n.length}}const D=d(`month${g+1}`,n(g)),R=`\n    <!DOCTYPE html>\n    <html dir="${c?"rtl":"ltr"}" lang="${p}">\n    <head>\n      <meta charset="UTF-8">\n      <title>${d("monthlyReport","التقرير الشهري")} - ${d("accountingSystem","نظام المحاسبي")}</title>\n      <style>\n        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: ${c?"rtl":"ltr"}; margin: 20px; background: #f5f5f5; }\n        .header { text-align: center; margin-bottom: 30px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n        .header h1 { color: #2c3e50; margin: 0; font-size: 28px; }\n        .monthly-summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n        .summary-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); text-align: center; }\n        .summary-card h3 { margin: 0 0 10px 0; color: #34495e; }\n        .summary-card .value { font-size: 28px; font-weight: bold; margin-bottom: 5px; }\n        .summary-card.sales .value { color: #3498db; }\n        .summary-card.profit .value { color: #27ae60; }\n        .summary-card.profit.negative .value { color: #e74c3c; }\n        .daily-table { background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 30px; }\n        table { width: 100%; border-collapse: collapse; direction: ${c?"rtl":"ltr"}; }\n        th { background: #34495e; color: white; padding: 15px; text-align: center; font-weight: bold; }\n        td { padding: 12px; text-align: center; border-bottom: 1px solid #ecf0f1; }\n        tr:nth-child(even) { background: #f8f9fa; }\n        .positive { color: #27ae60; font-weight: bold; }\n        .negative { color: #e74c3c; font-weight: bold; }\n        .footer { text-align: center; margin-top: 30px; color: #7f8c8d; }\n      </style>\n    </head>\n    <body class="lang-${i}">\n      <div class="header">\n        <h1>📅 ${d("monthlyReport","التقرير الشهري")} ${D} ${m}</h1>\n        <p>${d("monthlyOperationsSummary","ملخص العمليات الشهرية")} - ${d("improvedNewSystem","نظام جديد محسن")}</p>\n        <p>${d("accurateCalculations","حسابات دقيقة 100%")} - ${d("noErrors","بدون أخطاء")}</p>\n      </div>\n\n      <div class="monthly-summary">\n        <div class="summary-card sales">\n          <h3>${d("totalSales","إجمالي المبيعات")}</h3>\n          <div class="value">${s($)}</div>\n          <small>${h.length} ${d("invoice","فاتورة")}</small>\n        </div>\n        <div class="summary-card">\n          <h3>${d("costOfGoodsSold","تكلفة البضاعة المباعة")}</h3>\n          <div class="value" style="color: #e74c3c;">${s(y)}</div>\n          <small>${v.processedItems} ${d("item","عنصر")}</small>\n        </div>\n        <div class="summary-card">\n          <h3>${d("totalExpenses","إجمالي المصاريف")}</h3>\n          <div class="value" style="color: #9b59b6;">${s(x)}</div>\n          <small>${f.length} ${d("expense","مصروف")}</small>\n        </div>\n        <div class="summary-card profit ${b<0?"negative":""}">\n          <h3>${d("monthlyNetProfit","صافي الربح الشهري")}</h3>\n          <div class="value">${s(b)}</div>\n          <small>${d("margin","هامش")} ${w.toFixed(1)}%</small>\n        </div>\n      </div>\n\n      <div class="daily-table">\n        <h3 style="padding: 20px; margin: 0; background: #34495e; color: white;">📊 ${d("dailyPerformanceForMonth","الأداء اليومي للشهر")}</h3>\n        <table>\n          <thead>\n            <tr>\n              <th>${d("date","التاريخ")}</th>\n              <th>${d("sales","المبيعات")}</th>\n              <th>${d("cost","التكلفة")}</th>\n              <th>${d("expenses","المصاريف")}</th>\n              <th>${d("netProfit","صافي الربح")}</th>\n              <th>${d("invoiceCount","عدد الفواتير")}</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${Object.entries(S).map((([t,e])=>`\n                <tr>\n                  <td>${new Date(t).getDate()} ${D}</td>\n                  <td>${s(e.sales)}</td>\n                  <td>${s(e.costs)}</td>\n                  <td>${s(e.expenses)}</td>\n                  <td class="${e.netProfit>=0?"positive":"negative"}">${s(e.netProfit)}</td>\n                  <td>${e.invoiceCount}</td>\n                </tr>\n              `)).join("")}\n          </tbody>\n        </table>\n      </div>\n\n      <div class="footer">\n        <p>${d("reportGeneratedBy","تم إنشاء هذا التقرير بواسطة النظام الجديد المحسن - حسابات دقيقة 100%")}</p>\n        <p><strong>Developed by iCode DZ</strong></p>\n        <p><strong>📞 0551930589</strong></p>\n        <p>${d("allRightsReserved","© 2025 iCode DZ - جميع الحقوق محفوظة")}</p>\n      </div>\n    </body>\n    </html>\n  `,F=window.open("","_blank","width=1200,height=800");F.document.write(R),F.document.close(),F.onload=()=>setTimeout((()=>F.print()),1e3),l(d("monthlyReportOpenedSuccessfully","📅 تم فتح التقرير الشهري الجديد بنجاح - حسابات دقيقة 100%"),"success",3e3)}const c=({savedInvoices:t,products:e,expenses:n,formatPrice:r,showToast:s})=>{const{t:l,currentLanguage:i}=o(),[d,c]=a.useState((new Date).toISOString().split("T")[0]),[p,u]=a.useState((new Date).toISOString().slice(0,7)),[g,m]=a.useState("daily"),[h,f]=a.useState(null),$=()=>{"daily"===g?v():"monthly"===g&&y()},v=()=>{console.log("📊 Generating daily report for:",d);const o=t.filter((t=>new Date(t.date).toISOString().split("T")[0]===d)),a=o.reduce(((t,e)=>t+(parseFloat(e.finalTotal)||0)),0),r={};let c=0;o.forEach((t=>{t.items.forEach((t=>{const n=e.find((e=>e.id===t.productId));if(n){const e=t.productId;r[e]||(r[e]={id:n.id,name:n.name,category:n.category,buyPrice:n.buyPrice||0,sellPrice:n.sellPrice||n.price||0,totalQuantity:0,totalRevenue:0,totalCost:0,profit:0}),r[e].totalQuantity+=t.quantity,r[e].totalRevenue+=t.total,r[e].totalCost+=(n.buyPrice||0)*t.quantity,r[e].profit=r[e].totalRevenue-r[e].totalCost,c+=(n.buyPrice||0)*t.quantity}}))}));const p=Object.values(r).sort(((t,e)=>e.totalRevenue-t.totalRevenue)),u=n.filter((t=>new Date(t.date).toISOString().split("T")[0]===d)),g=u.reduce(((t,e)=>t+(parseFloat(e.amount)||0)),0),m=a-c,h=m-g;f({type:"daily",date:d,totalSales:a,totalCost:c,totalExpenses:g,grossProfit:m,netProfit:h,profitMargin:a>0?h/a*100:0,invoicesCount:o.length,soldProducts:p,expenses:u}),s(l("dailyReportGenerated",`📊 تم إنشاء التقرير اليومي لتاريخ ${new Date(d).toLocaleDateString("ar"===i?"ar-DZ":"fr"===i?"fr-FR":"en-US")}`),"success",3e3)},y=()=>{console.log("📊 Generating monthly report for:",p);const[o,a]=p.split("-"),r=t.filter((t=>{const e=new Date(t.date);return e.getFullYear()===parseInt(o)&&e.getMonth()===parseInt(a)-1})),i=r.reduce(((t,e)=>t+(parseFloat(e.finalTotal)||0)),0),d={};let c=0;r.forEach((t=>{t.items.forEach((t=>{const n=e.find((e=>e.id===t.productId));if(n){const e=t.productId;d[e]||(d[e]={id:n.id,name:n.name,category:n.category,buyPrice:n.buyPrice||0,sellPrice:n.sellPrice||n.price||0,totalQuantity:0,totalRevenue:0,totalCost:0,profit:0}),d[e].totalQuantity+=t.quantity,d[e].totalRevenue+=t.total,d[e].totalCost+=(n.buyPrice||0)*t.quantity,d[e].profit=d[e].totalRevenue-d[e].totalCost,c+=(n.buyPrice||0)*t.quantity}}))}));const u=Object.values(d).sort(((t,e)=>e.totalRevenue-t.totalRevenue)),g=n.filter((t=>{const e=new Date(t.date);return e.getFullYear()===parseInt(o)&&e.getMonth()===parseInt(a)-1})),m=g.reduce(((t,e)=>t+(parseFloat(e.amount)||0)),0),h=i-c,$=h-m;f({type:"monthly",date:p,totalSales:i,totalCost:c,totalExpenses:m,grossProfit:h,netProfit:$,profitMargin:i>0?$/i*100:0,invoicesCount:r.length,soldProducts:u,expenses:g});const v=`month${parseInt(a)}`,y=l(v,["جانفي","فيفري","مارس","أفريل","ماي","جوان","جويلية","أوت","سبتمبر","أكتوبر","نوفمبر","ديسمبر"][parseInt(a)-1]);s(l("monthlyReportGenerated",`📊 تم إنشاء التقرير الشهري لشهر ${y} ${o}`),"success",3e3)};return a.useEffect((()=>{t.length>0&&$()}),[d,p,g]),{selectedDate:d,setSelectedDate:c,selectedMonth:p,setSelectedMonth:u,reportType:g,setReportType:m,reportData:h,generateReport:$,printReport:()=>{if(!h)return void s(l("generateReportFirst","⚠️ يرجى إنشاء التقرير أولاً"),"warning",3e3);const t="daily"===h.type?`${l("dailyReport","التقرير اليومي")} - ${new Date(h.date).toLocaleDateString("ar"===i?"ar-DZ":"fr"===i?"fr-FR":"en-US")}`:`${l("monthlyReport","التقرير الشهري")} - ${h.date}`,e="ar"===i,n="ar"===i?"ar":"fr"===i?"fr":"en",o=`\n      <!DOCTYPE html>\n      <html dir="${e?"rtl":"ltr"}" lang="${n}">\n      <head>\n        <meta charset="UTF-8">\n        <title>${t}</title>\n        <style>\n          body { font-family: Arial, sans-serif; direction: ${e?"rtl":"ltr"}; margin: 20px; }\n          .header { text-align: center; margin-bottom: 30px; }\n          .summary { display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px; margin-bottom: 30px; }\n          .summary-card { border: 1px solid #ddd; padding: 15px; border-radius: 8px; }\n          .products-table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }\n          .products-table th, .products-table td { border: 1px solid #ddd; padding: 8px; text-align: center; }\n          .products-table th { background-color: #f5f5f5; }\n          .best-product { background-color: #e8f5e8; }\n          @media print { body { margin: 0; } }\n        </style>\n      </head>\n      <body>\n        <div class="header">\n          <h1>${t}</h1>\n          <p>${l("creationDate","تاريخ الإنشاء")}: ${(new Date).toLocaleDateString("ar"===n?"ar-DZ":"fr"===n?"fr-FR":"en-US")}</p>\n        </div>\n\n        <div class="summary">\n          <div class="summary-card">\n            <h3>${l("totalSales","إجمالي المبيعات")}</h3>\n            <p>${r(h.totalSales)}</p>\n          </div>\n          <div class="summary-card">\n            <h3>${l("netProfit","صافي الربح")}</h3>\n            <p>${r(h.netProfit)}</p>\n          </div>\n          <div class="summary-card">\n            <h3>${l("profitMargin","هامش الربح")}</h3>\n            <p>${h.profitMargin.toFixed(2)}%</p>\n          </div>\n        </div>\n\n        <h2>${l("soldProductsDetails","تفاصيل المنتجات المباعة")}</h2>\n        <table class="products-table">\n          <thead>\n            <tr>\n              <th>${l("product","المنتج")}</th>\n              <th>${l("category","الفئة")}</th>\n              <th>${l("quantitySold","الكمية المباعة")}</th>\n              <th>${l("totalRevenue","إجمالي الإيرادات")}</th>\n              <th>${l("totalCost","إجمالي التكلفة")}</th>\n              <th>${l("profit","الربح")}</th>\n              <th>${l("profitMargin","هامش الربح")}</th>\n            </tr>\n          </thead>\n          <tbody>\n            ${h.soldProducts.map(((t,e)=>`\n              <tr class="${0===e?"best-product":""}">\n                <td>${t.name}</td>\n                <td>${t.category}</td>\n                <td>${t.totalQuantity}</td>\n                <td>${r(t.totalRevenue)}</td>\n                <td>${r(t.totalCost)}</td>\n                <td>${r(t.profit)}</td>\n                <td>${t.totalRevenue>0?(t.profit/t.totalRevenue*100).toFixed(2):0}%</td>\n              </tr>\n            `)).join("")}\n          </tbody>\n        </table>\n\n        <div class="footer" style="text-align: center; margin-top: 30px; color: #7f8c8d;">\n          <p>${l("reportGeneratedBy","تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ")}</p>\n          <p><strong>Developed by iCode DZ</strong></p>\n          <p><strong>📞 0551930589</strong></p>\n          <p>${l("allRightsReserved","© 2025 iCode DZ - جميع الحقوق محفوظة")}</p>\n        </div>\n      </body>\n      </html>\n    `,a=window.open("","_blank","width=1200,height=800");a.document.write(o),a.document.close(),a.onload=()=>setTimeout((()=>a.print()),1e3),s(l("reportOpenedForPrint","🖨️ تم فتح التقرير للطباعة"),"success",3e3)}}};export{c as A,d as a,i as b,r as c,l as g};
